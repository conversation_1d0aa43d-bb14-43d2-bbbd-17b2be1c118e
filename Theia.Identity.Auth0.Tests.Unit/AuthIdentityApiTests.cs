using Auth0.Core.Exceptions;
using Auth0.ManagementApi;
using Auth0.ManagementApi.Models;
using Auth0.ManagementApi.Paging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;

namespace Theia.Identity.Auth0.Tests.Unit;

public class AuthIdentityApiTests
{
    private readonly IManagementApiClient managementApiClient = Substitute.For<IManagementApiClient>();
    private readonly IRateLimitExceptionHandler rateLimitExceptionHandler = Substitute.For<IRateLimitExceptionHandler>();
    
    private readonly IdentityApi sut;
    
    public AuthIdentityApiTests()
    {
        sut = new IdentityApi(new Auth0ApiOptions(), managementApiClient, rateLimitExceptionHandler);
    }

    [Fact]
    public async Task GetAllRolesPermissionsAsync_RateLimitException_HandlesRetries()
    {
        // Arrange
        managementApiClient.Roles
            .GetPermissionsAsync(Arg.Any<string>(), Arg.Any<PaginationInfo>(), Arg.Any<CancellationToken>())
            .ThrowsAsync(new RateLimitApiException(new RateLimit()));

        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => 
                    await sut.GetAllRolesPermissionsAsync(string.Empty, CancellationToken.None));

        // Assert
        Assert.IsType<RateLimitApiException>(exception);
        await rateLimitExceptionHandler
            .Received(5)
            .HandleAsync(Arg.Any<RateLimitApiException>(), Arg.Any<CancellationToken>());
    }
    
    [Fact]
    public async Task RemovePermissionsFromRoleAsync_RateLimitException_HandlesRetries()
    {
        // Arrange
        managementApiClient.Roles
            .RemovePermissionsAsync(Arg.Any<string>(), Arg.Any<AssignPermissionsRequest>(), Arg.Any<CancellationToken>())
            .ThrowsAsync(new RateLimitApiException(new RateLimit()));

        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => 
                    await sut.RemovePermissionsFromRoleAsync(string.Empty, [], CancellationToken.None));

        // Assert
        Assert.IsType<RateLimitApiException>(exception);
        await rateLimitExceptionHandler
            .Received(5)
            .HandleAsync(Arg.Any<RateLimitApiException>(), Arg.Any<CancellationToken>());
    }
    
    [Fact]
    public async Task AssignRolePermissionsAsync_RateLimitException_HandlesRetries()
    {
        // Arrange
        managementApiClient.Roles
            .AssignPermissionsAsync(Arg.Any<string>(), Arg.Any<AssignPermissionsRequest>(), Arg.Any<CancellationToken>())
            .ThrowsAsync(new RateLimitApiException(new RateLimit()));

        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => 
                    await sut.AssignRolePermissionsAsync(string.Empty, [], CancellationToken.None));

        // Assert
        Assert.IsType<RateLimitApiException>(exception);
        await rateLimitExceptionHandler
            .Received(5)
            .HandleAsync(Arg.Any<RateLimitApiException>(), Arg.Any<CancellationToken>());
    }
}