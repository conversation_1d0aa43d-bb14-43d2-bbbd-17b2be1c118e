using AutoWrapper.Wrappers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using MockQueryable.NSubstitute;
using NSubstitute;
using Theia.Application.Common.Interfaces.Persistence;
using Theia.Application.Common.Interfaces.Services;
using Theia.Application.Services;
using Theia.Domain.Common.Enums;
using Theia.Domain.Entities;
using Theia.Domain.Entities.BrokingHouses;
using Theia.Domain.Entities.Organisations;

namespace Theia.Application.Tests.Unit.Tests.Services;

public class BrokingHousePermissionServiceTests
{
    private readonly IApplicationDbContext dbContext = Substitute.For<IApplicationDbContext>();
    private readonly ITenantResolverService tenantResolverService = Substitute.For<ITenantResolverService>();
    private readonly IUserService userService = Substitute.For<IUserService>();
    private readonly BrokingHousePermissionService sut;
    
    private static readonly Guid ValidTenantId = Guid.NewGuid();
    private static readonly Guid ValidBrokingHouseId = Guid.NewGuid();
    private static readonly Guid ValidOrgId = Guid.NewGuid();
    
    public BrokingHousePermissionServiceTests()
    {
        DbSet<BrokingHouse> emptyBrokingHouseDbSet = Enumerable.Empty<BrokingHouse>().BuildMockDbSet();
        dbContext.BrokingHouses.Returns(emptyBrokingHouseDbSet);
        
        DbSet<OrgBrokingAssociation> emptyAssociationsSet = Enumerable.Empty<OrgBrokingAssociation>().BuildMockDbSet();
        dbContext.OrgBrokingAssociations.Returns(emptyAssociationsSet);

        tenantResolverService.GetTenantId().Returns(ValidTenantId);
        
        sut = new BrokingHousePermissionService(dbContext, tenantResolverService, userService);
    }
    
    [Fact]
    public async Task EnsureCurrentBrokerCanSeeOrganisationAsync_TenantIsNull_Throw400()
    {
        // Arrange
        tenantResolverService.GetTenantId().Returns((Guid?)null);
        
        // Act
        Exception? exception = await Record.ExceptionAsync(async () =>
            await sut.EnsureCurrentBrokerCanSeeOrganisationAsync(Guid.Empty, CancellationToken.None));

        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status400BadRequest, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task EnsureCurrentBrokerCanSeeOrganisationAsync_BrokingHouseIsNull_Throw400()
    {
        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => await sut.EnsureCurrentBrokerCanSeeOrganisationAsync(Guid.Empty, CancellationToken.None)); 
        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task EnsureCurrentBrokerCanSeeOrganisationAsync_NoOrgBrokingAssociation_Throw403()
    {
        // Arrange
        DbSet<BrokingHouse> dbSet =
            new BrokingHouse[]
            {
                new()
                {
                    Id = ValidBrokingHouseId,
                    Name = string.Empty,
                    ContactName = string.Empty,
                    Email = string.Empty,
                    Phone = string.Empty,
                    Active = default,
                    Tenant = new Tenant
                    {
                        Id = ValidTenantId, 
                        Name = string.Empty, 
                        Type = TenantType.None
                    }
                }
            }.BuildMockDbSet();
        dbContext.BrokingHouses.Returns(dbSet);
        
        DbSet<Organisation> organisationsDbSet =
            new Organisation[]
            {
                new ()
                {
                    Id = ValidOrgId,
                    Name = null,
                    ContactName = null,
                    ContactEmail = null,
                    ContactPhoneNumber = null
                }
            }.BuildMockDbSet();
        dbContext.Organisations.Returns(organisationsDbSet);
        
        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => await sut.EnsureCurrentBrokerCanSeeOrganisationAsync(Guid.Empty, CancellationToken.None)); 
        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status403Forbidden, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task EnsureCurrentBrokerCanSeeOrganisationAsync_OrgBrokingAssociationExists_DontThrow()
    {
        // Arrange
        DbSet<BrokingHouse> brokingHousesDbSet =
            new BrokingHouse[]
            {
                new()
                {
                    Id = ValidBrokingHouseId,
                    Name = string.Empty,
                    ContactName = string.Empty,
                    Email = string.Empty,
                    Phone = string.Empty,
                    Active = default,
                    Tenant = new Tenant
                    {
                        Id = ValidTenantId, 
                        Name = string.Empty, 
                        Type = TenantType.None
                    }
                }
            }.BuildMockDbSet();
        dbContext.BrokingHouses.Returns(brokingHousesDbSet);

        DbSet<Organisation> organisationsDbSet =
            new Organisation[]
            {
                new ()
                {
                    Id = ValidOrgId,
                    BrokingAssociations =
                    [
                        new OrgBrokingAssociation
                        {
                            OrgId = ValidOrgId,
                            Organisation = null,
                            BrokingId = ValidBrokingHouseId,
                            BrokingHouse = null
                        }
                    ],
                    Name = null,
                    ContactName = null,
                    ContactEmail = null,
                    ContactPhoneNumber = null
                }
            }.BuildMockDbSet();
        dbContext.Organisations.Returns(organisationsDbSet);
        
        // Act
        Exception? exception = 
            await Record.ExceptionAsync(async () =>
                await sut.EnsureCurrentBrokerCanSeeOrganisationAsync(ValidOrgId, CancellationToken.None)); 
        
        // Assert
        Assert.Null(exception);
    }
}