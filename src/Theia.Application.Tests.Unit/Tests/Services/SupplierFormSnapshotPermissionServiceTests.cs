using AutoWrapper.Wrappers;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Time.Testing;
using MockQueryable.NSubstitute;
using NSubstitute;
using Theia.Application.Common.Interfaces.Persistence;
using Theia.Application.Common.Interfaces.Services;
using Theia.Application.Services;
using Theia.Application.Tests.Unit.Builders.ApplicationForms;
using Theia.Application.Tests.Unit.Builders.Organisation;
using Theia.Application.Tests.Unit.Builders.Submissions;
using Theia.Application.Tests.Unit.Builders.Submissions.BrokerSubmissions;
using Theia.Application.Tests.Unit.Builders.Submissions.SupplierSubmissions;
using Theia.Application.Tests.Unit.Builders.Supplier;
using Theia.BackendResources;
using Theia.Domain.Common.Enums;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Indications;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;
using Theia.Domain.Entities.Organisations.Submissions.Organisation;
using Theia.Domain.Entities.Organisations.Submissions.Suppliers;
using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.ApplicationForms;
using Theia.Domain.Entities.Suppliers.Submissions;

namespace Theia.Application.Tests.Unit.Tests.Services;

public class SupplierFormSnapshotPermissionServiceTests
{
    private readonly IApplicationDbContext dbContext = Substitute.For<IApplicationDbContext>();
    private readonly IUserService userService = Substitute.For<IUserService>();
    private readonly ITenantResolverService tenantResolverService = Substitute.For<ITenantResolverService>();
    private readonly IRoleService roleService = Substitute.For<IRoleService>();
    private readonly FakeTimeProvider timeProvider = new();
    private readonly SupplierFormSnapshotPermissionService sut;

    private readonly string validUserId = Guid.NewGuid().ToString();
    private readonly Guid validTenantId = Guid.NewGuid();
    
    public SupplierFormSnapshotPermissionServiceTests()
    {
        userService.GetAuthId().Returns(validUserId);
        tenantResolverService.GetTenantId().Returns(validTenantId);
        
        sut = new SupplierFormSnapshotPermissionService(dbContext, userService, tenantResolverService, roleService);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_TenantIdIsNull_Throws403()
    {
        // Arrange
        tenantResolverService.GetTenantId().Returns((Guid?)null);
        
        // Act
        Exception? exception = 
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.Empty, CancellationToken.None));
        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status403Forbidden, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_TenantTypeIsDefault_Throws400()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(default(TenantType));
    
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.Empty, CancellationToken.None));
    
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status400BadRequest, apiProblemDetailsException.StatusCode);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_UserAuthIdIsNull_Throws400()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Admin);
        userService.GetAuthId().Returns((string?)null);
    
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.Empty, CancellationToken.None));
    
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status400BadRequest, apiProblemDetailsException.StatusCode);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_SupplierUserSupplierIsNull_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Supplier);

        DbSet<ApplicationUser> users =
            new ApplicationUser[] {new()
                {
                    Id = Guid.NewGuid()
                        .ToString(),
                    Email = string.Empty
                }
            }.BuildMockDbSet();
        dbContext.Users.Returns(users);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.Empty, CancellationToken.None));
        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_SnapshotExistsForOtherUser_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Supplier);
        
        Guid snapshotId = Guid.NewGuid();

        IQueryable<ApplicationUser> users = new List<ApplicationUser>
        {
            new()
            {
                Id = Guid.NewGuid()
                    .ToString(),
                Email = string.Empty,
                Supplier = new Supplier
                {
                    CompanyNumber = string.Empty,
                    Name = string.Empty,
                    ContactEmail = string.Empty,
                    Status = default,
                    ApplicationFormVersions =
                    [
                        new SupplierApplicationFormVersion
                        {
                            SupplierId = Guid.Empty,
                            Supplier = null,
                            ApplicationFormVersionId = Guid.Empty,
                            ApplicationFormVersion = null,
                            Snapshots =
                            [
                                new SupplierApplicationFormVersionSnapshot
                                {
                                    Id = snapshotId,
                                    CreatedOnUtc = default,
                                    SurveyAnswers = string.Empty,
                                    SupplierApplicationFormVersionId = Guid.Empty,
                                    SupplierApplicationFormVersion = null,
                                    CompletedByUserId = string.Empty,
                                    CompletedByUser = null,
                                    TheiaAnalysisJob = null
                                }
                            ]
                        }
                    ]
                }
            }
        }.AsQueryable();

        DbSet<ApplicationUser> mockDbSet = users.BuildMockDbSet();
        dbContext.Users.Returns(mockDbSet);

        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_IfUsersSupplierSnapshotExists_DoesntThrow()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Supplier);
        string userId = Guid.NewGuid().ToString();
        userService.GetAuthId().Returns(userId);
        
        Guid snapshotId = Guid.NewGuid();
        DbSet<ApplicationUser> mockDbSet =
            new ApplicationUser[]
            {
                new()
                {
                    Id = userId,
                    Email = string.Empty,
                    Supplier = new Supplier
                    {
                        CompanyNumber = string.Empty,
                        Name = string.Empty,
                        ContactEmail = string.Empty,
                        Status = default,
                        ApplicationFormVersions =
                        [
                            new SupplierApplicationFormVersion
                            {
                                SupplierId = Guid.Empty,
                                Supplier = null,
                                ApplicationFormVersionId = Guid.Empty,
                                ApplicationFormVersion = null,
                                Snapshots =
                                [
                                    new SupplierApplicationFormVersionSnapshot
                                    {
                                        Id = snapshotId,
                                        CreatedOnUtc = default,
                                        SurveyAnswers = string.Empty,
                                        SupplierApplicationFormVersionId = Guid.Empty,
                                        SupplierApplicationFormVersion = null,
                                        CompletedByUserId = string.Empty,
                                        CompletedByUser = null,
                                        TheiaAnalysisJob = null
                                    }
                                ]
                            }
                        ]
                    }
                }
            }.BuildMockDbSet();
        
        dbContext.Users.Returns(mockDbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
        
        // Assert
        Assert.Null(exception);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_ThrowsIfUnderwriterNotInBrokerSubmissionUnderwriters_Throws()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Insurer); 
        
        Guid snapshotId = Guid.NewGuid();
        DbSet<SupplierApplicationFormVersionSnapshot> mockDbSet =
            new SupplierApplicationFormVersionSnapshot[]
            {
                new()
                {
                    Id = snapshotId,
                    CreatedOnUtc = default,
                    SurveyAnswers = string.Empty,
                    SupplierApplicationFormVersionId = null,
                    SupplierApplicationFormVersion = null,
                    CompletedByUserId = null,
                    CompletedByUser = null,
                    TheiaAnalysisJob = null,
                    Submissions =
                    [
                        new SubmissionSupplierAppFormVersionSnapshot
                        {
                            SubmissionsSubmissionId = Guid.Empty,
                            SubmissionSupplier = new SubmissionSupplier
                            {
                                SubmissionId = Guid.Empty,
                                Submission = new OrganisationSubmission
                                {
                                    RequestedOnDate = default,
                                    SubmissionName = string.Empty,
                                    DueBy = default,
                                    RequestedBy = string.Empty,
                                    RequestedByApplicationUser = null,
                                    TenantRequestedById = Guid.Empty,
                                    RequestedByTenant = null,
                                    Layers =
                                    [
                                        new Layer
                                        {
                                            Limit = 0,
                                            TargetPremium = null,
                                            Currency = "GBP",
                                            SubmissionId = Guid.Empty,
                                            Submission = null,
                                            IndicationRequests =
                                            [
                                                new BrokerSubmission
                                                {
                                                    RequestedByBrokingHouseId = Guid.Empty,
                                                    RequestedByBrokingHouse = null,
                                                    Layer = null,
                                                    LayerId = Guid.Empty,
                                                    Underwriters =
                                                    [
                                                        new BrokerSubmissionUnderwriter
                                                        {
                                                            BrokerSubmissionId = Guid.Empty,
                                                            BrokerSubmission = null,
                                                            UserId = Guid.NewGuid()
                                                                .ToString(),
                                                            User = null
                                                        }
                                                    ],
                                                    LimitOfLiability = 0
                                                }
                                            ],
                                            Excess = null
                                        }
                                    ]
                                },
                                SupplierId = Guid.Empty,
                                Supplier = null,
                                SupplierSubmissionRequestId = null,
                                SupplierSubmissionRequest = null,
                                AssociatedProducts = null
                            },
                            SnapshotId = Guid.Empty,
                            Snapshot = null,
                            TheiaAnalysisJob = null
                        }
                    ]
                }
            }.BuildMockDbSet();
        
        dbContext.SupplierApplicationFormVersionSnapshots.Returns(mockDbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
        
        // Assert
        Assert.NotNull(exception);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_UnderwriterNotInBrokerSubmissionUnderwriters_DoesntThrow()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Insurer);
        string currentUserId = Guid.NewGuid().ToString();
        userService.GetAuthId().Returns(currentUserId);
        
        Guid snapshotId = Guid.NewGuid();
        DbSet<SupplierApplicationFormVersionSnapshot> mockDbSet =
            new SupplierApplicationFormVersionSnapshot[]
            {
                new()
                {
                    Id = snapshotId,
                    CreatedOnUtc = default,
                    SurveyAnswers = string.Empty,
                    SupplierApplicationFormVersionId = null,
                    SupplierApplicationFormVersion = null,
                    CompletedByUserId = null,
                    CompletedByUser = null,
                    TheiaAnalysisJob = null,
                    Submissions =
                    [
                        new SubmissionSupplierAppFormVersionSnapshot
                        {
                            SubmissionsSubmissionId = Guid.Empty,
                            SubmissionSupplier = new SubmissionSupplier
                            {
                                SubmissionId = Guid.Empty,
                                Submission = new OrganisationSubmission
                                {
                                    RequestedOnDate = default,
                                    SubmissionName = string.Empty,
                                    DueBy = default,
                                    RequestedBy = string.Empty,
                                    RequestedByApplicationUser = null,
                                    TenantRequestedById = Guid.Empty,
                                    RequestedByTenant = null,
                                    Layers =
                                    [
                                        new Layer
                                        {
                                            Limit = 0,
                                            TargetPremium = null,
                                            Currency = "GBP",
                                            SubmissionId = Guid.Empty,
                                            Submission = null,
                                            IndicationRequests =
                                            [
                                                new BrokerSubmission
                                                {
                                                    RequestedByBrokingHouseId = Guid.Empty,
                                                    RequestedByBrokingHouse = null,
                                                    Underwriters =
                                                    [
                                                        new BrokerSubmissionUnderwriter
                                                        {
                                                            BrokerSubmissionId = Guid.Empty,
                                                            BrokerSubmission = null,
                                                            UserId = currentUserId,
                                                            User = null
                                                        }
                                                    ],
                                                    Layer = null,
                                                    LayerId = Guid.Empty,
                                                    LimitOfLiability = 0
                                                }
                                            ],
                                            Excess = null
                                        }
                                    ]
                                },
                                SupplierId = Guid.Empty,
                                Supplier = null,
                                SupplierSubmissionRequestId = null,
                                SupplierSubmissionRequest = null,
                                AssociatedProducts = null
                            },
                            SnapshotId = Guid.Empty,
                            Snapshot = null,
                            TheiaAnalysisJob = null
                        }
                    ]
                }
            }.BuildMockDbSet();
        dbContext
            .SupplierApplicationFormVersionSnapshots
            .Returns(mockDbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
        
        // Assert
        Assert.Null(exception);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_BrokerNotInPrimaryBrokingHouse_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.BrokingHouse);
        
        Guid snapshotId = Guid.NewGuid();
        DbSet<SupplierApplicationFormVersionSnapshot> mockDbSet =
            new SupplierApplicationFormVersionSnapshot[]
            {
                new()
                {
                    Id = snapshotId,
                    CreatedOnUtc = default,
                    SurveyAnswers = string.Empty,
                    SupplierApplicationFormVersionId = null,
                    SupplierApplicationFormVersion = null,
                    CompletedByUserId = null,
                    CompletedByUser = null,
                    TheiaAnalysisJob = null,
                    Submissions =
                    [
                        new SubmissionSupplierAppFormVersionSnapshot
                        {
                            SubmissionsSubmissionId = Guid.Empty,
                            SubmissionSupplier = new SubmissionSupplier
                            {
                                SubmissionId = Guid.Empty,
                                Submission = new OrganisationSubmissionBuilder().WithPrimaryBrokingHouseTenantId(Guid.NewGuid())
                                    .Build(),
                                SupplierId = Guid.Empty,
                                Supplier = null,
                                SupplierSubmissionRequestId = null,
                                SupplierSubmissionRequest = null,
                                AssociatedProducts = null
                            },
                            SnapshotId = Guid.Empty,
                            Snapshot = null,
                            TheiaAnalysisJob = null
                        }
                    ]
                }
            }.BuildMockDbSet();
        
        dbContext.SupplierApplicationFormVersionSnapshots.Returns(mockDbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
    
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }


    [Fact]
    public async Task CheckAccessOrThrowAsync_BrokerIsNotInBrokerSubmissionsBrokers_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.BrokingHouse);
        
        Guid snapshotId = Guid.NewGuid();

        BrokerSubmission bs = new BrokerSubmissionBuilder()
            .WithBrokers(new BrokerSubmissionBrokerBuilder().WithRandomUserId());
        Layer l = new LayerBuilder().WithIndicationRequests(bs);
        DbSet<SupplierApplicationFormVersionSnapshot> mockDbSet =
            new SupplierApplicationFormVersionSnapshot[]
            {
                new()
                {
                    Id = snapshotId,
                    CreatedOnUtc = default,
                    SurveyAnswers = string.Empty,
                    SupplierApplicationFormVersionId = null,
                    SupplierApplicationFormVersion = null,
                    CompletedByUserId = null,
                    CompletedByUser = null,
                    TheiaAnalysisJob = null,
                    Submissions =
                    [
                        new SubmissionSupplierAppFormVersionSnapshot
                        {
                            SubmissionsSubmissionId = Guid.Empty,
                            SubmissionSupplier = new SubmissionSupplier
                            {
                                SubmissionId = Guid.Empty,
                                Submission =
                                    new OrganisationSubmissionBuilder()
                                        .WithRandomPrimaryBrokingHouseTenantId()
                                        .WithLayers(l),
                                SupplierId = Guid.Empty,
                                Supplier = null,
                                SupplierSubmissionRequestId = null,
                                SupplierSubmissionRequest = null,
                                AssociatedProducts = null
                            },
                            SnapshotId = Guid.Empty,
                            Snapshot = null,
                            TheiaAnalysisJob = null
                        }
                    ]
                }
            }.BuildMockDbSet();
        
        dbContext.SupplierApplicationFormVersionSnapshots.Returns(mockDbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(snapshotId, CancellationToken.None));
    
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_NoOrganisationWithTenantId_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Organization);
        
        DbSet<Organisation> dbSet = new List<Organisation> {new OrganisationBuilder().WithRandomTenantId()}.BuildMockDbSet();
        dbContext.Organisations.Returns(dbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.NewGuid(), CancellationToken.None));

        
        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_OrganisationWithNoSuppliers_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Organization);

        DbSet<Organisation> dbSet = new List<Organisation> {new OrganisationBuilder().WithTenantId(validTenantId)}.BuildMockDbSet();
        dbContext.Organisations.Returns(dbSet);

        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.NewGuid(), CancellationToken.None));

        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_OrganisationWithIncompleteSubmissionRequest_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Organization);
        
        SupplierSubmissionRequest supplierSubmissionRequest = new SupplierSubmissionRequestBuilder();
        Supplier supplier = new SupplierBuilder().WithSubmissionRequests(supplierSubmissionRequest);
        OrganisationSupplierAssociation association = new OrganisationSupplierAssociationBuilder().WithSupplier(supplier);
        Organisation organisation = 
            new OrganisationBuilder()
                .WithTenantId(validTenantId)
                .WithSuppliers(association);

        DbSet<Organisation> dbSet = new List<Organisation> {organisation}.BuildMockDbSet();
        dbContext.Organisations.Returns(dbSet);
        
        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.NewGuid(), CancellationToken.None));

        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }

    [Fact]
    public async Task CheckAccessOrThrowAsync_OrganisationWithValidSnapshot_AllowsAccess()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Organization);

        Guid validSnapshotId = Guid.NewGuid();
        SupplierApplicationFormVersionSnapshot versionSnapshot = new SupplierApplicationFormVersionSnapshotBuilder().WithId(validSnapshotId);
        SupplierSubmissionApplicationFormVersionSnapshot submissionSnapshot = new SupplierSubmissionApplicationFormVersionSnapshotBuilder().WithSnapshot(versionSnapshot);
        SupplierSubmissionRequest supplierSubmissionRequest = new SupplierSubmissionRequestBuilder();
        supplierSubmissionRequest.Complete(timeProvider.GetUtcNow(), [submissionSnapshot]);
        Supplier supplier = new SupplierBuilder().WithSubmissionRequests(supplierSubmissionRequest);
        OrganisationSupplierAssociation association = new OrganisationSupplierAssociationBuilder().WithSupplier(supplier);
        Organisation organisation =
            new OrganisationBuilder()
                .WithTenantId(validTenantId)
                .WithSuppliers(association);

        DbSet<Organisation> dbSet = new List<Organisation> {organisation}.BuildMockDbSet();
        dbContext.Organisations.Returns(dbSet);

        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(validSnapshotId, CancellationToken.None));

        // Assert
        Assert.Null(exception);
    }
    
    [Fact]
    public async Task CheckAccessOrThrowAsync_OrganisationWithValidSnapshotButDifferentId_Throws404()
    {
        // Arrange
        tenantResolverService.GetTenantType().Returns(TenantType.Organization);

        SupplierApplicationFormVersionSnapshot versionSnapshot = 
            new SupplierApplicationFormVersionSnapshotBuilder().WithRandomId();
        SupplierSubmissionApplicationFormVersionSnapshot submissionSnapshot = 
            new SupplierSubmissionApplicationFormVersionSnapshotBuilder().WithSnapshot(versionSnapshot);
        
        SupplierSubmissionRequest supplierSubmissionRequest = new SupplierSubmissionRequestBuilder();
        supplierSubmissionRequest.Complete(timeProvider.GetUtcNow(), [submissionSnapshot]);
        
        Supplier supplier = new SupplierBuilder().WithSubmissionRequests(supplierSubmissionRequest);
        OrganisationSupplierAssociation association = new OrganisationSupplierAssociationBuilder().WithSupplier(supplier);
        Organisation organisation =
            new OrganisationBuilder()
                .WithTenantId(validTenantId)
                .WithSuppliers(association);

        DbSet<Organisation> dbSet = new List<Organisation> {organisation}.BuildMockDbSet();
        dbContext.Organisations.Returns(dbSet);

        // Act
        Exception? exception =
            await Record.ExceptionAsync(
                async () => await sut.CheckAccessOrThrowAsync(Guid.NewGuid(), CancellationToken.None));

        // Assert
        ApiProblemDetailsException apiProblemDetailsException = Assert.IsType<ApiProblemDetailsException>(exception);
        Assert.Equal(StatusCodes.Status404NotFound, apiProblemDetailsException.StatusCode);
    }
}