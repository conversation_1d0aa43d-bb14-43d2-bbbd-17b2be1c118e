using Theia.Domain.Entities.Suppliers.ApplicationForms;

namespace Theia.Application.Tests.Unit.Builders.ApplicationForms;

public class SupplierApplicationFormVersionSnapshotBuilder 
    : EntityBuilder<SupplierApplicationFormVersionSnapshot>
{
    private Guid id = Guid.NewGuid();

    public SupplierApplicationFormVersionSnapshotBuilder WithRandomId()
    {
        id = Guid.NewGuid();
        return this;
    }
    
    public SupplierApplicationFormVersionSnapshotBuilder WithId(Guid snapshotId)
    {
        id = snapshotId;
        return this;
    }
    
    public override SupplierApplicationFormVersionSnapshot Build()
    {
        return new SupplierApplicationFormVersionSnapshot
        {
            Id = id,
            CreatedOnUtc = default,
            SurveyAnswers = string.Empty,
            SupplierApplicationFormVersionId = null,
            SupplierApplicationFormVersion = null,
            CompletedByUserId = null,
            CompletedByUser = null,
            TheiaAnalysisJob = null
        };
    }
}