using Theia.Domain.Entities.Organisations.Submissions;

namespace Theia.Application.Tests.Unit.Builders.Submissions;

public class SubmissionSupplierBuilder : EntityBuilder<SubmissionSupplier>
{
    private Guid submissionId;
    private Submission? submission;

    public SubmissionSupplierBuilder WithSubmission(Submission suppliedSubmission)
    {
        submissionId = suppliedSubmission.Id;
        submission = suppliedSubmission;
        
        return this;
    }
    
    public override SubmissionSupplier Build()
    {
        return new SubmissionSupplier
        {
            SubmissionId = submissionId,
            Submission = submission,
            SupplierId = Guid.Empty,
            Supplier = null,
            SupplierSubmissionRequestId = null,
            SupplierSubmissionRequest = null,
            AssociatedProducts = null
        };
    }
}