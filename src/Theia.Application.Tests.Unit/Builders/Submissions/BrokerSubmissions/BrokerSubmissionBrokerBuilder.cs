using Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;

namespace Theia.Application.Tests.Unit.Builders.Submissions.BrokerSubmissions;

public class BrokerSubmissionBrokerBuilder : EntityBuilder<BrokerSubmissionBroker>
{
    private string userId = string.Empty;
    private Guid? brokerSubmissionId;

    public BrokerSubmissionBrokerBuilder WithRandomUserId()
    {
        return WithUserId(Guid.NewGuid().ToString());
    }
    
    public BrokerSubmissionBrokerBuilder WithUserId(string supplyUserId)
    {
        userId = supplyUserId;
        return this;
    }
    
    public BrokerSubmissionBrokerBuilder WithBrokerSubmissionId(Guid suppliedBrokerSubmissionId)
    {
        brokerSubmissionId = suppliedBrokerSubmissionId;
        return this;
    }
    
    public override BrokerSubmissionBroker Build()
    {
        return new BrokerSubmissionBroker
        {
            BrokerSubmissionId = brokerSubmissionId ?? Guid.Empty,
            Submission = null,
            UserId = userId,
            Broker = null
        };
    }
}