using Theia.Domain.Entities.Indications;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;

namespace Theia.Application.Tests.Unit.Builders.Submissions.BrokerSubmissions;

public class BrokerSubmissionBuilder : EntityBuilder<BrokerSubmission>
{
    private List<BrokerSubmissionBroker> brokers = [];
    private List<BrokerSubmissionUnderwriter> underwriters = [];
    private Guid brokerSubmissionId = Guid.NewGuid();
    private Layer? layer;
    private Guid? tenantRequestedById;
    
    public BrokerSubmissionBuilder WithBrokers(params BrokerSubmissionBroker[] suppliedBrokers)
    {
        brokers = suppliedBrokers.ToList();
        return this;
    }
    
    public BrokerSubmissionBuilder WithUnderwriters(params BrokerSubmissionUnderwriter[] supplierUnderwriters)
    {
        underwriters = supplierUnderwriters.ToList();
        return this;
    }

    public BrokerSubmissionBuilder WithBrokerSubmissionId(Guid suppliedBrokerSubmissionId)
    {
        brokerSubmissionId = suppliedBrokerSubmissionId;
        return this;
    }
    
    public BrokerSubmissionBuilder WithDefaultLayerAndSubmission(Submission suppliedSubmission)
    {
        layer = new()
        {
            Currency = string.Empty,
            Excess = 0,
            Limit = 0,
            Submission = suppliedSubmission,
            SubmissionId = suppliedSubmission.Id,
            TargetPremium = 0
        };
        return this;
    }
    
    public override BrokerSubmission Build()
    {
        return new BrokerSubmission
        {
            Id = brokerSubmissionId,
            RequestedByBrokingHouseId = default,
            RequestedByBrokingHouse = null,
            Brokers = brokers,
            Underwriters = underwriters,
            Layer = layer,
            LayerId = layer?.Id ?? Guid.Empty,
            Indications = [],
            LimitOfLiability = 0
        };
    }
}