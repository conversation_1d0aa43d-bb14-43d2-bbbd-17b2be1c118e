using Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;
using Theia.Domain.Entities.Organisations.Submissions.Wholesale;

namespace Theia.Application.Tests.Unit.Builders.Submissions.Wholesale;

public class WholesaleSubmissionBuilder : EntityBuilder<WholesaleSubmission>
{
    private Guid? primaryBrokingHouseId;
    private BrokerSubmission? indicationRequest;
    private Guid? wholesaleSubmissionId;
    private WholesaleSubmissionBroker[] brokers = [];
    
    public override WholesaleSubmission Build()
    {
        return new WholesaleSubmission
        {
            Id = wholesaleSubmissionId ?? Guid.Empty,
            RequestedOnDate = default,
            SubmissionName = string.Empty,
            DueBy = default,
            RequestedBy = string.Empty,
            RequestedByApplicationUser = null,
            TenantRequestedById = Guid.Empty,
            RequestedByTenant = null,
            Brokers = brokers,
            BrokingHouseId = Guid.Empty,
            BrokingHouse = null,
            PrimaryBrokingHouseTenantId = primaryBrokingHouseId,
            IndicationRequest = indicationRequest
        };
    }
    
    public WholesaleSubmissionBuilder WithPrimaryBrokingHouseId(Guid supplierPrimaryBrokingHouseId)
    {
        primaryBrokingHouseId = supplierPrimaryBrokingHouseId;
        return this;
    }

    public WholesaleSubmissionBuilder WithIndicationRequest(BrokerSubmission suppliedIndicationRequest)
    {
        indicationRequest = suppliedIndicationRequest;
        return this;
    }

    public WholesaleSubmissionBuilder WithBrokers(WholesaleSubmissionBroker[] suppliedBrokers)
    {
        brokers = suppliedBrokers;
        return this;
    }

    public WholesaleSubmissionBuilder WithValidWholesaleSubmissionId(Guid supplierWholesaleSubmissionId)
    {
        wholesaleSubmissionId = supplierWholesaleSubmissionId;
        return this;
    }
}