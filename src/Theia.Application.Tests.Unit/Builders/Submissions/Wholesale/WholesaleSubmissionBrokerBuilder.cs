using Theia.Domain.Entities.Organisations.Submissions.Wholesale;

namespace Theia.Application.Tests.Unit.Builders.Submissions.Wholesale;

public class WholesaleSubmissionBrokerBuilder : EntityBuilder<WholesaleSubmissionBroker>
{
    private string? brokerId;
    private Guid? wholesaleSubmissionId;
    
    public override WholesaleSubmissionBroker Build()
    {
        return new WholesaleSubmissionBroker
        {
            BrokerId = brokerId ?? string.Empty,
            Broker = null,
            WholesaleSubmissionId = wholesaleSubmissionId ?? Guid.Empty,
            WholesaleSubmission = null
        };
    }
    
    public WholesaleSubmissionBrokerBuilder WithBrokerId(string suppliedBrokerId)
    {
        brokerId = suppliedBrokerId;
        return this;
    }
    
    public WholesaleSubmissionBrokerBuilder WithWholesaleSubmissionId(Guid suppliedWholesaleSubmissionId)
    {
        wholesaleSubmissionId = suppliedWholesaleSubmissionId;
        return this;
    }
}