<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="EntityFrameworkCore.Testing.NSubstitute" Version="8.1.1" />
        <PackageReference Include="Microsoft.Extensions.TimeProvider.Testing" Version="8.10.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.3" />
        <PackageReference Include="NSubstitute" Version="5.3.0" />
        <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.17">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="xunit" Version="2.5.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Theia.App.Server\Theia.App.Server.csproj" />
    </ItemGroup>

</Project>
