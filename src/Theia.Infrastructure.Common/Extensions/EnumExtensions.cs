using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Theia.Infrastructure.Common.Extensions;

public static class EnumExtensions
{
    public static string GetDescription(this Enum enumValue)
    {
        FieldInfo? field = enumValue.GetType().GetField(enumValue.ToString());
        if (field is null)
        {
            return string.Empty;
        }

        DescriptionAttribute? attribute = field.GetCustomAttribute<DescriptionAttribute>();
        return attribute?.Description ?? enumValue.ToString();
    }
    
    public static string GetDisplay(this Enum enumValue)
    {
        MemberInfo member = enumValue.GetType().GetMember(enumValue.ToString())[0];
        DisplayAttribute? displayAttribute = member.GetCustomAttribute<DisplayAttribute>();
        return displayAttribute is null ? enumValue.ToString() : displayAttribute.GetName();
    }
}