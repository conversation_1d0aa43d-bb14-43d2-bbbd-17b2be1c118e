using System.ComponentModel;

namespace Theia.Infrastructure.Common.Enums;

public enum AllRoles
{
    Default,
    [Description("Theia Admin")]
    TheiaAdmin,
    [Description("Theia Super Admin")]
    TheiaSuperAdmin,
    [Description("Broker")]
    <PERSON>roker,
    [Description("Broking Admin")]
    BrokingHouseAdmin,
    [Description("Organisation User")]
    Organisation,
    [Description("Organisation Admin")]
    OrganisationAdmin,
    [Description("Underwriter")]
    Underwriter,
    [Description("Underwriting Admin")]
    UnderwriterAdmin,
    [Description("Supplier")]
    Supplier,
    [Description("Supplier Admin")]
    SupplierAdmin
}