using Theia.App.Shared.Extensions;
using Theia.Domain.Common.Enums;
using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Client.Underwriting.Models;

public class OptionsVm
{
    public Guid LocalId { get; set; }
    public Guid? OptionId { get; set; }
    public List<OptionValuesVm> Covers { get; init; }
    public DateTimeOffset CreatedOn { get; init; }
    public decimal? InsurerLine { get; set; }
    public decimal? Premium { get; set; }
    public int? BrokeragePercentage { get; set; }
    public LiabilityAgreement? LiabilityAgreement { get; set; }
    public required decimal LimitOfLiability { get; init; }
    public string LimitOfLiabilityString => LimitOfLiability.ConvertToAppFormat();
}

public class OptionValuesVm
{
    public Guid Id { get; init; }
    public decimal LimitsOfLiabilityAmount { get; set; }
    public int RetentionWaitingPeriodAmount { get; set; }
    public bool IsCovered { get; set; } = true;
    public string Name { get; set; }
    public Guid HeadsOfCoverId { get; init; }
    public HeadOfCoverSection Section { get; init; }
    public required bool HasWaitingPeriod { get; init; }
    public required int? WaitingPeriod { get; set; }
}