using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared.Underwriting.Info;

namespace Theia.App.Client.Underwriting.Consumers;

public interface IInfoClient
{
    public Task<ApiResponse<GetBrokerSubmissionsInCurrentMonthResponse>> GetBrokerSubmissionsInCurrentMonthAsync(CancellationToken ct);
    public Task<ApiResponse<GetBrokerSubmissionsInCurrentYearResponse>> GetBrokerSubmissionsInCurrentYearAsync(CancellationToken ct);
    public Task<ApiResponse<GetBrokingHousesCountResponse>> GetBrokingHousesCountAsync(CancellationToken ct);
    public Task<ApiResponse<GetUnopenedBrokerSubmissionsResponse>> GetUnopenedBrokerSubmissionsAsync(CancellationToken ct);
    public Task<ApiResponse<GetTopIndustriesResponse>> GetTopIndustriesAsync(CancellationToken ct);
    public Task<ApiResponse<GetTopCountriesResponse>> GetTopCountriesAsync(CancellationToken ct);
    public Task<ApiResponse<GetOrganisationRevenueSplitResponse>> GetOrganisationRevenueSplitAsync(CancellationToken ct);
    public Task<ApiResponse<GetBrokerSubmissionSplitResponse>> GetBrokerSubmissionSplitAsync(CancellationToken ct);
    public Task<ApiResponse<GetBrokerSubmissionsPerMonthResponse>> GetBrokerSubmissionsPerMonthAsync(CancellationToken ct);
}

public class InfoClient(HttpService httpService) : IInfoClient
{
    public Task<ApiResponse<GetBrokerSubmissionsInCurrentMonthResponse>> GetBrokerSubmissionsInCurrentMonthAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionsInCurrentMonthResponse>("underwriter/info/broker-submissions-current-month", ct);
    
    public Task<ApiResponse<GetBrokerSubmissionsInCurrentYearResponse>> GetBrokerSubmissionsInCurrentYearAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionsInCurrentYearResponse>("underwriter/info/broker-submissions-current-year", ct);
    
    public Task<ApiResponse<GetBrokingHousesCountResponse>> GetBrokingHousesCountAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokingHousesCountResponse>("underwriter/info/broking-houses", ct);
    
    public Task<ApiResponse<GetUnopenedBrokerSubmissionsResponse>> GetUnopenedBrokerSubmissionsAsync(CancellationToken ct)
        => httpService.GetAsync<GetUnopenedBrokerSubmissionsResponse>("underwriter/info/unopened-broker-submissions", ct);

    public Task<ApiResponse<GetTopIndustriesResponse>> GetTopIndustriesAsync(CancellationToken ct)
        => httpService.GetAsync<GetTopIndustriesResponse>("underwriter/info/top-industries", ct);
    
    public Task<ApiResponse<GetTopCountriesResponse>> GetTopCountriesAsync(CancellationToken ct)
        => httpService.GetAsync<GetTopCountriesResponse>("underwriter/info/top-countries", ct);
    
    public Task<ApiResponse<GetOrganisationRevenueSplitResponse>> GetOrganisationRevenueSplitAsync(CancellationToken ct)
        => httpService.GetAsync<GetOrganisationRevenueSplitResponse>("underwriter/info/org-revenue-split", ct);
    
    public Task<ApiResponse<GetBrokerSubmissionSplitResponse>> GetBrokerSubmissionSplitAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionSplitResponse>("underwriter/info/broker-submission-split", ct);

    public Task<ApiResponse<GetBrokerSubmissionsPerMonthResponse>> GetBrokerSubmissionsPerMonthAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionsPerMonthResponse>("underwriter/info/broker-submissions-per-month", ct);
}