<PageHeader Header="@Resource.Statistics"/>

<TelerikGridLayout ColumnSpacing="1em" RowSpacing="2em">
    <GridLayoutColumns>
        <GridLayoutColumn Width="24%"></GridLayoutColumn>
        <GridLayoutColumn Width="24%"></GridLayoutColumn>
        <GridLayoutColumn Width="24%"></GridLayoutColumn>
        <GridLayoutColumn Width="24%"></GridLayoutColumn>
    </GridLayoutColumns>
    <GridLayoutRows>
        <GridLayoutRow Height="15vh"></GridLayoutRow>
        <GridLayoutRow Height="35vh"></GridLayoutRow>
        <GridLayoutRow Height="70vh"></GridLayoutRow>
    </GridLayoutRows>
    <GridLayoutItems>
        <GridLayoutItem Row="1" Column="1">
            <SingleInfoCard Title="@Resource.Unopened_Indication_Requests" GetValueFunc="@GetUnopenedBrokerSubmissionsAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="1" Column="2">
            <SingleInfoCard Title="@string.Format(Resource.Submissions_received_by_team_in, DateTime.Now.ToString("MMMM"))" GetValueFunc="@GetBrokerSubmissionsInCurrentMonthAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="1" Column="3">
            <SingleInfoCard Title="@string.Format(Resource.Submissions_received_by_team_in, DateTime.Now.ToString("yyyy"))" GetValueFunc="@GetBrokerSubmissionsInCurrentYearAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="1" Column="4">
            <SingleInfoCard Title="@Resource.Broking_House_Relationships" GetValueFunc="@GetBrokingHousesCountAsync"/>
        </GridLayoutItem>

        <GridLayoutItem Row="2" Column="1">
            <PieChart Title="@Resource.Top_3_Industries" GetFunction="@GetTopIndustriesAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="2">
            <PieChart Title="@Resource.Top_3_Countries" GetFunction="@GetTopCountriesAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="3">
            <PieChart Title="@Resource.Organisation_Revenue_Split" GetFunction="@GetOrgRevenueSplitAsync"/>
        </GridLayoutItem>
        <GridLayoutItem Row="2" Column="4">
            <PieChart Title="@Resource.Indication_Request_Split" GetFunction="@GetBrokerSubmissionSplitAsync"/>
        </GridLayoutItem>

        <GridLayoutItem Row="3" Column="1" ColumnSpan="4">
            <LineChart Title="@Resource.Indication_Requests" GetFunction="GetBrokerSubmissionsPerMonthAsync"/>
        </GridLayoutItem>
    </GridLayoutItems>
</TelerikGridLayout>