using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Client.Underwriting.Consumers;
using Theia.App.Shared.Models;
using Theia.App.Shared.Underwriting.Info;
using Theia.FrontendResources;

namespace Theia.App.Client.Underwriting.Pages;

[Route($"/{UrlConstants.Statistics}")]
public partial class Statistics : ComponentBase, IDisposable
{
    [Inject]
    private IInfoClient InfoClient { get; init; } = null!;
    
    [Inject]
    private IBreadcrumbService BreadcrumbService { get; init; } = null!;

    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private CancellationTokenSource _cts = new();
    
    protected override Task OnInitializedAsync()
    {
        SetupBreadcrumbHome();
        return base.OnInitializedAsync();
    }
    
    private void SetupBreadcrumbHome()
    {
        BreadcrumbService.AddBreadcrumb(new BreadcrumbItem(Resource.Statistics, UrlConstants.Statistics, true));
    }
    
    private async Task<IPieChartModel?> GetTopIndustriesAsync()
    {
        ApiResponse<GetTopIndustriesResponse> response = await InfoClient.GetTopIndustriesAsync(_cts.Token);
        return NotificationHelper.Unwrap(response);
    }
    
    private async Task<IPieChartModel?> GetTopCountriesAsync()
    {
        ApiResponse<GetTopCountriesResponse> response = await InfoClient.GetTopCountriesAsync(_cts.Token);
        return NotificationHelper.Unwrap(response);
    }
    
    private async Task<IPieChartModel?> GetOrgRevenueSplitAsync()
    {
        ApiResponse<GetOrganisationRevenueSplitResponse> response = await InfoClient.GetOrganisationRevenueSplitAsync(_cts.Token);
        return NotificationHelper.Unwrap(response);
    }
    
    private async Task<IPieChartModel?> GetBrokerSubmissionSplitAsync()
    {
        ApiResponse<GetBrokerSubmissionSplitResponse> response = await InfoClient.GetBrokerSubmissionSplitAsync(_cts.Token);
        return NotificationHelper.Unwrap(response);
    }

    private async Task<string?> GetUnopenedBrokerSubmissionsAsync()
    {
        ApiResponse<GetUnopenedBrokerSubmissionsResponse> response = 
            await InfoClient.GetUnopenedBrokerSubmissionsAsync(_cts.Token);
        
        return NotificationHelper.Unwrap(response)?.UnopenedBrokerSubmissionsCount.ToString();
    }

    private async Task<string?> GetBrokerSubmissionsInCurrentMonthAsync()
    {
        ApiResponse<GetBrokerSubmissionsInCurrentMonthResponse> response =
            await InfoClient.GetBrokerSubmissionsInCurrentMonthAsync(_cts.Token);
        
        return NotificationHelper.Unwrap(response)?.SubmissionsCount.ToString();
    }
    
    private async Task<string?> GetBrokerSubmissionsInCurrentYearAsync()
    {
        ApiResponse<GetBrokerSubmissionsInCurrentYearResponse> response =
            await InfoClient.GetBrokerSubmissionsInCurrentYearAsync(_cts.Token);
        
        return NotificationHelper.Unwrap(response)?.SubmissionsCount.ToString();
    }
    
    private async Task<string?> GetBrokingHousesCountAsync()
    {
        ApiResponse<GetBrokingHousesCountResponse> response =
            await InfoClient.GetBrokingHousesCountAsync(_cts.Token);
        
        return NotificationHelper.Unwrap(response)?.BrokingHousesCount.ToString();
    }

    private async Task<LineChartModel> GetBrokerSubmissionsPerMonthAsync()
    {
        ApiResponse<GetBrokerSubmissionsPerMonthResponse> response =
            await InfoClient.GetBrokerSubmissionsPerMonthAsync(_cts.Token);

        List<LineChartItem>? lineData =
            NotificationHelper
                .Unwrap(response)?
                .SubmissionsPerMonth
                .Select(spm =>
                    new LineChartItem($"{spm.Month} - {spm.Year}", spm.SubmissionCount))
                .ToList();

        return new LineChartModel(lineData);
    }
    
    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
}