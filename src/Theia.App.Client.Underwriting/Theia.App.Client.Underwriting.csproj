<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <LangVersion>12</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.0" PrivateAssets="all" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Theia.App.Client.Common.Login\Theia.App.Client.Common.Login.csproj" />
      <ProjectReference Include="..\Theia.App.Shared.Underwriting\Theia.App.Shared.Underwriting.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="wwwroot\appsettings.Production.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\appsettings.Development.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="wwwroot\appsettings.UAT.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Pages\Indications\PolicyForms.razor" />
      <_ContentIncludedByDefault Remove="Pages\Indications\ViewPolicyFormModal.razor" />
      <_ContentIncludedByDefault Remove="Pages\Indications\Components\OrganisationData.razor" />
    </ItemGroup>

</Project>
