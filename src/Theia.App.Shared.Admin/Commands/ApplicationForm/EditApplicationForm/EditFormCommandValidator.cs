using FluentValidation;
using Theia.FrontendResources;

namespace Theia.App.Shared.Admin.Commands.ApplicationForm.EditApplicationForm;

public class EditFormCommandValidator : AbstractValidator<EditFormCommand>
{
    public EditFormCommandValidator()
    {
        RuleFor(r => r.Name)
            .NotEmpty()
            .WithName(_ => Resource.Name);

        RuleFor(r => r.ApplicationFormCode)
            .NotEmpty()
            .WithMessage(_ => BackendResources.Resource.Code_is_required);
    }
}