using Theia.Domain.Common.Enums;

namespace Theia.App.Shared.Admin.ControlFrameworks.GetControlFrameworkForEdit;

public record ControlFrameworkForEditResponse
{
    public required Guid Id { get; init; }
    public required string Name { get; init; }
    public required string Description { get; init; }
    public required bool IsActive { get; init; }
    public required ControlFrameworkType Type { get; init; }
    public required ControlFrameworkForEditResponseCategory[] ControlFrameworkCategories { get; init; }
}

public record ControlFrameworkForEditResponseCategory(
    Guid Id, string Name, int Weighting, bool IsActive, string Description, string Reference,
    ControlFrameworkForEditResponseCategoryClause[] ControlFrameworkCategoryClauses);


public record ControlFrameworkForEditResponseCategoryClause
{
    public required Guid Id { get; init; }
    public required string Name { get; init; }
    public required decimal Weighting { get; init; }
    public required bool IsActive { get; init; }
    public required string Description { get; init; }
    public required string Reference { get; init; }
}