using FluentValidation;

namespace Theia.App.Shared.Admin.ControlFrameworks.CreateControlFramework;

public record CreateControlFrameworkCommand(string Name, string? Description, bool IsActive, string Code);

public class CreateControlFrameworkCommandValidator : AbstractValidator<CreateControlFrameworkCommand>
{
    public CreateControlFrameworkCommandValidator()
    {
        RuleFor(r => r.Name).NotEmpty();
    }
}