using FluentValidation;
using Theia.FrontendResources;
using Theia.Http.Services;

namespace Theia.App.Shared.Admin.ApplicationForms;

public record EditVersionRequest(WebSafeGuid Id, string Version, bool IsActive);

public class EditVersionRequestValidator : AbstractValidator<EditVersionRequest>
{
    public EditVersionRequestValidator()
    {
        RuleFor(r => r.Version).NotEmpty().WithName(_ => Resource.Version);
    }
}