using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Shared.Admin.ApplicationForms;

public record GetApplicationFormsResponse : DataEnvelope<GetApplicationFormsResponseForm>;

public record GetApplicationFormsResponseForm(
    Guid Id,
    string ApplicationFormCode,
    string Name,
    string Description,
    DateTimeOffset? CreatedOn,
    IEnumerable<GetApplicationFormsResponseVersion> Versions,
    ApplicationFormType ApplicationFormType);

public record GetApplicationFormsResponseVersion(Guid Id, string Version, DateTimeOffset? CreatedOn, bool IsComplete, bool InUse, bool IsActive);
