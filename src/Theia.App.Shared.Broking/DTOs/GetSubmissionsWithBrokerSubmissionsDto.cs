namespace Theia.App.Shared.Broking.DTOs;

public class GetSubmissionsWithBrokerSubmissionsDto
{
    public Guid SubmissionId { get; init; }
    public string SubmissionName { get; init; }
    public string? SubmittedBy { get; init; }
    public DateTimeOffset? SubmittedOn { get; init; }
    public DateTimeOffset RequestedOnDate { get; init; }
    public BrokerSubmissionsDto[]? BrokerSubmissions { get; init; }
}