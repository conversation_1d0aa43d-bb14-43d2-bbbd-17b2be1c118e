namespace Theia.App.Shared.Broking.DTOs;

public class GetOrganisationApplicationFormsDTO
{
    public Guid ApplicationFormId { get; init; }
    public string? ApplicationFormName { get; init; }
    public GetOrganisationApplicationFormVersionsDto[] Versions { get; init; }
}

public class GetOrganisationApplicationFormVersionsDto
{
    public string? ApplicationFormVersion { get; init; }
    public Guid ApplicationFormVersionId { get; set; }
    public Guid ChildToApplicationFormId { get; init; }
}