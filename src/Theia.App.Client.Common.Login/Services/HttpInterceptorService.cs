using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using System.Net;
using System.Net.Http.Headers;
using Theia.App.Client.Common.Extensions;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Services;
using Theia.App.Client.Common.Services.UI;
// using Toolbelt.Blazor;

namespace Theia.App.Client.Common.Login.Services;

public class HttpInterceptorService : IHttpInterceptorService, IDisposable
{
    private readonly HttpClient _httpClient;
    // private readonly HttpClientInterceptor _httpClientInterceptor;
    // private readonly IRefreshTokenService _refreshTokenService;
    // private readonly IAuthenticationService _authenticationService;
    private readonly ISubDomainProvider _subDomainProvider;

    public HttpInterceptorService(IHttpClientFactory httpClientFactory,
        // HttpClientInterceptor httpClientInterceptor,
        // SpinnerService spinnerService,
        // NavigationManager navigationManager,
        // IRefreshTokenService refreshTokenService,
        // IAuthenticationService authenticationService,
        ISubDomainProvider subDomainProvider)
    {
        _httpClient = httpClientFactory.CreateClient("HttpInterceptorService");
        // _httpClientInterceptor = httpClientInterceptor;
        // _refreshTokenService = refreshTokenService;
        // _authenticationService = authenticationService;
        _subDomainProvider = subDomainProvider;
        // _httpClientInterceptor.BeforeSendAsync += async (s, e) => await HttpClientInterceptor_BeforeSendAsync(s, e);
        // _httpClientInterceptor.AfterSendAsync += async (s, e) => await HttpClientInterceptor_AfterSendAsync(s, e);
    }

    public void Dispose()
    {
        // _httpClientInterceptor.BeforeSendAsync -= HttpClientInterceptor_BeforeSendAsync;
        // _httpClientInterceptor.AfterSendAsync -= HttpClientInterceptor_AfterSendAsync;
        _httpClient.Dispose();
    }

    private async Task HttpClientInterceptor_BeforeSendAsync(object sender)
    {
        string? subDomain = _subDomainProvider.GetSubDomain();

        if (subDomain is not null)
        {
            // e.Request.Headers.Add("X-Tenant", subDomain);
        }

        // if (e.Request.Headers.Authorization is not null)
        // {
        //     string absPath = e.Request.RequestUri.AbsolutePath;
        //     if (!absPath.Contains("/api/account/"))
        //     {
        //         TryRefreshTokenResult result = await _refreshTokenService.TryRefreshToken();
        //         switch (result.CurrentTokenValid)
        //         {
        //             case true:
        //                 return;
        //             case false when result.RefreshedToken is not null:
        //                 e.Request.Headers.Authorization = new AuthenticationHeaderValue("bearer", result.RefreshedToken);
        //                 break;
        //             case false:
        //                 e.Request.Headers.Authorization = null;
        //                 break;
        //         }
        //     }
        // }
    }

    private async Task HttpClientInterceptor_AfterSendAsync(object sender)
    {
        // if (e.Response is {StatusCode: HttpStatusCode.Forbidden or HttpStatusCode.Unauthorized})
        // {
        //     await _authenticationService.Logout();
        // }
        //
        // await Task.CompletedTask;
    }
}