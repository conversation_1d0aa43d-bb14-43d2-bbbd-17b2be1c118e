namespace Theia.App.Shared.Organisation.DTOs;

public class BrokerSubmissionsAttachedToSubmissions
{
    public required string BrokerSubmissionName { get; init; }
    public required Guid BrokerSubmissionId { get; init; }
    public required string CreatedByBrokingHouse { get; init; }
    public required DateTimeOffset CreatedOn { get; init; }
    public required bool HasUnansweredQuestions { get; init; }
}