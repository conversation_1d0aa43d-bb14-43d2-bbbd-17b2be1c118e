using Theia.App.Shared.Enums;
using Theia.App.Shared.Models;

namespace Theia.App.Shared.Organisation.HomePage;

public record GetHomePageInfoResponse(
    string PercentagePosition,
    int UnansweredQuestions,
    int IncompleteAssessments,
    byte PercentageCompleted,
    string OrganisationName,
    string? LastSubmissionId,
    ControlFrameworkModel[] Frameworks,
    GetHomePageInfoUserWithAccess[] Brokers,
    GetHomePageInfoUserWithAccess[] Underwriters);
    
public record GetHomePageInfoUserWithAccess(string UserFullName, string TenantName);