using OneOf;
using OneOf.Types;
using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Organisations.EditOrganisation;
using Theia.App.Shared.Broking.Organisations;
using Theia.App.Shared.Broking.Organisations.GetAvailableBrokers;
using Theia.App.Shared.Broking.Organisations.GetBrokingHouseOrganisations;

namespace Theia.App.Client.Broking.Consumers;

public interface IOrganisationsClient
{
    Task<ApiResponse<GetBrokersAvailableForOrganisationResponse>> GetAvailableBrokers(CancellationToken ct);
    Task<ApiResponse<DataEnvelope<GetBrokingHouseOrganisationsResponseItem>>> GetOrganisationRequestsAsync(DataSourceRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> DeleteOrganisationRequestAsync(Guid id, CancellationToken ct);
    Task<NoPayloadApiResponse> EditOrganisationRequestAsync(EditOrganisationRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> UpdateOrganisationBrokers(UpdateOrganisationBrokersRequest request, CancellationToken ct);
}

public class OrganisationsClient : IOrganisationsClient
{
    private readonly HttpService _httpService;

    public OrganisationsClient(HttpService httpService)
    {
        _httpService = httpService;
    }

    public Task<ApiResponse<GetBrokersAvailableForOrganisationResponse>> GetAvailableBrokers(CancellationToken ct)
        => _httpService.GetAsync<GetBrokersAvailableForOrganisationResponse>("organisations/availableBrokers", ct);

    public Task<ApiResponse<DataEnvelope<GetBrokingHouseOrganisationsResponseItem>>> GetOrganisationRequestsAsync(DataSourceRequest request, CancellationToken ct)
        => _httpService.PostAsync<DataSourceRequest, DataEnvelope<GetBrokingHouseOrganisationsResponseItem>>("organisations/brokingHouse", request, ct);

    public Task<NoPayloadApiResponse> DeleteOrganisationRequestAsync(Guid id, CancellationToken ct)
        => _httpService.DeleteAsync($"organisations/{id}", ct);

    public Task<NoPayloadApiResponse> 
        EditOrganisationRequestAsync(EditOrganisationRequest request, CancellationToken ct)
            => _httpService.PutAsync("organisations", request, ct);

    public Task<NoPayloadApiResponse>
        UpdateOrganisationBrokers(UpdateOrganisationBrokersRequest request, CancellationToken ct)
            => _httpService.PutAsync("organisations/updateBrokers", request, ct);
}