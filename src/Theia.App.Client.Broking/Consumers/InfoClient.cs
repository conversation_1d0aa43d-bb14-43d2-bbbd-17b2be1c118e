using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared.Broking.Models.Info;

namespace Theia.App.Client.Broking.Consumers;

public class InfoClient(HttpService httpService)
{
    public Task<ApiResponse<GetBrokerSubmissionsSentResponse>> GetBrokerSubmissionsSentAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionsSentResponse>("broker/info/broker-submissions-sent", ct);
    
    public Task<ApiResponse<GetOpenOrganisationRequestsResponse>> GetOpenOrganisationRequestsAsync(CancellationToken ct)
        => httpService.GetAsync<GetOpenOrganisationRequestsResponse>("broker/info/open-organisation-requests", ct);
    
    public Task<ApiResponse<GetOrganisationSubmissionThisMonthResponse>> GetOrganisationSubmissionsThisMonthAsync(CancellationToken ct)
        => httpService.GetAsync<GetOrganisationSubmissionThisMonthResponse>("broker/info/organisation-submissions-this-month", ct);
    
    public Task<ApiResponse<GetUnopenedBrokerSubmissionsResponse>> GetUnopenedBrokerSubmissionsAsync(CancellationToken ct)
        => httpService.GetAsync<GetUnopenedBrokerSubmissionsResponse>("broker/info/unopened-broker-submissions", ct);
    
    public Task<ApiResponse<GetUnopenedOrganisationSubmissionsResponse>> GetUnopenedOrganisationSubmissionsAsync(CancellationToken ct)
        => httpService.GetAsync<GetUnopenedOrganisationSubmissionsResponse>("broker/info/unopened-organisation-submissions", ct);
    
    public Task<ApiResponse<GetTopCountriesResponse>> GetTopCountriesAsync(CancellationToken ct)
        => httpService.GetAsync<GetTopCountriesResponse>("broker/info/top-countries", ct);
    
    public Task<ApiResponse<GetTopIndustriesResponse>> GetTopIndustriesAsync(CancellationToken ct)
        => httpService.GetAsync<GetTopIndustriesResponse>("broker/info/top-industries", ct);
    
    public Task<ApiResponse<GetOrganisationRevenueSplitResponse>> GetOrganisationRevenueSplitAsync(CancellationToken ct)
        => httpService.GetAsync<GetOrganisationRevenueSplitResponse>("broker/info/organisation-revenue-split", ct);

    public Task<ApiResponse<GetBrokerSubmissionsPerMonthResponse>> GetBrokerSubmissionsPerMonthAsync(CancellationToken ct)
        => httpService.GetAsync<GetBrokerSubmissionsPerMonthResponse>("broker/info/broker-submissions-per-month", ct);
    
    public Task<ApiResponse<GetOrganisationSubmissionsPerMonthResponse>> GetOrganisationSubmissionsPerMonthAsync(CancellationToken ct)
        => httpService.GetAsync<GetOrganisationSubmissionsPerMonthResponse>("broker/info/organisation-submissions-per-month", ct);
}