using FluentValidation;
using Theia.Domain.Common.Enums;
using Theia.Infrastructure.Common.Defaults;

namespace Theia.App.Client.Broking.Pages.OrganisationRequests;

public class OrganisationRequestVm
{
    public Guid Id { get; set; }
    public string OrganisationName { get; set; } = string.Empty;
    public string ContactName { get; set; } = string.Empty;
    public string ContactEmail { get; set; } = string.Empty;
    public string ContactPhoneNumber { get; set; } = string.Empty;
    public OrganisationRequestStatus Status { get; set; }
    public string AdminFullName { get; set; } = string.Empty;
    public string AdminEmail { get; set; } = string.Empty;
}

public class OrganisationRequestVmValidator : AbstractValidator<OrganisationRequestVm>
{
    public OrganisationRequestVmValidator()
    {
        RuleFor(r => r.OrganisationName)
            .NotEmpty()
            .MaximumLength(OrganisationEntityDefaults.NameMaxLength);

        RuleFor(r => r.ContactName)
            .NotEmpty()
            .MaximumLength(OrganisationEntityDefaults.ContactNameMaxLength);

        RuleFor(r => r.ContactEmail)
            .NotEmpty()
            .EmailAddress()
            .MaximumLength(OrganisationEntityDefaults.EmailMaxLength);

        RuleFor(r => r.ContactPhoneNumber)
            .NotEmpty()
            .MaximumLength(OrganisationEntityDefaults.PhoneMaxLength);

        RuleFor(r => r.AdminFullName).NotEmpty();
        RuleFor(r => r.AdminEmail)
            .NotEmpty()
            .MaximumLength(ApplicationUserDefaults.MaxUserNameLength)
            .EmailAddress();
    }
}