@using Telerik.FontIcons
@using Theia.Infrastructure.Common.Defaults

<PageHeader Header=@Resource.Organisations />

<TelerikGrid
    @ref="_gridRef"
    TItem="OrganisationVm"
    Size="@ThemeConstants.Grid.Size.Medium"
    Sortable="true"
    ShowColumnMenu="true"
    Reorderable="true"
    ConfirmDelete="true"
    Pageable
    @bind-PageSize="_pageSize"
    OnRead="FetchDataAsync"
    OnDelete="@DeleteOrganisationAsync"
    EnableLoaderContainer="DefaultSettings.Grid.IsLoaderVisible">
    <GridToolBarTemplate>
        <AppGridSearchBox/> 
    </GridToolBarTemplate>
    <GridSettings>
        <GridPagerSettings PageSizes="DefaultSettings.Pager.PageSizes" ButtonCount="DefaultSettings.Pager.ButtonCount"/>
    </GridSettings>
    <GridColumns>
        <GridColumn Field="@nameof(OrganisationVm.OrganisationName)" Title="@Resource.Organisation_name"/>
        <GridColumn Field="@nameof(OrganisationVm.ContactName)" Title="@Resource.Contact_Name"/>
        <GridColumn Field="@nameof(OrganisationVm.ContactEmail)" Title="@Resource.Email"/>
        <GridColumn Field="@nameof(OrganisationVm.ContactPhoneNumber)" Title="@Resource.Phone_Number"/>
        <GridCommandColumn ShowColumnMenu="false">
            @if (context is OrganisationVm vm)
            {
                <GridCommandButton
                    Class="k-float-right"
                    Title="@Resource.Submissions"
                    Size="@ThemeConstants.Button.Size.Small"
                    Icon="@FontIcon.FolderOpen"
                    FillMode="@ThemeConstants.Button.FillMode.Solid"
                    ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                    OnClick="@(() => NavigateToViewSubmissions(vm.Id))">
                </GridCommandButton>
            }
        </GridCommandColumn>
    </GridColumns>
    <NoDataTemplate>
        <GridNoDataSkeleton Loaded="_loaded"/>
    </NoDataTemplate>
</TelerikGrid>


<style>
    .k-wizard .k-wizard-content { 
        padding: 3em 7em 1em 5em;
    }
    
    #additional-info {
        min-height: 8rem;
    }
    
    .brokers-save-button {
        width: fit-content;
        margin-left: auto;
    }
</style>