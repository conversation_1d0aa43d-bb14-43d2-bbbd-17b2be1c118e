namespace Theia.App.Client.Broking.ViewModels;

public class NewSubmissionViewModel
{
    public string? SubmissionName { get; set; }
    public List<Guid> ListOfApplicationForms { get; set; } = [];
    public DateTime DueBy { get; set; } = DateTime.Today;
    public bool IsAddNewAssessmentVisible { get; private set; }
    public bool FirstStepDisabled { get; private set; }
    public bool SecondStepDisabled { get; private set; }
    
    public void Show()
    {
        IsAddNewAssessmentVisible = true;
    }

    public void Hide()
    {
        IsAddNewAssessmentVisible = false;
        Clear();
    }

    public void ChangeVisibility(bool isVisible)
    {
        if (isVisible) Show();
        else Hide();
    }

    public void FirstStepCompleted()
    {
        FirstStepDisabled = true;
    }

    public void SecondStepCompleted()
    {
        SecondStepDisabled = true;
    }

    private void Clear()
    {
        DueBy = DateTime.Today;
        ListOfApplicationForms = [];
        SubmissionName = default;
        FirstStepDisabled = false;
        SecondStepDisabled = false;
    }
}