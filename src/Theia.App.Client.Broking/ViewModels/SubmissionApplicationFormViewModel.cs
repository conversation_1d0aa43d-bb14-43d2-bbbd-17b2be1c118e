using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Client.Broking.ViewModels;

public class SubmissionApplicationFormViewModel
{
    public required Guid SubmissionApplicationFormId { get; init; }
    public required ApplicationFormStatus ApplicationFormStatus { get; init; }
    public required int PercentageComplete { get; init; }
    public required bool HasBeenModifiedByBroker { get; init; }
    public required string FormVersion { get; init; }
    public required Guid ApplicationFormVersionId { get; init; }
    public required string FormName { get; init; }
}