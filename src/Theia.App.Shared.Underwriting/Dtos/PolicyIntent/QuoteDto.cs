using Theia.App.Shared.Enums;
using Theia.Domain.Common.Enums;

namespace Theia.App.Shared.Underwriting.Dtos.PolicyIntent;

public record QuoteDto
{
    public Guid QuoteId { get; init; }
    public int? IndictionValidDays { get; init; }
    public DateTimeOffset? PolicyPeriodStart { get; init; }
    public DateTimeOffset? PolicyPeriodEnd { get; init; }
    public string? InclusiveNoteForIndication { get; init; }
    public DateTimeOffset? RetroactiveDate { get; init; }
    public RetroactiveDateType RetroactiveDateType { get; init; }
    public string? ChoiceOfLawAndJurisdiction { get; init; }
    public string? IncidentResponsePanel { get; init; }
    public string? IncidentAndClaimsNotification { get; init; }
    public required Guid ForInsurerId { get; init; }
    public Guid? SelectedPolicyWordingId { get; init; }
    public decimal LimitOfLiability { get; init; }
}