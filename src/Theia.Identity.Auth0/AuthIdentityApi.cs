using Auth0.AuthenticationApi;
using Auth0.AuthenticationApi.Models;
using Auth0.Core.Exceptions;
using Auth0.ManagementApi;
using Auth0.ManagementApi.Models;
using Auth0.ManagementApi.Paging;
using Theia.Identity.Interface;
using Theia.Identity.Interface.CreateRole;
using Theia.Identity.Interface.CreateUser;
using Theia.Identity.Interface.Roles;
using Theia.Identity.Interface.Shared;
using Theia.Identity.Interface.UpdateUser;

namespace Theia.Identity.Auth0;

public sealed class IdentityApi(
        Auth0ApiOptions auth0ApiOptions,
        IManagementApiClient client,
        IRateLimitExceptionHandler rateLimitExceptionHandler)
    : IAuthIdentityApi
{
    private const int MaxRateLimitRetries = 5;

    public async Task<CreateUserResponse> CreateUserAsync(CreateUserRequest request, CancellationToken ct)
    {
        User newUser = await client.Users.CreateAsync(new UserCreateRequest
        {
            Connection = auth0ApiOptions.Connection.Name,
            Email = request.User.Email,
            FirstName = request.User.FirstName,
            LastName = request.User.LastName,
            VerifyEmail = false,
            Password = $"pass_!@#123_{Guid.NewGuid():N}",
            AppMetadata = new UserAppMetadata
            {
                TenantHostname = request.TenantHostname
            },
            UserMetadata = new UserMetadata
            {
                PhoneNumber = request.User.PhoneNumber
            }
        }, ct);

        await Task.Delay(1000, ct);

        await client.Users.AssignRolesAsync(newUser.UserId, new()
        {
            Roles = [request.RoleAuthId]
        }, ct);
        
        await Task.Delay(1000, ct);

        IdentityOptionsOrganisation? org = auth0ApiOptions.GetOrganisation(request.OrganisationTypes);
        if (!org.HasValue || string.IsNullOrWhiteSpace(org.Value.Id)) 
            throw new ArgumentException("Organisation does not exist", nameof(request));
        
        await client.Organizations.AddMembersAsync(org.Value.Id, new OrganizationAddMembersRequest {Members = [newUser.UserId]}, ct);
        await client.Organizations
            .AddMemberRolesAsync(org.Value.Id, newUser.UserId, new OrganizationAddMemberRolesRequest {Roles = [request.RoleAuthId]}, ct);

        await Task.Delay(1000, ct);

        await SendChangePasswordInvitationEmail(request.User.Email, ct);

        return new CreateUserResponse(newUser.UserId);
    }

    public async Task SendChangePasswordInvitationEmail(string email, CancellationToken ct)
    {
        using AuthenticationApiClient authenticationApiClient = new(new Uri(auth0ApiOptions.Authority));
        await authenticationApiClient.ChangePasswordAsync(new ChangePasswordRequest
        {
            Connection = auth0ApiOptions.Connection.Name,
            Email = email
        }, ct);
    }

    public async Task SendChangePasswordEmailAsync(ChangeUsersPasswordRequest request, CancellationToken ct)
    {
        using AuthenticationApiClient authenticationApiClient = new(new Uri(auth0ApiOptions.Authority));
        await authenticationApiClient.ChangePasswordAsync(new ChangePasswordRequest
        {
            Connection = auth0ApiOptions.Connection.Name,
            Email = request.Email
        }, ct);
    }

    public async Task<bool> UpdateUserAsync(UpdateUserRequest request, CancellationToken ct)
    {
        User? responseUser = await client.Users.UpdateAsync(request.UserId, new()
        {
            FirstName = request.Forename,
            LastName = request.Surname,
            Blocked = !request.DisableUser
        }, ct);

        IPagedList<Role> toRemove = await client.Users.GetRolesAsync(request.UserId, new(), ct);

        if (toRemove is { Count: > 0 })
        {
            await client.Users.RemoveRolesAsync(request.UserId, new()
            {
                Roles = [toRemove[0].Id]
            }, ct);
        }
        await client.Users.AssignRolesAsync(request.UserId, new()
        {
            Roles = [request.SelectedRoleId]
        }, ct);

        IdentityOptionsOrganisation? org = auth0ApiOptions.GetOrganisation(request.OrganisationTypes);
        if (!org.HasValue || string.IsNullOrWhiteSpace(org.Value.Id)) 
            throw new ArgumentException("Organisation does not exist", nameof(request));

        await client.Organizations
            .AddMemberRolesAsync(org.Value.Id, request.UserId, new OrganizationAddMemberRolesRequest {Roles = [request.SelectedRoleId]}, ct);

        return responseUser is not null;
    }

    public Task UpdateUsersNameAsync(UpdateUsersNameRequest request, CancellationToken ct)
    {
        return client.Users.UpdateAsync(request.UserId, new UserUpdateRequest
        {
            FirstName = request.Forename,
            LastName = request.Surname
        }, ct); 
    }

    public Task UpdateUsersEmailAsync(UpdateUsersEmailRequest request, CancellationToken ct)
    {
        return client.Users.UpdateAsync(request.UserId, new UserUpdateRequest
        {
            Email = request.Email,
        }, ct);
    }

    public Task ConfirmUsersEmail(ConfirmUsersEmailRequest request, CancellationToken ct)
    {
        return client.Users.UpdateAsync(request.UserId, new UserUpdateRequest {EmailVerified = true}, ct);
    }

    public Task RemoveUsersMfaAsync(string userId, CancellationToken ct)
    {
        return client.Users.DeleteAuthenticationMethodsAsync(userId, ct);
    }

    public async Task ChangeRolesAsync(string userId, string[] rolesToRemoveId, string[] newRolesId, CancellationToken ct)
    {
        await client.Users.RemoveRolesAsync(userId, new AssignRolesRequest {Roles = rolesToRemoveId}, ct);
        await client.Users.AssignRolesAsync(userId, new AssignRolesRequest {Roles = newRolesId}, ct);
    }

    public Task BlockUserAsync(string userId, CancellationToken ct)
    {
        return client.Users.UpdateAsync(userId, new UserUpdateRequest {Blocked = true}, ct);
    }

    public async Task ResendEmailConfirmation(string userName, CancellationToken ct)
    {
        await SendChangePasswordInvitationEmail(userName, ct);
    }

    public Task ReplaceApiPermissionsAsync(IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct)
    {
        return client.ResourceServers.UpdateAsync(auth0ApiOptions.ApiIdOrAudience, new ResourceServerUpdateRequest
        {
            Scopes = permissions.Select(x => new ResourceServerScope {Value = x.Name}).ToList()
        }, ct);
    }

    public async Task<IEnumerable<IdentityPermission>> GetAllApiPermissionsAsync(CancellationToken ct)
    {
        ResourceServer api = await client.ResourceServers.GetAsync(auth0ApiOptions.ApiIdOrAudience, ct).ConfigureAwait(false);
        return api.Scopes.Select(x => new IdentityPermission {Name = x.Value});
    }

    public async Task<GetAllRolesResponse> GetAllRolesAsync(CancellationToken ct)
    {
        IPagedList<Role> allRoles = await client.Roles.GetAllAsync(new GetRolesRequest(), new PaginationInfo(0, 100), ct);
        GetAllRolesResponse.Role[] roles = allRoles.Select(r => new GetAllRolesResponse.Role
        {
            AuthId = r.Id,
            Name = r.Name,
        }).ToArray();
        
        return new GetAllRolesResponse {Roles = roles};
    }

    public async Task<CreateRoleResponse> CreateRoleAsync(IdentityRole role, CancellationToken ct)
    {
        Role createdRole = await client.Roles.CreateAsync(new RoleCreateRequest {Name = role.Name}, ct);
        List<PermissionIdentity>? permissions =
            role.Permissions?
                .Select(x => new PermissionIdentity {Identifier = auth0ApiOptions.ApiIdOrAudience, Name = x.Name})
                .ToList();

        if (permissions is {Count: > 0})
        {
            await client.Roles.AssignPermissionsAsync(createdRole.Id, new AssignPermissionsRequest
            {
                Permissions = permissions
            }, ct);
        }

        return new CreateRoleResponse
        {
            Name = createdRole.Name,
            AuthId = createdRole.Id
        };
    }

    public async Task UpdateRoleNameAsync(string authId, string roleName, CancellationToken ct)
    {
        await client.Roles.UpdateAsync(authId, new RoleUpdateRequest {Name = roleName}, ct);
    }

    public async Task<ICollection<IdentityPermission>> GetAllRolesPermissionsAsync(string roleAuthId, CancellationToken ct)
    {
        List<IdentityPermission> rolePermissions = new(200);

        const int perPage = 100;
        // same as perPage to re-run a request if rate limit happens on the first iteration
        int itemsRecentlyRetrieved = perPage;
        int pageNumber = 0;
        int retries = 0;

        do
        {
            try
            {
                IPagedList<Permission> permissions =
                    await client.Roles.GetPermissionsAsync(roleAuthId, new PaginationInfo(pageNumber, perPage), ct);

                rolePermissions.AddRange(permissions.Select(x => new IdentityPermission {Name = x.Name}));
                itemsRecentlyRetrieved = permissions.Count;
                pageNumber++;
            }
            catch (RateLimitApiException rateLimitException)
            {
                if (++retries > MaxRateLimitRetries) throw;
                await rateLimitExceptionHandler.HandleAsync(rateLimitException, ct);
            }
        } while (itemsRecentlyRetrieved == perPage);

        return rolePermissions;
    }

    public async Task RemovePermissionsFromRoleAsync(string roleId, IReadOnlyCollection<IdentityPermission> permissionName, CancellationToken ct)
    {
        bool success = false;
        int retries = 0;
        List<PermissionIdentity> permissions =
            permissionName
                .Select(x => new PermissionIdentity {Name = x.Name, Identifier = auth0ApiOptions.ApiIdOrAudience})
                .ToList();

        do
        {
            try
            {
                await client.Roles.RemovePermissionsAsync(roleId, new AssignPermissionsRequest {Permissions = permissions}, ct);
                success = true;
            }
            catch (RateLimitApiException rateLimitException)
            {
                if (++retries > MaxRateLimitRetries) throw;
                await rateLimitExceptionHandler.HandleAsync(rateLimitException, ct);
            }
        } while (!success);
    }

    public async Task AssignRolePermissionsAsync(string roleAuthId, IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct)
    {
        AssignPermissionsRequest request = new() 
        {
            Permissions = permissions.Select(x => new PermissionIdentity
            {
                Name = x.Name,
                Identifier = auth0ApiOptions.ApiIdOrAudience
            }).ToArray()
        };

        int retries = 0;
        bool success = false;

        do
        {
            try
            {
                await client.Roles.AssignPermissionsAsync(roleAuthId, request, ct);
                success = true;
            }
            catch (RateLimitApiException rateLimitException)
            {
                if (++retries > MaxRateLimitRetries) throw;
                await rateLimitExceptionHandler.HandleAsync(rateLimitException, ct);
            }
        } while (!success); 
    }

    

    private async Task<List<Permission>> RetrieveAllPermissionsFromAuthAsync(string roleAuthId, CancellationToken ct)
    {
        int currentPage = 0;
        List<Permission> allPermissions = new();

        while (true)
        {
            IPagedList<Permission> permissionsPage = await client.Roles.GetPermissionsAsync(
                roleAuthId,
                new PaginationInfo(currentPage, 100),
                ct
            );

            if (permissionsPage.Count is 0)
            {
                break;
            }

            allPermissions.AddRange(permissionsPage);
            currentPage++;

            await Task.Delay(500, ct);
        }

        return allPermissions;
    }
}