using Microsoft.OpenApi.Any;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace Theia.App.Server.Extensions;

public static class SwaggerExtensions
{
    public static IServiceCollection AddSwaggerApi(this IServiceCollection services, IConfiguration configuration)
    {
        OpenApiSecurityScheme securityScheme = new()
        {
            Type = SecuritySchemeType.OAuth2,
            BearerFormat = "JWT",
            Flows = new OpenApiOAuthFlows
            {
                Implicit = new OpenApiOAuthFlow
                {
                    TokenUrl = new Uri($"{configuration["Auth0Api:Authority"]}oauth/token"),
                    AuthorizationUrl = new Uri($"{configuration["Auth0Api:Authority"]}authorize?audience={configuration["Auth0Api:ApiIdOrAudience"]}"),
                    Scopes = new Dictionary<string, string>
                    {
                        { "openid", "OpenId" }
                    }
                },
            }
        };

        OpenApiSecurityRequirement securityRequirement = new()
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "oauth2" },
                },
                ["openid"]
            }
        };

        services.AddSwaggerGen(options =>
        {
            options.OperationFilter<AddRequiredHeaderParameter>();
            options.CustomSchemaIds(type => type.ToString());
            options.SwaggerDoc("v1",
                new OpenApiInfo
                {
                    Version = "v1",
                    Title = "TheiaLens API"
                });

            // Set the comments path for the Swagger JSON and UI.
            //var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            //var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            //options.IncludeXmlComments(xmlPath);

            options.AddSecurityDefinition("oauth2", securityScheme);
            options.AddSecurityRequirement(securityRequirement);
            // using System.Reflection;
            string xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));
        });

        return services;
    }

    public static IApplicationBuilder UseSwaggerApi(this IApplicationBuilder app)
    {
        app.UseSwagger();

        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("./v1/swagger.json", "Theia v1.x.x");
            c.InjectStylesheet("/api/swagger-ui-themes/theme-material.css");
            c.DocExpansion(DocExpansion.List);
        });

        return app;
    }
}

public class AddRequiredHeaderParameter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        operation.Parameters ??= new List<OpenApiParameter>();

        operation.Parameters.Add(new OpenApiParameter
        {
            Name = "X-Tenant", 
            In = ParameterLocation.Header, 
            Schema = new OpenApiSchema {Type = "String"}, 
            Required = false,
            Example = new OpenApiString("{{xTenant}}")
        });
        
        operation.Parameters.Add(new OpenApiParameter
        {
            Name = "Accept-Language", 
            In = ParameterLocation.Header, 
            Schema = new OpenApiSchema {Type = "String"}, 
            Required = false,
            Example = new OpenApiString("en-GB")
        });
    }
}