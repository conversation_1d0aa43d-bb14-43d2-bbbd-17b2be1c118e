namespace Theia.App.Server.Services.Webhooks;

public class WebhookCard
{
    [JsonPropertyName("@type")]
    public string Type { get; } = "MessageCard";

    [JsonPropertyName("@context")]
    public string Context { get; } = "http://schema.org/extensions";
    
    public required string Summary { get; init; }
    public List<WebhookSection> Sections { get; init; }
    public List<WebhookAction> PotentialAction { get; init; }
}

public class WebhookSection
{
    public string ActivityTitle { get; set; }
    public List<WebhookFact> Facts { get; set; }
    public bool Markdown { get; } = true;
}

public class WebhookFact
{
    public string Name { get; set; }
    public string? Value { get; set; }
}

public abstract class WebhookAction
{
    [JsonPropertyName("@type")]
    public string Type { get; protected set; }
    public string Name { get; init; }
    public List<WebhookOpenUriTarget> Targets { get; set; }
}

public class WebhookOpenUriAction : WebhookAction
{
    public WebhookOpenUriAction()
    {
        Type = "OpenUri";
    }
}

public class WebhookOpenUriTarget
{
    public string Os { get; } = "default";
    public string Uri { get; set; }
}