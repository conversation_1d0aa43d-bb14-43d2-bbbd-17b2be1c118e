using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using Theia.Application.Common.Interfaces.Services.IdentityServices;
using Theia.Domain.Entities.Identity;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Services.IdentityServices;

public class PermissionScannerService : IPermissionScannerService
{
    private readonly IApplicationDbContext _dbContext;

    public PermissionScannerService(IApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task ScanBuiltInPermissions()
    {
        List<ApplicationPermission> permissionsToBeDeleted =
            await _dbContext.ApplicationPermissions.IgnoreQueryFilters().ToListAsync();

        _dbContext.ApplicationPermissions.RemoveRange(permissionsToBeDeleted);
        
        ApplicationPermission[] permissions =
            Permissions.Collection
                .Select(c => c.Value.BuildPermissionName())
                .Select(permissionName => new ApplicationPermission {Name = permissionName, HostVisibility = true, TenantVisibility = true})
                .ToArray();

        _dbContext.ApplicationPermissions.AddRange(permissions);

        try
        {
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}