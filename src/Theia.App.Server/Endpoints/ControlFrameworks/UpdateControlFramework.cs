using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Admin.ControlFrameworks.UpdateControlFramework;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ControlFrameworks;

[ApiAuthorize(PermissionType = PermissionTypes.UpdateControlFramework)]
public sealed class
    UpdateControlFramework : EndpointBaseAsync.WithRequest<UpdateControlFrameworkCommand>.WithActionResult
{
    private readonly IApplicationDbContext _dbContext;

    public UpdateControlFramework(IApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpPut("api/control-frameworks/updateControlFramework")]
    public override async Task<ActionResult> HandleAsync(
        UpdateControlFrameworkCommand request, CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid) throw new ApiProblemDetailsException(ModelState);

        bool cfWithNameExists =
            _dbContext.ControlFrameworks
                .Any(a => a.Name.ToLower().Trim() == request.Name.ToLower().Trim() && a.Id != request.Id);

        if (cfWithNameExists)
        {
            throw new ApiProblemDetailsException(Resource.Control_Framework_already_exist,
                statusCode: HttpStatusCode.BadRequest.ToInt());
        }

        int updated = await _dbContext.ControlFrameworks
            .Where(f => f.Id == request.Id)
            .ExecuteUpdateAsync(
                setters => setters
                    .SetProperty(f => f.Name, request.Name)
                    .SetProperty(f => f.Description, request.Description)
                    .SetProperty(f => f.IsActive, request.IsActive)
                    .SetProperty(f => f.Type, request.Type), cancellationToken)
            .ConfigureAwait(true);

        if (updated == 0)
        {
            throw new ApiProblemDetailsException(Resource.Unable_to_load_Control_Framework,
                statusCode: HttpStatusCode.NotFound.ToInt());
        }

        return Ok();
    }
}