using Ardalis.ApiEndpoints;
using Theia.Application.Common.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Admin.ControlFrameworks.GetControlFrameworks;
using Theia.Domain.Entities.ControlFrameworks;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ControlFrameworks;

[ApiAuthorize(PermissionType = PermissionTypes.GetControlFrameworks)]
public sealed class GetControlFrameworks : EndpointBaseAsync.WithRequest<GetControlFrameworksQuery>.WithResult<
    ActionResult<GetControlFrameworksResponse>>
{
    private readonly IApplicationDbContext _dbContext;

    public GetControlFrameworks(IApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpPost("api/control-frameworks/GetControlFrameworks")]
    public override async Task<ActionResult<GetControlFrameworksResponse>> HandleAsync(
        GetControlFrameworksQuery request,
        CancellationToken cancellationToken = new())
    {
        IQueryable<ControlFramework> query = _dbContext.ControlFrameworks.AsQueryable();

        if (!string.IsNullOrWhiteSpace(request.SearchText))
            query = query.Where(a => a.Name.Contains(request.SearchText));

        query = !string.IsNullOrWhiteSpace(request.SortBy)
            ? query.SortBy(request.SortBy)
            : query.OrderBy(a => a.Name);

        PagedList<GetControlFrameworksResponseItem> controlFrameworkItems =
            await GetControlFrameworkItemsAsync(request, query)
                .ConfigureAwait(true);
        
        GetControlFrameworksResponse controlFrameworksResponse = new(controlFrameworkItems);

        return Ok(controlFrameworksResponse);
    }

    private static Task<PagedList<GetControlFrameworksResponseItem>> GetControlFrameworkItemsAsync(
        IPagingRequest request,
        IQueryable<ControlFramework> query)
    {
        return query
            .Select(cf => new GetControlFrameworksResponseItem((WebSafeGuid)cf.Id, cf.Name, cf.Description, cf.IsActive))
            .ToPagedListAsync(request.PageNumber, request.RowsPerPage);
    }
}