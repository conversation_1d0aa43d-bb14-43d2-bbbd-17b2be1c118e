using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Suppliers.SupplierSubmissionRequests;
using Theia.Application.Common.Extensions;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.Submissions;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.SupplierSubmissionRequests;

[ApiAuthorize(PermissionType = PermissionTypes.ViewSupplierSubmissionRequests)]
public class AssociateFile(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor) 
    : EndpointBaseAsync.WithRequest<AssociateFileRequest>.WithoutResult
{
    [HttpPost("api/submission-requests/files/associate")]
    public override async Task HandleAsync(AssociateFileRequest request, CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.EnsureAuthId();

        SupplierSubmissionRequestFile? submissionFile =
            await dbContext.Users
                .Where(x => x.Id == currentUserId)
                .SelectMany(x => x.Supplier.SubmissionRequests)
                .Where(x => x.Status == SupplierSubmissionRequestStatus.Incomplete)
                .SelectMany(x => x.Files)
                .SingleOrDefaultAsync(x => x.Id == request.FileRequestId, cancellationToken)
                .ConfigureAwait(false);

        if (submissionFile is null)
        {
            throw new ApiProblemDetailsException(Resource.File_not_found, StatusCodes.Status404NotFound);
        }

        if (request.SupplierFileId == Guid.Empty)
        {
            await RefreshPreviousFileInUse(request, submissionFile, cancellationToken).ConfigureAwait(false);
            submissionFile.AssociateFile(null); 
            await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
            return;
        }
        
        SupplierFile? supplierFile =
            await dbContext.SupplierFiles
                .Where(x => !x.IsDeleted)
                .SingleOrDefaultAsync(x => x.Id == request.SupplierFileId, cancellationToken)
                .ConfigureAwait(false);

        if (supplierFile is null)
        {
            throw new ApiProblemDetailsException(Resource.File_not_found, StatusCodes.Status404NotFound);
        }

        await RefreshPreviousFileInUse(request, submissionFile, cancellationToken).ConfigureAwait(false);
        submissionFile.AssociateFile(supplierFile.Id);
        supplierFile.InUse = true;

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }

    private async Task RefreshPreviousFileInUse(
        AssociateFileRequest request,
        SupplierSubmissionRequestFile submissionFile,
        CancellationToken ct)
    {
        Guid? previousAssociatedFileId = submissionFile.AssociatedSupplierFileId;
        if (previousAssociatedFileId.HasValue && previousAssociatedFileId.Value != request.SupplierFileId)
        {
            SupplierFile? previousFile = 
                await dbContext.SupplierFiles
                    .Include(x => x.AssociatedSubmissionFiles)
                    .SingleOrDefaultAsync(x => x.Id == previousAssociatedFileId, ct)
                    .ConfigureAwait(false);

            if (previousFile is not null)
            {
                previousFile.InUse = previousFile.AssociatedSubmissionFiles?.Any(x => x.Id != submissionFile.Id) ?? false;
            }
        }
    }
}