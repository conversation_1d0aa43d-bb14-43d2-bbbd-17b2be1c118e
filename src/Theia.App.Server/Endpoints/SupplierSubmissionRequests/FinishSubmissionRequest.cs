using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.Application.Common.Extensions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Suppliers.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.SupplierSubmissionRequests;

[ApiAuthorize(PermissionType = PermissionTypes.ViewSupplierSubmissionRequests)]
public class FinishSubmissionRequest(
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        TimeProvider timeProvider)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpPost("api/submission-requests/{requestId:required}/finish")]
    public override async Task HandleAsync([FromRoute] WebSafeGuid requestId, CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.GetAuthId();

        SupplierSubmissionRequest? submissionRequest =
            await dbContext.Users
                .Where(x => x.Id == currentUserId)
                .SelectMany(x => x.Supplier.SubmissionRequests)
                .Where(x => x.Status == SupplierSubmissionRequestStatus.Incomplete)
                .Include(x => x.ApplicationFormVersions)
                .ThenInclude(afv => afv.SupplierApplicationFormVersion)
                .ThenInclude(fv => fv.Snapshots)
                .Include(x => x.Files)
                .SingleOrDefaultAsync(x => x.Id == requestId, cancellationToken)
                .ConfigureAwait(false);

        if (submissionRequest is null)
        {
            throw new ApiProblemDetailsException(Resource.Submission_Request_Not_Found, StatusCodes.Status404NotFound);
        }

        await RemoveSupplierFromUnsubmittedSubmissions(submissionRequest, currentUserId, cancellationToken).ConfigureAwait(false);

        var forms =
            submissionRequest.ApplicationFormVersions
                .Select(x => new
                {
                    AppFormVersionId = x.SupplierApplicationFormVersion.Id,
                    LatestSnapshot = x.SupplierApplicationFormVersion.Snapshots.MaxBy(s => s.CreatedOnUtc)
                });

        bool allFormsHaveSnapshot = forms.All(x => x.LatestSnapshot is not null);
        if (!allFormsHaveSnapshot)
        {
            throw new ApiProblemDetailsException(Resource.Submission_request_cant_be_finished, StatusCodes.Status409Conflict);
        }

        IEnumerable<SupplierSubmissionApplicationFormVersionSnapshot> snapshots =
            forms.Select(f => new SupplierSubmissionApplicationFormVersionSnapshot
            {
                SupplierSubmissionId = Guid.Empty,
                SupplierSubmission = submissionRequest,
                SupplierApplicationFormVersionSnapshotId = f.LatestSnapshot!.Id,
                SupplierApplicationFormVersionSnapshot = null
            });
        
        submissionRequest.Complete(timeProvider.GetUtcNow(), snapshots);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }

    private async Task RemoveSupplierFromUnsubmittedSubmissions(SupplierSubmissionRequest submissionRequest, string currentUserId, CancellationToken cancellationToken)
    {
        SubmissionSupplier[] toRemove =
            await dbContext.SubmissionSupplier
                .Where(x =>
                    x.Submission.Status == SubmissionStatus.Unsubmitted
                    && x.SupplierId == dbContext.Users.SingleOrDefault(u => u.Id == currentUserId).SupplierId
                    && x.Submission.RequestedForOrganisationId == submissionRequest.OrganisationId)
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(false);

        dbContext.SubmissionSupplier.RemoveRange(toRemove);
    }
}