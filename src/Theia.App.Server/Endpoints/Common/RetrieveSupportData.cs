using Ardalis.ApiEndpoints;
using Theia.App.Shared.Dtos;

namespace Theia.App.Server.Endpoints.Common;

public class RetrieveSupportData(
        IConfiguration configuration)
    : EndpointBaseSync.WithoutRequest.WithResult<SupportDataResponse>
{
    [HttpGet("api/support/data")]
    public override SupportDataResponse Handle()
    {
        string supportEmail = configuration["ClientApp:TheiaLensSupportEmail"] ?? string.Empty;

        return new SupportDataResponse
        {
            SupportEmail = supportEmail
        };
    }
}