using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Layers;
using Theia.Application.Services.Layers;
using Theia.Domain.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Layers;

[ApiAuthorize(PermissionType = PermissionTypes.ReadLayers)]
public class GetAgreedOption(IApplicationDbContext dbContext, LayerPermissionService layerPermissionService)
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<GetAgreedOptionResponse>
{
    [HttpGet("api/layers/{layerId:guid:required}/agreed-option")]
    public override async Task<GetAgreedOptionResponse> HandleAsync(Guid layerId, CancellationToken cancellationToken = new())
    {
        await layerPermissionService.CheckOrThrowAsync(layerId, cancellationToken).ConfigureAwait(false);
        
        GetAgreedOptionResponse? agreedOption =
            await dbContext.Layers
                .Where(layer => layer.Id == layerId)
                .SelectMany(layer => layer.IndicationRequests)
                .SelectMany(indicationRequest => indicationRequest.Indications)
                .SelectMany(indication => indication.IndicationOptions!)
                .Where(option => option.IsSelected)
                .Select(option => new GetAgreedOptionResponse
                {
                    IndicationDate = option.Indication!.IndicatedDate,
                    InsuredName = option.Indication!.IndicationRequest!.Layer!.Submission!.RequestedForOrganisation!.Name,
                    InsurerName = option.Indication!.Insurer!.Name,
                    UnderwriterName = option.Indication!.CreatedFor!.FullName,
                    PolicyAggregateLimit = option.LimitOfLiability,
                    Premium = option.Premium,
                    InsurerLine = option.InsurerLine,
                    Currency = option.Indication!.IndicationRequest!.Layer!.Currency,
                    IndicationRequestId = option.Indication!.IndicationRequest!.Id,
                    IndicationId = option.Indication!.Id,
                    SelectedOptionId = option.Id
                })
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (agreedOption is null)
        {
            throw new ApiProblemDetailsException(Resource.No_agreed_option_found_for_this_layer, StatusCodes.Status404NotFound);
        }

        return agreedOption;
    }
}
