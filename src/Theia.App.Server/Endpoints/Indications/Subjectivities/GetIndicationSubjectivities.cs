using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Indications.Subjectivities;
using Theia.Application.Services.Indications;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Subjectivities;

[ApiAuthorize(PermissionType = PermissionTypes.ReadIndications)]
public class GetIndicationSubjectivities(
    IApplicationDbContext dbContext,
    IndicationsPermissionService indicationsPermissionService)
    : EndpointBaseAsync.WithRequest<Guid>.WithActionResult<GetIndicationSubjectivitiesResponse>
{
    [HttpGet("api/quotes/{quoteId:guid:required}/subjectivities")]
    public override async Task<ActionResult<GetIndicationSubjectivitiesResponse>> HandleAsync(
        [FromRoute] Guid quoteId,
        CancellationToken cancellationToken = new())
    {
        await indicationsPermissionService.CheckReadAccessOrThrowAsync(quoteId, cancellationToken).ConfigureAwait(false);

        List<GetIndicationSubjectivitiesResponseItem> items =
            await dbContext.Subjectivities
                .Where(x => x.IndicationId == quoteId)
                .OrderBy(x => x.Name)
                .Select(x => new GetIndicationSubjectivitiesResponseItem {Id = x.Id, Name = x.Name, IsSatisfied = x.Satisfied})
                .ToListAsync(cancellationToken)
                .ConfigureAwait(false);

        return new GetIndicationSubjectivitiesResponse {Items = items};
    }
}