using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Indications.Subjectivities;
using Theia.Application.Services.Indications;
using Theia.Application.Services.Subjectivities;
using Theia.Domain.Entities.Indications;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Subjectivities;

[ApiAuthorize(PermissionType = PermissionTypes.IndicateIndications)]
public class UpdateSubjectivity(
    IApplicationDbContext dbContext,
    SubjectivitiesPermissionService subjectivitiesPermissionService)
    : EndpointBaseAsync.WithRequest<UpdateSubjectivityRequest>.WithoutResult
{
    [HttpPut("api/quotes/subjectivities")]
    public override async Task HandleAsync(UpdateSubjectivityRequest request, CancellationToken cancellationToken = new())
    {
        await subjectivitiesPermissionService.EnsureWritePermissionAsync(request.IndicationId, cancellationToken).ConfigureAwait(false);

        Subjectivity? subjectivity =
            await dbContext.Subjectivities
                .SingleOrDefaultAsync(x => 
                    x.Id == request.SubjectivityId &&
                    x.IndicationId == request.IndicationId, cancellationToken)
                .ConfigureAwait(false);

        if (subjectivity is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        subjectivity.Satisfied = request.IsSatisfied;
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}
