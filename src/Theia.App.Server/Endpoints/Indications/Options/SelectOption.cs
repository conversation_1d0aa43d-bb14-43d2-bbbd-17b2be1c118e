using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using OneOf;
using OneOf.Types;
using Theia.App.Shared.Indications.Options;
using Theia.Application.Services.Indications;
using Theia.Domain.Entities.Indications;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.Wholesale;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Options;

[ApiAuthorize(PermissionType = PermissionTypes.SelectOption)]
public class SelectOption(
    IApplicationDbContext dbContext,
    IndicationsPermissionService indicationsPermissionService) 
    : EndpointBaseAsync.WithRequest<SelectOptionRequest>.WithoutResult
{
    [HttpPut("api/quotes/options")]
    public override async Task HandleAsync(SelectOptionRequest request, CancellationToken cancellationToken = new())
    {
        await indicationsPermissionService.CheckWriteAccessOrThrowAsync(request.IndicationId, cancellationToken).ConfigureAwait(false);

        Indication? indication =
            await dbContext.Indications
                .Where(x => x.Id == request.IndicationId)
                .Include(x => x.IndicationOptions)
                .Include(x => x.IndicationRequest)
                .ThenInclude(x => x.Layer)
                .ThenInclude(x => x.Submission)
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (indication is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        Submission? submission = indication.IndicationRequest?.Layer?.Submission;

        if (submission is null)
        {
            throw new ApiProblemDetailsException(Resource.Submission_not_found, StatusCodes.Status404NotFound);
        }
        
        OneOf<Success, Error<string>> selectOption = indication.ChangeOptionSelection(request.OptionId, request.IsSelected);
        selectOption.Switch(_ => { }, error => throw new ApiProblemDetailsException(error.Value, StatusCodes.Status409Conflict));

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}