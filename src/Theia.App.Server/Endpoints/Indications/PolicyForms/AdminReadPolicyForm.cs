using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Admin.PolicyForms.ReadForm;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.PolicyForms;

[ApiAuthorize(PermissionType = PermissionTypes.WritePolicyForms)]
public class AdminReadPolicyForm(IApplicationDbContext dbContext)
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<AdminReadPolicyFormResponse>
{
    [HttpGet("api/admin/policy-forms/{policyFormId:guid:required}")]
    public override async Task<AdminReadPolicyFormResponse> HandleAsync(Guid policyFormId, CancellationToken cancellationToken = new())
    {
        AdminReadPolicyFormResponse? response =
            await dbContext.PolicyForms
                .Where(x => x.Id == policyFormId)
                .Select(x => new AdminReadPolicyFormResponse
                {
                    Id = x.Id, Name = x.Name, IsActive = x.IsActive, CreatedOn = x.CreatedOn,
                    HeadsOfCover =
                        x.HeadsOfCover
                            .Select(y => new AdminReadPolicyFormHeadOfCover
                            {
                                Id = y.Id, Name = y.Name, Section = y.Section, HasWaitingPeriod = y.HasWaitingPeriod
                            })
                            .ToArray(),
                    FileName = x.FileName
                })
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (response is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        return response;
    }
}