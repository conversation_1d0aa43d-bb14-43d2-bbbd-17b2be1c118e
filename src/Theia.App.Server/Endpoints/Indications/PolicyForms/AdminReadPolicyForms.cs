using Ardalis.ApiEndpoints;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Admin.PolicyForms.ReadForms;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.PolicyForms;

[ApiAuthorize(PermissionType = PermissionTypes.WritePolicyForms)]
public class AdminReadPolicyForms(IApplicationDbContext dbContext) 
    : EndpointBaseAsync.WithRequest<DataSourceRequest>.WithResult<DataEnvelope<AdminReadPolicyFormsResponse>>
{
    [HttpPost("api/admin/policy-forms/get")]
    public override async Task<DataEnvelope<AdminReadPolicyFormsResponse>> HandleAsync(DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        DataSourceResult result =
            await dbContext.PolicyForms
                .Select(x => new AdminReadPolicyFormsResponse
                {
                    Id = x.Id, Name = x.Name, IsActive = x.IsActive, CreatedOn = x.CreatedOn,
                    FileName = x.FileName
                })
                .ToDataSourceResultAsync(request)
                .ConfigureAwait(false);

        return DataEnvelope<AdminReadPolicyFormsResponse>.FromDataSourceResult(result, request);
    }
}