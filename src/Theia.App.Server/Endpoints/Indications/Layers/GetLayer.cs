using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Layers;
using Theia.Application.Common.Enums;
using Theia.Application.Services;
using Theia.Application.Services.Layers;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Layers;

[ApiAuthorize(PermissionType = PermissionTypes.ReadLayer)]
public class GetLayer(
    IApplicationDbContext dbContext,
    LayerPermissionService layerPermissionService,
    IRoleService roleService) 
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<GetLayerResponse>
{
    [HttpGet("api/submissions/layers/{id:guid:required}")]
    public override async Task<GetLayerResponse> HandleAsync(Guid id, CancellationToken cancellationToken = new())
    {
        AccessLevel accessLevel = await layerPermissionService.GetAccessLevelAsync(id, cancellationToken);
        accessLevel.ThrowIfNoAccess(AccessLevel.Read);
        bool canEdit = accessLevel == AccessLevel.Write;

        bool indicationRequestsVisible = roleService.IsUserInBrokerRole || roleService.IsUserInOrganisationRole;

        GetLayerResponse? response =
            await dbContext.Layers
                .Select(x => new GetLayerResponse
                {
                    Id = x.Id,
                    Limit = x.Limit,
                    Excess = x.Excess,
                    Currency = x.Currency,
                    TargetPremium = x.TargetPremium,
                    IsSharedWithOrg = x.IsSharedWithOrg,
                    IsShareableWithOrg = x.Submission!.SubmissionType == SubmissionType.OrganisationSubmission,
                    Index =
                        dbContext.Layers
                            .Count(y =>
                                y.SubmissionId == x.SubmissionId &&
                                EF.Property<DateTimeOffset>(y,
                                    ShadowProperties.CreatedOn) <
                                EF.Property<DateTimeOffset>(x,
                                    ShadowProperties.CreatedOn)),
                    IndicationRequests =
                        indicationRequestsVisible
                            ? x.IndicationRequests!
                                .Select(y => new GetLayerResponseIndicationRequest
                                {
                                    Id = y.Id,
                                    Name = y.SubmissionName,
                                    Responded = 
                                        y.Indications!.Count(i => i.Status == IndicationStatus.Indicated)
                                        + dbContext.WholesaleSubmissions.Count(w => w.IndicationRequestId == y.Id && w.IsVisibleAsCompletedToRetail()),
                                    Total =
                                        y.Indications!.Count(i => i.Status == IndicationStatus.AwaitingResponse || i.Status == IndicationStatus.Indicated)
                                        + dbContext.WholesaleSubmissions.Count(w => w.IndicationRequestId == y.Id && w.HasBeenSentBackToBroker)
                                })
                                .ToArray()
                            : Array.Empty<GetLayerResponseIndicationRequest>(),
                    HasOptionSelected = 
                        x.IndicationRequests!
                            .SelectMany(y => y.Indications!)
                            .SelectMany(i => i.IndicationOptions!)
                            .Any(o => o.IsSelected),
                    CanEdit = canEdit
                })
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken)
                .ConfigureAwait(false);

        if (response is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        return response;
    }
}