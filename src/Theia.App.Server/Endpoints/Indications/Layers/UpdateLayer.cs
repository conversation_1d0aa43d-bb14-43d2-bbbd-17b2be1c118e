using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Layers;
using Theia.Application.Services.Layers;
using Theia.Domain.Entities.Indications;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Layers;

[ApiAuthorize(PermissionType = PermissionTypes.WriteLayers)]
public class UpdateLayer(
    IApplicationDbContext dbContext,
    LayerPermissionService layerPermissionService) 
    : EndpointBaseAsync.WithRequest<UpdateLayerRequest>.WithoutResult
{
    [HttpPut("api/submissions/layers")]
    public override async Task HandleAsync(UpdateLayerRequest request, CancellationToken cancellationToken = new())
    {
        await layerPermissionService.CheckOrThrowAsync(request.LayerId, cancellationToken).ConfigureAwait(false);
        
        Layer? layer = 
            await dbContext.Layers
                .Include(x => x.Submission)
                .SingleOrDefaultAsync(x => x.Id == request.LayerId, cancellationToken)
                .ConfigureAwait(false);
        
        if (layer is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        bool succeeded = layer.ChangeVisibility(request.IsSharedWithOrg);

        if (!succeeded)
        {
            throw new ApiProblemDetailsException(Resource.Unable_to_change_visibility_of_a_wholesale_submission_s_layer, StatusCodes.Status409Conflict);
        }
        
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}