using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Layers;
using Theia.App.Shared.Submissions.ProgramStack.GetSubmissionProgramStack;
using Theia.Application.Services.Layers;
using Theia.Application.Services.Layers.LayerPlacement;
using Theia.Domain.Entities.Indications;
using Theia.Domain.Entities.Organisations.Submissions.Wholesale;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Indications.Layers;

[ApiAuthorize(PermissionType = PermissionTypes.ReadLayers)]
public class GetLayerPlacement(
    LayerPermissionService layerPermissionService,
    PlacementService placementService)
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<GetPlacementResponse>
{
    [HttpGet("api/submissions/layers/{layerId:guid:required}/placement")]
    public override async Task<GetPlacementResponse> HandleAsync(
        [FromRoute] Guid layerId,
        CancellationToken cancellationToken = new())
    {
        await layerPermissionService.CheckOrThrowAsync(layerId, cancellationToken).ConfigureAwait(false);
        
        GetPlacementResponse response = await placementService.GetLayerPlacementAsync(layerId, cancellationToken);
        
        return response;
    }
}
