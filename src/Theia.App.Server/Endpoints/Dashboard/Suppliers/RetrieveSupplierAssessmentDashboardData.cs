using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Models;
using Theia.Application.Services;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Dashboard.Suppliers;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class RetrieveSupplierAssessmentDashboardData(
        IApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService,
        IHttpContextAccessor httpContextAccessor,
        IRoleService roleService,
        SupplierFormSnapshotPermissionService supplierFormSnapshotPermissionService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithResult<DashboardDataBasicSubmissionData>
{
    [HttpGet("api/suppliers/get-basic-assessment-data/{request}")]
    public override async Task<DashboardDataBasicSubmissionData> HandleAsync(
        [FromRoute] WebSafeGuid request, CancellationToken cancellationToken = new())
    {
        Guid currentUserSupplierId = await tenantResolverService.GetDomainTenantSupplierIdAsync(
            httpContextAccessor.HttpContext!, dbContext).ConfigureAwait(false);

        var supplierApplicationForm =
            await supplierFormSnapshotPermissionService
                .GetAvailableSnapshotsToDisplay()
                .Where(x => x.Id == request)
                .Select(x => new
                {
                    x.SupplierApplicationFormVersion.SupplierId,
                    x.CompletedByUser,
                    x.CreatedOnUtc
                })
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (supplierApplicationForm is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        if (currentUserSupplierId != supplierApplicationForm.SupplierId
            && !(roleService.IsUserInOrganisationRole
                 || roleService.IsUserInBrokerRole
                 || roleService.IsUserInInsurerRole))
        {
            throw new ApiProblemDetailsException(Resource.You_are_forbidden, HttpStatusCode.Forbidden.ToInt());
        }

        return new DashboardDataBasicSubmissionData
        {
            AssessmentDate =
                supplierApplicationForm.CreatedOnUtc.ToString(DefaultSettings.DateFormatConstants.DateFormat),
            SignedOffBy =
                $"{supplierApplicationForm.CompletedByUser?.Name} {supplierApplicationForm.CompletedByUser?.Surname}"
        };
    }
}