using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Dtos;
using Theia.App.Shared.Models;
using Theia.Domain.Entities;
using Theia.Domain.Entities.Reporting;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;
using SupplierApplicationFormVersionSnapshot = Theia.Domain.Entities.Suppliers.ApplicationForms.SupplierApplicationFormVersionSnapshot;

namespace Theia.App.Server.Endpoints.Dashboard.Suppliers;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class RetrieveSupplierControlFrameworkCategoryAnalysisDetail(
        IApplicationDbContext dbContext)
    : EndpointBaseAsync.WithRequest<RetrieveControlFrameworkCategoryDashboardDetails>
        .WithResult<DataState>
{
    [HttpPost("api/suppliers/retrieve-control-framework-details")]
    public override async Task<DataState> HandleAsync(RetrieveControlFrameworkCategoryDashboardDetails request,
        CancellationToken cancellationToken = new())
    {
        IQueryable<AnalysedControlFrameworkCategoryClause> analysedClausesQuery =
            dbContext.AnalysedControlFrameworkCategoryClauses
                .Where(clause =>
                    clause.AnalysedControlFrameworkCategory.ControlFrameworkCategoryId == request.ControlFrameworkCategoryId
                    && clause.SupplierApplicationFormVersionSnapshotId == request.SnapshotQuoteOrSubmissionId
                    && clause.AnalysedControlFrameworkCategory.AnalysedControlFramework.TheiaAnalysisJobId != null
                    && clause.AnalysedControlFrameworkCategory.AnalysedControlFramework.SupplierApplicationFormVersionSnapshotId == request.SnapshotQuoteOrSubmissionId
                    && clause.AnalysedControlFrameworkCategory.AnalysedControlFramework.SupplierApplicationFormVersionSnapshot.TheiaAnalysisJob.Status == JobStatus.Complete);

        Dictionary<ControlFrameworkSatisfactoryState, DataSubState[]> clauses =
            await (from clause in dbContext.ControlFrameworkCategoryClauses.Where(clause => clause.ControlFrameworkCategoryId == request.ControlFrameworkCategoryId)
                    join analysed in analysedClausesQuery on clause.Id equals analysed.ControlFrameworkCategoryClauseId into groupedAnalysed
                    from gAnalysed in groupedAnalysed.DefaultIfEmpty()
                    let satisfactoryState = gAnalysed == null ? ControlFrameworkSatisfactoryState.NotAnswered : gAnalysed.SatisfactoryState
                    group new {DataSubState = new DataSubState {Name = clause.Name, Text = clause.Description}, SatisfactoryState = satisfactoryState} by satisfactoryState
                    into groupedByState
                    select new {SatisfactoryState = groupedByState.Key, Items = groupedByState.Select(g => g.DataSubState).ToArray()})
                .ToDictionaryAsync(
                    x => x.SatisfactoryState,
                    x => x.Items,
                    cancellationToken)
                .ConfigureAwait(false);

        DataSubState[]? notAnswered = clauses.GetValueOrDefault(ControlFrameworkSatisfactoryState.NotAnswered);
        DataSubState[]? noPath = clauses.GetValueOrDefault(ControlFrameworkSatisfactoryState.NoPath);

        DataState response = new()
        {
            Satisfactory = clauses.GetValueOrDefault(ControlFrameworkSatisfactoryState.Satisfied), 
            AreasOfImprovement = clauses.GetValueOrDefault(ControlFrameworkSatisfactoryState.Dissatisfied), 
            Informational = clauses.GetValueOrDefault(ControlFrameworkSatisfactoryState.ZeroWeightingInformational), 
            Unanswered = notAnswered is null ? noPath : notAnswered.Concat(noPath ?? []).ToArray()
        };

        return response;
    }
}