using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Contracts.AddContractRequest;
using Theia.Domain.Entities;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Organisations.Profile;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Contracts;

[ApiAuthorize(PermissionType = PermissionTypes.ManageOrgProfile)]
public class AddContract : EndpointBaseAsync.WithRequest<AddContractRequest>.WithoutResult
{
    private readonly IApplicationDbContext _dbContext;
    private readonly Guid? _tenantId;

    public AddContract(IApplicationDbContext dbContext, ITenantResolverService tenantResolverService)
    {
        _dbContext = dbContext;
        _tenantId = tenantResolverService.GetTenantId();
    }

    [HttpPost("api/organisations/contracts")]
    public override async Task HandleAsync(AddContractRequest request, CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid)
        {
            throw new ApiProblemDetailsException(ModelState);
        }
        
        var org =
            await _dbContext.Organisations
                .Where(o => o.TenantId == _tenantId)
                .Select(o => new
                {
                    o.Id,
                    ContractsCount = o.Contracts.Count,
                    IndustryExists = _dbContext.Industries.Any(i => i.Id == request.IndustryId)
                })
                .FirstOrDefaultAsync(cancellationToken);

        if (org is null)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        }

        if (!org.IndustryExists)
        {
            throw new ApiProblemDetailsException(Resource.Industry_not_found, HttpStatusCode.NotFound.ToInt());
        }
        
        if (org.ContractsCount >= 5)
        {
            throw new ApiProblemDetailsException(Resource.Only_5_contracts_are_required, HttpStatusCode.UnprocessableEntity.ToInt());
        }

        Contract contract = new()
        {
            IndustryId = request.IndustryId,
            Industry = null,
            Duration = request.Duration,
            ClientName = request.ClientName,
            ContractValue = request.ContractValue,
            ProductServiceName = request.ProductServiceName,
            OrganisationId = org.Id,
            Organisation = null
        };

        _dbContext.Contracts.Add(contract);
        
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}