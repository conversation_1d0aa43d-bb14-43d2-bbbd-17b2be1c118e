using Ardalis.ApiEndpoints;
using Theia.App.Shared.Admin.BrokingHouses.AddBrokingHouse;
using Theia.Application.Common.Interfaces.UseCases.Tenants;
using Theia.Domain.Entities;
using Theia.Domain.Entities.BrokingHouses;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.BrokingHouses;

[ApiAuthorize(PermissionType = PermissionTypes.AddBrokingHouse)]
public class AddBrokingHouse(IApplicationDbContext dbContext, ITenantUseCase tenantUseCase)
    : EndpointBaseAsync.WithRequest<AddBrokingHouseRequest>.WithoutResult
{
    [HttpPost("api/admin/broking-houses")]
    public override async Task HandleAsync(AddBrokingHouseRequest request, CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid)
        {
            throw new ApiProblemDetailsException(ModelState);
        }
        
        CreateTenantCommand command = new() {Name = request.Subdomain, Type = TenantType.BrokingHouse};
        Tenant tenant = await tenantUseCase.CreateTenantAsync(command, cancellationToken).ConfigureAwait(true);

        BrokingHouse bh =
            new()
            {
                Name = request.BrokingHouse,
                ContactName = request.ContactName,
                Email = request.Email,
                Phone = request.Phone,
                Active = request.Active,
                Tenant = tenant
            };

        dbContext.BrokingHouses.Add(bh);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}