using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Admin.BrokingHouses.UpdateBrokingHouse;
using Theia.Application.Services;
using Theia.Application.Services.TenantHostname;
using Theia.Domain.Entities.BrokingHouses;
using Theia.Domain.Entities.Identity;
using Theia.Identity.Interface;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.BrokingHouses;

[ApiAuthorize(PermissionType = PermissionTypes.UpdateBrokingHouse)]
public class UpdateBrokingHouse(ApplicationDbContext dbContext, IUserService userService) 
    : EndpointBaseAsync.WithRequest<UpdateBrokingHouseRequest>.WithoutResult
{
    [HttpPut("api/admin/broking-houses")]
    public override async Task HandleAsync(UpdateBrokingHouseRequest request,
        CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid)
        {
            throw new ApiProblemDetailsException(ModelState);
        }
        
        string trimmedSubdomain = request.Subdomain.Trim();
        if (await dbContext.Tenants
                .AnyAsync(o => 
                    o.Name.ToLower() == trimmedSubdomain.ToLower() 
                    && o.BrokingHouse.Id != request.Id, cancellationToken)
                .ConfigureAwait(true))
        {
            throw new ApiProblemDetailsException(Resource.Tenant_with_this_subdomain_already_exists, HttpStatusCode.Conflict.ToInt());
        }
        
        BrokingHouse? brokingHouse = await dbContext
            .BrokingHouses
            .Include(o => o.Tenant)
            .Where(o => o.Id == request.Id)
            .SingleOrDefaultAsync(cancellationToken)
            .ConfigureAwait(true);

        if (brokingHouse is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        brokingHouse.Name = request.BrokingHouse;
        brokingHouse.ContactName = request.ContactName;
        brokingHouse.Email = request.Email;
        brokingHouse.Phone = request.Phone;
        brokingHouse.Tenant.Name = trimmedSubdomain;

        if (!brokingHouse.Active && request.Active)
        {
            ApplicationUser? newUser =
                await userService.AddFirstTenantUserAsync(new User
                {
                    Email = request.Email,
                    RoleName = RolesConstant.BrokingHouseAdmin,
                    TenantType = TenantType.BrokingHouse,
                    TenantId = brokingHouse.Tenant.Id,
                    Tenant = brokingHouse.Tenant
                }, cancellationToken).ConfigureAwait(false);

            if (newUser is not null)
                dbContext.Users.Add(newUser);
        }
        
        brokingHouse.Active = request.Active;

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}