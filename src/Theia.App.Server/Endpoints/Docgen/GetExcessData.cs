using Ardalis.ApiEndpoints;
using System.Net;
using Theia.App.Shared.DocGen;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Docgen;

[ApiAuthorize(PermissionType = PermissionTypes.GetOrgProfile)]
public class GetExcessData(
        IApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService) :
    EndpointBaseAsync.WithRequest<Guid>.WithResult<ExcessDataRequestResponse>
{
    [HttpGet("api/doc-gen/excess-data/{excessId}")]
    public override async Task<ExcessDataRequestResponse> HandleAsync(
        [FromRoute] Guid excessId,
        CancellationToken cancellationToken = new())
    {
        // Business logic section for retrieving and processing data
        // Add your data retrieval and processing code here
        // Speak to <PERSON>ugo regarding whether this is an Indication or not???????????


        // If no data found, throw exception
        if (false /* replace with actual condition */)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        }

        ExcessDataRequestResponse response = new()
        {
            __CommunicationMethod__ = "1",
            __ContextId__ = "",
            __FileNamePrefix__ = "Theia",
            __FileNameEssentialId__ = "",
            __FileNameEssentialName__ = "Excess_1_0_0",
            __DocumentFormat__ = "2",
            __TeamsChannelKey__ = "",
            __DocumentType__ = "Excess",
            __TemplateName__ = "Theia-Excess-Data_1_0_0.cshtml",
            NamedInsured = "",
            Domicile = "",
            Revenue = "",
            BrokerContact = "",
            Brokerage = "",
            Inception = "",
            RetroactiveDate = "",
            PolicyForm = "",
            Currency = "",
            EndorsementsExclusions = new List<string>(),
            Subjectivities = new List<string>(),
            Options = new List<Option>()
        };

        return response;
    }
}
