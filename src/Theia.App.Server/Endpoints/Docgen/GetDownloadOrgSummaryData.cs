using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.DocGen;
using Theia.App.Shared.Extensions;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Organisations.Profile.Finances;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Docgen;

[ApiAuthorize(PermissionType = PermissionTypes.GetOrgProfile)]
public class GetDownloadOrgSummaryData(IApplicationDbContext dbContext, ITenantResolverService tenantResolverService) :
    EndpointBaseAsync
    .WithRequest<WebSafeGuid?>
    .WithResult<GetDownloadOrgSummaryDataRequestResponse>
{
    [HttpGet("api/doc-gen/org-summary/{orgId?}")]
    public override async Task<GetDownloadOrgSummaryDataRequestResponse> HandleAsync(
        [FromRoute] WebSafeGuid? orgId,
        CancellationToken cancellationToken = new())
    {
        IQueryable<Organisation> baseQuery = dbContext.Organisations.AsQueryable();
        if (orgId.HasValue)
        {
            baseQuery = baseQuery.Where(org => org.Id == orgId);
        }
        else
        {
            Guid? tenantId = tenantResolverService.GetTenantId();
            baseQuery = baseQuery.Where(org => org.TenantId == tenantId);
        }

        var orgData =
            await baseQuery
                .Select(org => new 
                {
                    OrgName = org.Name,
                    Industry = org.Industry.Name,
                    org.Website,
                    org.EmployeesCount,
                    ReportingCurrency = org.Currency.ISO3LetterCurrencySymbol,
                    org.YearEstablished,
                    ApproxContractsNumber = org.ApproximateContractsNumber,
                    AvgContractDuration = org.AverageContractDuration,
                    AvgContractSize = org.AverageContractSize,
                    LastYearRevenue = (decimal?)org.AnnualFinances.FirstOrDefault(af => af.Type == AnnualFinancesType.Revenue && af.Year == DateTime.Now.Year - 1).Value,
                    LastYearGrossProfit = (decimal?)org.AnnualFinances.FirstOrDefault(af => af.Type == AnnualFinancesType.GrossProfit && af.Year == DateTime.Now.Year - 1).Value,
                    CurrentYearRevenue = (decimal?)org.AnnualFinances.FirstOrDefault(af => af.Type == AnnualFinancesType.Revenue && af.Year == DateTime.Now.Year).Value,
                    NextYearProjectedRevenue = (decimal?)org.AnnualFinances.FirstOrDefault(af => af.Type == AnnualFinancesType.Projected && af.Year == DateTime.Now.Year + 1).Value,
                    Address = org.HqAddress,
                    City = org.HqCity,
                    Country = org.HqCountry.Name,
                    Postcode = org.HqPostcode,
                    NoOfBiometricRecords = org.NumberOfBiometricRecords,
                    NoOfPciRecords = org.NumberOfPaymentCardInformationRecords,
                    NoOfPhiRecords = org.NumberOfPersonalHealthInformationRecords,
                    NoOfPiiRecords = org.NumberOfPersonalIdentifiableInformationRecords,
                    ApproxNoOfPersonalRecordsInDb = org.ApproximateNumberOfPersonalRecordsStoredInSingleDb,
                    org.NumberOfEndpoints,
                    NumberOfServer = org.NumberOfServers,
                    ApproxTotalHardwareItValue = org.TotalHardwareItValue,
                    ApproxTotalOtHardwareValue = org.TotalHardwareOtValue,
                    Vendors =
                        dbContext.OrganisationSupplierAssociations
                            .Where(v => v.OrganisationId == org.Id)
                            .Select(v => new 
                            {
                                v.Supplier.Name,
                                SupplierType = v.Supplier.Organisations.FirstOrDefault(x => x.OrganisationId == org.Id)
                                    .OrganisationSupplierAssociationSupplierServiceTypes.Select(x => x.SupplierServiceType.Type),
                                SupplierServicesNames = v.OrganisationSupplierAssociationSupplierServices.Select(s => s.SupplierService.Name)
                            })
                            .ToList(),
                    CountryRevenueSplit = 
                        org.CountryRevenues
                            .Select(cr => new List<string> {cr.Country.Name, cr.Revenue.ToString()})
                            .ToList(),
                    IndustryRevenueSplit = 
                        org.IndustryRevenues
                            .Select(ir => new List<string> {ir.YearStarted.ToString(), ir.Industry.Name, ir.Revenue.ToString()})
                            .ToList(),
                    DataCentreLocations = 
                        dbContext.DataCentres
                            .Where(dc => dc.OrganisationId == org.Id)
                            .Select(dc => new List<string> {dc.Country.Name, dc.NumberOfDataCentres.ToString()})
                            .ToList(),
                    TopFiveContracts = 
                        org.Contracts
                            .Select(c => new 
                            {
                                c.ClientName, c.Industry.Name, c.ProductServiceName, c.ContractValue, c.Duration
                            })
                            .ToList(),
                    org.Currency.CurrencySymbol
                })
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(true);

        if (orgData is null)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        }

        GetDownloadOrgSummaryDataRequestResponse response = new()
        {
            __CommunicationMethod__ = "1",
            __ContextId__ = "",
            __FileNamePrefix__ = "Theia",
            __FileNameEssentialId__ = "",
            __FileNameEssentialName__ = "Summary_1_0_0",
            __DocumentFormat__ = "2",
            __TeamsChannelKey__ = "",
            __DocumentType__ = "Org",
            __TemplateName__ = "Theia-Org-Summary_1_0_0.cshtml",
            OrgName = orgData.OrgName,
            Industry = orgData.Industry,
            Website = orgData.Website,
            EmployeesCount = orgData.EmployeesCount,
            ReportingCurrency = orgData.ReportingCurrency,
            YearEstablished = orgData.YearEstablished,
            ApproxContractsNumber = orgData.ApproxContractsNumber,
            AvgContractDuration = orgData.AvgContractDuration,
            AvgContractSize = orgData.AvgContractSize,
            LastYearRevenue = orgData.LastYearRevenue.ConvertToRelevantCurrency(orgData.CurrencySymbol),
            LastYearGrossProfit = orgData.LastYearGrossProfit.ConvertToRelevantCurrency(orgData.CurrencySymbol),
            CurrentYearRevenue = orgData.CurrentYearRevenue.ConvertToRelevantCurrency(orgData.CurrencySymbol),
            NextYearProjectedRevenue = orgData.NextYearProjectedRevenue.ConvertToRelevantCurrency(orgData.CurrencySymbol),
            Address = orgData.Address,
            City = orgData.City,
            Country = orgData.Country,
            Postcode = orgData.Postcode,
            NoOfBiometricRecords = orgData.NoOfBiometricRecords,
            NoOfPciRecords = orgData.NoOfPciRecords,
            NoOfPhiRecords = orgData.NoOfPhiRecords,
            NoOfPiiRecords = orgData.NoOfPiiRecords,
            ApproxNoOfPersonalRecordsInDb = orgData.ApproxNoOfPersonalRecordsInDb,
            NumberOfEndpoints = orgData.NumberOfEndpoints,
            NumberOfServer = orgData.NumberOfServer,
            ApproxTotalHardwareItValue = orgData.ApproxTotalHardwareItValue,
            ApproxTotalOtHardwareValue = orgData.ApproxTotalOtHardwareValue,
            Vendors = orgData.Vendors.Select(v => new List<string>
            {
                v.Name,
                v.SupplierServicesNames.Any() ? v.SupplierServicesNames.Aggregate((acc, curr) => $"{acc}, {curr}") : string.Empty,
                v.SupplierType.Any() ? v.SupplierType.Aggregate((acc, curr) => $"{acc}, {curr}") : string.Empty
            }).ToList(),
            CountryRevenueSplit = orgData.CountryRevenueSplit,
            IndustryRevenueSplit = orgData.IndustryRevenueSplit,
            DataCentreLocations = orgData.DataCentreLocations,
            TopFiveContracts =
                orgData.TopFiveContracts
                    .Select(c => new List<string>
                    {
                        c.ClientName,
                        c.Name,
                        c.ProductServiceName,
                        c.ContractValue.ShortenWithSuffixAndCurrencySymbols(orgData.CurrencySymbol),
                        c.Duration.ToString()
                    })
                    .ToList()
        };

        return response;
    }
}
