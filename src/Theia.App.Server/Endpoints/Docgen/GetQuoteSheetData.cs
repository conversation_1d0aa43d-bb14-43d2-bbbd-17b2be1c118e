using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Net;
using Theia.App.Shared.DocGen;
using Theia.App.Shared.Enums;
using Theia.Application.Common.Exceptions;
using Theia.Domain.Entities.Indications;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;
using Option = Theia.App.Shared.DocGen.Option;

namespace Theia.App.Server.Endpoints.Docgen;

[ApiAuthorize(PermissionType = PermissionTypes.GetIndicationData)]
public class GetQuoteSheetData(
        IApplicationDbContext dbContext) :
    EndpointBaseAsync.WithRequest<Guid>.WithResult<QuoteSheetDataRequestResponse>
{
    [HttpGet("api/doc-gen/quotes/{quoteId:guid:required}")]
    public override async Task<QuoteSheetDataRequestResponse> HandleAsync(
        [FromRoute] Guid quoteId,
        CancellationToken cancellationToken = new())
    {
        Indication? indicationToReturn = await dbContext.Indications
            .AsNoTracking()
            .Include(i => i.IndicationRequest)
            .ThenInclude(bs => bs.Layer.Submission.RequestedForOrganisation)
            .Include(i => i.IndicationRequest)
            .ThenInclude(bs => bs.RequestedByBrokingHouse)
            .Include(i => i.IndicationOptions)
            .ThenInclude(io => io.HeadsOfCovers)
            .ThenInclude(hoc => hoc.HeadsOfCover)
            .Include(i => i.PolicyWording)
            .Include(i => i.Endorsements)
            .Include(i => i.Subjectives)
            .Include(i => i.CreatedFor)
            .Include(i => i.Insurer)
            .FirstOrDefaultAsync(i => i.Id == quoteId, cancellationToken)
            .ConfigureAwait(true);

        if (indicationToReturn is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, HttpStatusCode.NotFound.ToInt());
        }

        QuoteSheetDataRequestResponse response = new()
        {
            __CommunicationMethod__ = "1",
            __ContextId__ = indicationToReturn.Id.ToString(),
            __FileNamePrefix__ = "Theia",
            __FileNameEssentialId__ = indicationToReturn.Id.ToString(),
            __FileNameEssentialName__ = "Indication-Summary_1_0_0",
            __DocumentFormat__ = "2",
            __TeamsChannelKey__ = "",
            __DocumentType__ = "Indication-Summary",
            __TemplateName__ = "Theia-Indication-Summary_1_0_0.cshtml",
            Insured = indicationToReturn.IndicationRequest?.Layer?.Submission?.RequestedForOrganisation?.Name ?? "",
            BrokerContact = $"{indicationToReturn.IndicationRequest?.RequestedByBrokingHouse?.Name} - {indicationToReturn.IndicationRequest?.RequestedByBrokingHouse?.ContactName}",
            QuoteDate = $"{indicationToReturn.IndicatedDate ?? DateTimeOffset.MinValue:dd MMM yyyy}",
            QuoteValid = indicationToReturn.IndicationDays is null ? Resource.TBC : $"{indicationToReturn.IndicationDays} {Resource.days} ({indicationToReturn.IndicatedDate?.AddDays(indicationToReturn.IndicationDays ?? 0):dd MMM yyyy})",
            Underwriter = indicationToReturn.CreatedFor?.FullName ?? "",
            PolicyPeriodFrom = indicationToReturn.PolicyStartDate is null ? Resource.TBC : $"{indicationToReturn.PolicyStartDate:dd MMM yyyy}",
            PolicyPeriodTo = indicationToReturn.PolicyEndDate is null ? Resource.TBC : $"{indicationToReturn.PolicyEndDate:dd MMM yyyy}",
            Retroactivedate = HandleRetroactiveDate(indicationToReturn.RetroactiveDateType, indicationToReturn.RetroactiveDate),
            Insurer = indicationToReturn.Insurer?.Name ?? "",
            IncidentAndClaimNotifications = indicationToReturn.IncidentClaimNotifications ?? Resource.TBC,
            ChoiceOfLawAndJurisdiction = indicationToReturn.LawAndJurisdiction ?? Resource.TBC,
            PolicyWording = indicationToReturn.PolicyWording?.Name ?? "",
            IncidentResponsePanel = indicationToReturn.IncidentResponsePanel ?? Resource.TBC,
            EndorsementsExclusions = indicationToReturn.Endorsements is { Count: > 0 } ? [indicationToReturn.Endorsements.Select(e => e.Name).ToList()] : [],
            Subjectivities = indicationToReturn.Subjectives is { Count: > 0 } ? [indicationToReturn.Subjectives.Select(s => s.Name).ToList()] : [],
            Options = [MapIndicationOptionsToResponseOptions(indicationToReturn.IndicationOptions, indicationToReturn.IndicationRequest?.Layer?.Currency ?? "")] 
        };

        return response;
    }

    private static List<Option> MapIndicationOptionsToResponseOptions(ICollection<Domain.Entities.Indications.Option>? indicationOptions, string currency)
    {
        if (indicationOptions == null || !indicationOptions.Any())
        {
            return new List<Option>();
        }
        
        return indicationOptions.Select(io => new Option
        {
            PolicyAggregateLimitOfLiability = $"{currency} {io.LimitOfLiability:N2}",
            InsurerLine = $"{currency} {io.InsurerLine:N2}",
            Premium = $"{currency} {io.Premium:N2}",
            Brokerage = $"{io.BrokeragePercentage}%",
            LiabilityAgreement = io.LiabilityAgreement.GetDisplay(),
            InsuringAgreements = io.HeadsOfCovers
                .Where(hoc => hoc.HeadsOfCover.Section == HeadOfCoverSection.InsuringAgreements)
                .Select(hoc => new InsuringAgreementOrExtension
                {
                    Name = hoc.HeadsOfCover.Name,
                    Limit = !hoc.IsCovered ? Resource.Not_covered : $"{currency} {hoc.LiabilityAmount:N2}",
                    RetentionOrWaitingPeriod = !hoc.IsCovered ? Resource.Not_covered : $"{currency} {hoc.RetentionAmount:N2}",
                    HasWaitingPeriod = hoc.HeadsOfCover.HasWaitingPeriod,
                    WaitingPeriod = $"{hoc.WaitingPeriod} {Resource.Hrs}",
                    IsCovered = hoc.IsCovered
                })
                .ToList(),
            Extensions = io.HeadsOfCovers
                .Where(hoc => hoc.HeadsOfCover.Section == HeadOfCoverSection.Extensions)
                .Select(hoc => new InsuringAgreementOrExtension
                {
                    Name = hoc.HeadsOfCover.Name,
                    Limit = !hoc.IsCovered ? Resource.Not_covered : $"{currency} {hoc.LiabilityAmount:N2}",
                    RetentionOrWaitingPeriod = !hoc.IsCovered ? Resource.Not_covered : $"{currency} {hoc.RetentionAmount:N2}",
                    HasWaitingPeriod = hoc.HeadsOfCover.HasWaitingPeriod,
                    WaitingPeriod = $"{hoc.WaitingPeriod} {Resource.Hrs}",
                    IsCovered = hoc.IsCovered
                })
                .ToList()
        }).ToList();
    }

    private static string HandleRetroactiveDate(RetroactiveDateType retroactiveDateType,
        DateTimeOffset? retroactiveDate) => retroactiveDateType switch
    {
        RetroactiveDateType.TBC => Resource.TBC,
        RetroactiveDateType.Inception => Resource.Inception,
        RetroactiveDateType.DateProvided => retroactiveDate?.ToString(DefaultSettings.DateFormatConstants.DateFormat) ??
                                            throw new ExpectedDateNotFoundException(),
        _ => Resource.TBC
    };
}
