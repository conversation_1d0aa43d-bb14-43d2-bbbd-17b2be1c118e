using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Suppliers.Dashboard;
using Theia.App.Shared.Suppliers.DTOs;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Suppliers.Dashboard;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class GetDashboard(
        IApplicationDbContext dbContext,
        IHttpContextAccessor contextAccessor,
        ScoreService scoreService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetSupplierDashboardResponse>
{
    [HttpGet("/api/suppliers/dashboard")]
    public override async Task<GetSupplierDashboardResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        string currentUserId = contextAccessor.GetAuthId();

        const int daysIn9Months = 9 * 30;
        const int daysIn12Months = 12 * 30;

        Guid[] controlFrameworkIds = await dbContext.ControlFrameworks
            .Select(x => x.Id)
            .ToArrayAsync(cancellationToken)
            .ConfigureAwait(false);
        
        GetSupplierDashboardResponse? response =
            await dbContext.Users
                .Where(u => u.Id == currentUserId)
                .Select(u => u.Supplier)
                .Select(s => new GetSupplierDashboardResponse
                {
                    NumberOfContracts = s.Organisations.Count,
                    IncompleteRequests = s.SubmissionRequests.Count(sr => sr.Status == SupplierSubmissionRequestStatus.Incomplete),
                    ExpiringAssessments = s.SubmissionRequests
                        .Where(sr => sr.CompletedOnUtc != null)
                        .GroupBy(sr => sr.OrganisationId)
                        .Select(g =>
                            g.OrderByDescending(sr => sr.CompletedOnUtc)
                                .Select(sr => sr.CompletedOnUtc)
                                .First())
                        .Select(dto => EF.Functions.DateDiffDay(dto, DateTimeOffset.Now))
                        .Count(dd => dd < daysIn12Months && dd >= daysIn9Months),
                    ExpiredAssessments = s.SubmissionRequests
                        .Where(sr => sr.CompletedOnUtc != null)
                        .GroupBy(sr => sr.OrganisationId)
                        .Select(g =>
                            g.OrderByDescending(sr => sr.CompletedOnUtc)
                                .Select(sr => sr.CompletedOnUtc)
                                .First())
                        .Select(dto => EF.Functions.DateDiffDay(dto, DateTimeOffset.Now))
                        .Count(dd => dd > daysIn12Months),
                    RecentlyModifiedAssessments = 
                        s.ApplicationFormVersions
                            .Where(v => v.LastModifiedOn.HasValue)
                            .OrderByDescending(v => v.LastModifiedOn)
                            .Take(5)
                            .Select(v => new GetSupplierDashboardResponseAssessment
                            {
                                Id = v.Id,
                                ApplicationFormName = v.ApplicationFormVersion.ApplicationForm.Name,
                                ApplicationFormVersionName = v.ApplicationFormVersion.Version, 
                                LastModifiedOn = v.LastModifiedOn,
                                IsCompeted = v.IsComplete,
                                LatestSnapshotId = v.Snapshots.OrderByDescending(s => s.CreatedOnUtc).FirstOrDefault().Id
                            })
                            .ToArray(),
                    Submissions = 
                        s.SubmissionRequests
                            .Where(sr => sr.Status == SupplierSubmissionRequestStatus.Completed && sr.CompletedOnUtc.HasValue)
                            .OrderByDescending(sr => sr.CompletedOnUtc)
                            .Take(5)
                            .Select(sr => new GetSupplierDashboardResponseSubmission
                            {
                                Id = sr.Id,
                                ClientName = sr.Organisation.Name,
                                CompletedOn = sr.CompletedOnUtc.Value
                            })
                            .ToArray(),
                    ControlFrameworkScores =
                        s.ApplicationFormVersions
                            .SelectMany(afv => afv.Snapshots)
                            .Select(snapshot => new
                            {
                                Analysis = dbContext.AnalysedControlFrameworks
                                    .Where(x => x.SupplierApplicationFormVersionSnapshotId == snapshot.Id && x.Score > 0)
                                    .Select(x => new
                                    {
                                        x.ControlFramework.Name,
                                        x.Score,
                                        x.ControlFramework.Code,
                                        DateToSortBy = x.TheiaAnalysisJob.DateMovedIntoStatus,
                                        x.ControlFrameworkId,
                                        SupplierApplicationFormVersionSnapshotId = snapshot.Id
                                    }).ToArray()
                            })
                            .SelectMany(x => x.Analysis)
                            .GroupBy(x => x.Name)
                            .Select(x =>
                                x.OrderByDescending(y => y.DateToSortBy)
                                    .Select(y => new SupplierControlFrameworkScores
                                    {
                                        ControlFrameworkScore = y.Score * 100,
                                        ControlFrameworkName = y.Name,
                                        ControlFrameworkId = y.ControlFrameworkId,
                                        IsTheiaLensFramework = y.Code == ControlFrameworkCodes.THEIA,
                                        SupplierApplicationFormVersionSnapshotId = y.SupplierApplicationFormVersionSnapshotId
                                    }).First())
                            .ToArray()
                })
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (response is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        ScoreModel[] scores =
            await scoreService
                .RetrieveSupplierLatestSubmissionScoresAsync(controlFrameworkIds, cancellationToken)
                .ConfigureAwait(false);
        
        foreach (SupplierControlFrameworkScores controlFramework in response.ControlFrameworkScores)
        {
            Dictionary<Guid, string> positions = scores
                .Where(x => x.ControlFrameworkId == controlFramework.ControlFrameworkId)
                .ToDictionary(
                    x => x.ControlFrameworkId,
                    x => x.CalculatePercentagePosition(controlFramework.SupplierApplicationFormVersionSnapshotId!.Value));

            controlFramework.PercentilePosition = positions.FirstOrDefault(x =>
                x.Key == controlFramework.ControlFrameworkId).Value;
        }

        return response;
    }
}