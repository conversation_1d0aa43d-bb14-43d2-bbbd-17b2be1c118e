using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Suppliers.DTOs;
using Theia.Application.Common.Extensions;
using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Suppliers.MainContact;

[ApiAuthorize(PermissionType = PermissionTypes.EditSupplierMainContact)]
public class EditSupplierMainContact(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor)
    : EndpointBaseAsync.WithRequest<EditSupplierMainContactRequest>.WithoutResult
{
    [HttpPost("api/suppliers/organisations/contact")]
    public override async Task HandleAsync(EditSupplierMainContactRequest request, CancellationToken cancellationToken = new())
    {
        string userId = httpContextAccessor.GetAuthId();

        OrganisationSupplierAssociation? association =
            await dbContext.Users
                .Where(u => u.Id == userId)
                .Select(u => u.Supplier.Organisations.FirstOrDefault(o => o.OrganisationId == request.OrganisationId))
                .Where(o => o != null)
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);
        
        association.SupplierMainContactName = request.SupplierMainContactName;
        association.SupplierMainContactEmail = request.SupplierMainContactEmail;
        association.SupplierMainContactLandline = request.SupplierMainContactLandline;
        association.SupplierMainContactMobile = request.SupplierMainContactMobile;

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}