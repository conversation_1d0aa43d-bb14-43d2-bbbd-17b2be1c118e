using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Net;
using Theia.App.Shared.Vendors;
using Theia.Domain.Entities.Suppliers;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Suppliers.Products;

[ApiAuthorize(PermissionType = PermissionTypes.EditProducts)]
public class UpdateProduct(
        IApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService,
        SupplierProductsValidator supplierProductsValidator)
    : EndpointBaseAsync.WithRequest<SupplierProductsDto>.WithoutResult
{
    [HttpPost("api/supplier/product/update")]
    public override async Task HandleAsync(
        [FromBody] SupplierProductsDto request, CancellationToken cancellationToken = new())
    {
        Guid? currentSupplierId = await tenantResolverService.GetDomainTenantSupplierIdAsync(HttpContext, dbContext)
            .ConfigureAwait(true);

        await supplierProductsValidator.ValidateAndThrowAsync(request, cancellationToken).ConfigureAwait(false);

        Product? toUpdate = 
            await dbContext.Products
                .SingleOrDefaultAsync(x => 
                    x.SupplierId == currentSupplierId && x.Id == request.LocalId, cancellationToken)
                .ConfigureAwait(false);

        if (toUpdate is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        string requestName = request.Name.Trim();
        string? requestVersion = request.Version?.Trim();
        
        bool productAlreadyExists = 
            await dbContext.Products
                .AnyAsync(x => 
                    x.Id != request.LocalId 
                    && x.SupplierId == currentSupplierId 
                    && x.Name == requestName
                    && x.Version == requestVersion, cancellationToken)
                .ConfigureAwait(false);
        
        if (productAlreadyExists)
        {
            throw new ApiProblemDetailsException(Resource.Product_already_exists, StatusCodes.Status409Conflict);
        }

        toUpdate.Name = requestName;
        toUpdate.Version = requestVersion;
        
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}