using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Extensions;
using Theia.App.Shared.Suppliers.DTOs;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Suppliers.Clients;

[ApiAuthorize(PermissionType = PermissionTypes.GetMyClients)]
public class GetSupplierClients(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    SupplierSubmissionRequestExpirationService submissionRequestExpirationService)
    : EndpointBaseAsync.WithRequest<DataSourceRequest>.WithResult<DataEnvelope<GetSupplierClientsResponseItem>>
{
    [HttpPost("api/suppliers/clients")]
    public override async Task<DataEnvelope<GetSupplierClientsResponseItem>> HandleAsync(DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.GetAuthId();

        Guid? supplierId =
            await dbContext.Users
                .Where(u => u.Id == currentUserId)
                .Select(u => u.SupplierId)
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (supplierId == null)
        {
            throw new ApiProblemDetailsException(Resource.Supplier_is_not_found, StatusCodes.Status404NotFound);
        }

        const string memberName = nameof(GetSupplierClientsResponseItem.SubmissionExpirationStatus);
        const string newMemberName = nameof(Association.LatestSubmissionRequestCompletedDate);
        
        request.ReplaceSort(memberName, newMemberName);
        request.ReplaceFilter(memberName, value => submissionRequestExpirationService.MapFilters(value, newMemberName));

        DataSourceResult result =
            await dbContext.OrganisationSupplierAssociations
                .Where(a => a.SupplierId == supplierId)
                .Select(a => new Association
                {
                    Id = a.OrganisationId,
                    Name = a.Organisation!.Name,
                    LatestSubmissionRequestCompletedDate =
                        a.Supplier.SubmissionRequests
                            .Where(sr => sr.OrganisationId == a.OrganisationId)
                            .OrderByDescending(sr => sr.CompletedOnUtc)
                            .Select(sr => sr.CompletedOnUtc)
                            .FirstOrDefault()
                })
                .ToDataSourceResultAsync(request,
                    a => new GetSupplierClientsResponseItem
                    {
                        Id = a.Id,
                        Name = a.Name,
                        SubmissionExpirationStatus = submissionRequestExpirationService.GetSubmissionStatusFromCompletedDate(a.LatestSubmissionRequestCompletedDate)
                    })
                .ConfigureAwait(false);

        return DataEnvelope<GetSupplierClientsResponseItem>.FromDataSourceResult(result, request);
    }

    private readonly struct Association
    {
        public Guid Id { get; init; }
        public string Name { get; init; }
        public DateTimeOffset? LatestSubmissionRequestCompletedDate { get; init; }
    }
}