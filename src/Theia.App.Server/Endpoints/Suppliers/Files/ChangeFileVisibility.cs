using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Suppliers.Files;
using Theia.Application.Common.Extensions;
using Theia.Domain.Entities.Suppliers;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Suppliers.Files;

[ApiAuthorize(PermissionType = PermissionTypes.SupplierAddFiles)]
public class ChangeFileVisibility(IApplicationDbContext dbContext, IHttpContextAccessor httpContextAccessor)
    : EndpointBaseAsync.WithRequest<ChangeFileVisibilityRequest>.WithoutResult
{
    [HttpPost("api/suppliers/files/visibility")]
    public override async Task HandleAsync(ChangeFileVisibilityRequest request, CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.GetAuthId();

        SupplierFile? file =
            await dbContext.Users
                .Where(u => u.Id == currentUserId && u.SupplierId != null)
                .SelectMany(u => u.Supplier.Files)
                .FirstOrDefaultAsync(f => f.Id == request.Id, cancellationToken)
                .ConfigureAwait(false);

        if (file is null)
        {
            throw new ApiProblemDetailsException(Resource.File_not_found, StatusCodes.Status404NotFound);
        }
        
        file.Visibility = request.Visibility;
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}