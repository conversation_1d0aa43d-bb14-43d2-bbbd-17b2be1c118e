using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Broking.Models.Info;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Info;

[ApiAuthorize(PermissionType = PermissionTypes.BrokingHomePageView)]
public class GetOrganisationSubmissionsPerMonth(IApplicationDbContext dbContext, ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetOrganisationSubmissionsPerMonthResponse>
{
    [HttpGet("api/broker/info/organisation-submissions-per-month")]
    public override async Task<GetOrganisationSubmissionsPerMonthResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        Guid? tenantId = tenantResolverService.GetTenantId();

        IQueryable<Submission> query =
            dbContext.Submissions
                .Where(s => s.TenantRequestedById == tenantId);

        int submissionsCount =
            await query
                .CountAsync(cancellationToken)
                .ConfigureAwait(true);

        if (submissionsCount < 3)
        {
            return new GetOrganisationSubmissionsPerMonthResponse([]);
        }

        DateTimeOffset now = DateTimeOffset.Now;
        int currentYear = now.Year;
        int currentMonth = now.Month;
        int startYear = currentMonth == 12 ? currentYear : currentYear - 1;
        int startMonth = currentMonth == 12 ? 1 : currentMonth + 1;

        GetOrganisationSubmissionsPerMonthResponseItem[] brokerSubmissionPerMonth =
            await query
                .Where(bs =>
                    (bs.RequestedOnDate.Year == startYear && bs.RequestedOnDate.Month >= startMonth)
                    || (bs.RequestedOnDate.Year == currentYear && bs.RequestedOnDate.Month <= currentMonth))
                .Distinct()
                .GroupBy(bs => new {bs.RequestedOnDate.Year, bs.RequestedOnDate.Month})
                .Select(grouped =>
                    new GetOrganisationSubmissionsPerMonthResponseItem
                    {
                        Year = grouped.Key.Year, Month = grouped.Key.Month, SubmissionCount = grouped.Count()
                    })
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(true);

        return new GetOrganisationSubmissionsPerMonthResponse(brokerSubmissionPerMonth);
    }
}