using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Broking.Models.Info;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Info;

[ApiAuthorize(PermissionType = PermissionTypes.BrokingHomePageView)]
public class GetUnopenedBrokerSubmissions(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    SubmissionsService submissionsService,
    ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetUnopenedBrokerSubmissionsResponse>
{
    [HttpGet("api/broker/info/unopened-broker-submissions")]
    public override async Task<GetUnopenedBrokerSubmissionsResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.GetAuthId();
        Guid? tenantId = tenantResolverService.GetTenantId();

        if (!tenantId.HasValue)
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, HttpStatusCode.NotFound.ToInt());

        int sharedCount =
            await submissionsService
                .GetAllOrganisationsBrokerSubmissionSharedToUserCountAsync(currentUserId, cancellationToken)
                .ConfigureAwait(true);

        int fromOrgCount = 
            await submissionsService
                .GetBrokerSubmissionsAvailableToTenantCountAsync(tenantId.Value, currentUserId, cancellationToken)
                .ConfigureAwait(true);

        int totalAvailableSubmissions = sharedCount + fromOrgCount;

        int viewedSubmissions =
            await dbContext.BrokerSubmissionViews
                .CountAsync(bsv =>
                    bsv.UserId == currentUserId
                    && EF.Property<string>(bsv.BrokerSubmission, ShadowProperties.CreatedById)!= currentUserId
                    && dbContext.BrokerSubmissionBroker
                        .Any(y =>
                            y.BrokerSubmissionId == bsv.BrokerSubmissionId
                            && y.UserId == bsv.UserId), cancellationToken)
                .ConfigureAwait(true);

        return new GetUnopenedBrokerSubmissionsResponse(totalAvailableSubmissions - viewedSubmissions);
    }
}