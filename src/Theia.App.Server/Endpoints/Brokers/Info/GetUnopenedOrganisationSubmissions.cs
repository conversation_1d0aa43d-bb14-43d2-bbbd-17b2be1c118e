using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Broking.Models.Info;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Info;

[ApiAuthorize(PermissionType = PermissionTypes.BrokingHomePageView)]
public class GetUnopenedOrganisationSubmissions(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    SubmissionsService submissionsService) 
    : EndpointBaseAsync.WithoutRequest.WithResult<GetUnopenedOrganisationSubmissionsResponse>
{
    [HttpGet("api/broker/info/unopened-organisation-submissions")]
    public override async Task<GetUnopenedOrganisationSubmissionsResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        string currentUserId = httpContextAccessor.GetAuthId();
        IQueryable<Submission> query = submissionsService.GetAllSubmissionAvailableForCurrentUsersBrokingHouseQuery();

        int totalAvailableSubmissions =
            await query
                .CountAsync(cancellationToken)
                .ConfigureAwait(true);

        int viewedSubmissions = await dbContext.SubmissionViews
            .Join(query, 
                bsv => bsv.SubmissionId, 
                submission => submission.Id, 
                (bsv, submission) => bsv)
            .CountAsync(bsv => bsv.ViewedById == currentUserId, cancellationToken)
            .ConfigureAwait(false);

        return new GetUnopenedOrganisationSubmissionsResponse(totalAvailableSubmissions - viewedSubmissions);
    }
}