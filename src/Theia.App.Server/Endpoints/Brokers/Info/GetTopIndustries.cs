using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Broking.Models.Info;
using Theia.App.Shared.Models;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Info;

[ApiAuthorize(PermissionType = PermissionTypes.BrokingHomePageView)]
public class GetTopIndustries(SubmissionsService submissionsService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetTopIndustriesResponse>
{
    [HttpGet("api/broker/info/top-industries")]
    public override async Task<GetTopIndustriesResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        int submissionsCount =
            await submissionsService
                .GetAllBrokerSubmissionAvailableForCurrentUsersBrokingHouseQuery()
                .CountAsync(cancellationToken)
                .ConfigureAwait(true);

        if (submissionsCount < 3)
        {
            return new GetTopIndustriesResponse([]);
        }

        PieChartItem[] topIndustries =
            await submissionsService
                .GetAllBrokerSubmissionAvailableForCurrentUsersBrokingHouseQuery()
                .Select(bsu => bsu.Layer.Submission.RequestedForOrganisation)
                .Distinct()
                .Select(org => org.Industry.Name)
                .GroupBy(name => name)
                .Select(grouping => new PieChartItem(grouping.Key, grouping.Count().ToString()))
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(true);

        return new GetTopIndustriesResponse(topIndustries);
    }
}