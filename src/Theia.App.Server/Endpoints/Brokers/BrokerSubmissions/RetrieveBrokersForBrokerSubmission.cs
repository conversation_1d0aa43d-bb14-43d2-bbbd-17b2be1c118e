using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Models;
using Theia.Application.Interfaces;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.BrokerSubmissions;

[ApiAuthorize(PermissionType = PermissionTypes.UpdateBrokerSubmissionUsers)]
public class RetrieveBrokersForBrokerSubmission(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService,
        ICommonServices commonServices,
        ITenantResolverService tenantResolverService) 
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithResult<BrokerSubmissionTenantModel[]>
{
    [HttpGet("api/brokers/retrieve-brokers-for-broker-submission/{brokerSubmissionId}")]
    public override async Task<BrokerSubmissionTenantModel[]> HandleAsync(
        [FromRoute] WebSafeGuid brokerSubmissionId,
        CancellationToken cancellationToken = new())
    {
        string userId = (await commonServices.RetrieveAuthUserIdFromHttpContextAsync(HttpContext).ConfigureAwait(false)).ToString();
        await submissionPermissionService
            .CheckReadAccessOrThrowAsync(brokerSubmissionId: brokerSubmissionId, cancellationToken: cancellationToken)
            .ConfigureAwait(false);

        string[] existingBrokers = await dbContext.BrokerSubmissions
            .Join(dbContext.BrokerSubmissionBroker,
                bs => bs.Id,
                bsb => bsb.BrokerSubmissionId,
                (bs, bsb) => new { Bs = bs, Bsb = bsb })
            .Join(dbContext.Submissions,
                model => model.Bs.Layer.SubmissionId,
                sub => sub.Id,
                (model, sub) => new { model.Bs, model.Bsb, Sub = sub })
            .Where(model => model.Bs.Id == brokerSubmissionId
                            && model.Sub.PrimaryBrokingHouseTenantId == tenantResolverService.GetTenantId())
            .Select(model => model.Bsb.UserId)
            .ToArrayAsync(cancellationToken).ConfigureAwait(false);

        Guid? primaryBrokingHouseTenantId = await dbContext.BrokerSubmissions
                .Join(dbContext.Submissions,
                    bs => bs.Layer.SubmissionId,
                    s => s.Id,
                    (bs, s) => new { Bs = bs, S = s })
                .Where(model => model.Bs.Id == brokerSubmissionId)
                .Select(model => model.S.PrimaryBrokingHouseTenantId)
                .SingleAsync(cancellationToken)
                .ConfigureAwait(false);

        return await dbContext.BrokingHouses
            .Where(x => x.Id != primaryBrokingHouseTenantId)
            .Select(x => new BrokerSubmissionTenantModel
            {
                TenantId = x.Id,
                TenantName = x.Name,
                Users = (from utc in dbContext.UserTenantControls
                        join u in dbContext.Users on utc.UserId equals u.Id
                        join sb in dbContext.BrokerSubmissionBroker
                            on new { u.Id, BrokerSubmissionId = (Guid)brokerSubmissionId }
                            equals new { Id = sb.UserId, sb.BrokerSubmissionId }
                            into grouping
                        from subSb in grouping.DefaultIfEmpty()
                        where utc.TenantId == x.Id 
                              && u.Id != userId
                              && u.EmailConfirmed
                              && (existingBrokers.Contains(u.Id) || !u.IsSuspended)
                        select new { Utc = utc, U = u, Sb = subSb })
                    .Select(model => new SubmissionTenantUserModel
                    {
                        Name = $"{model.U.Name} {model.U.Surname}",
                        UserId = model.U.Id,
                        IsSelected = existingBrokers.Contains(model.U.Id),
                        Email = x.Email,
                        IsSuspended = model.U.IsSuspended
                    }).ToArray()
            }).ToArrayAsync(cancellationToken)
            .ConfigureAwait(false);
    }
}