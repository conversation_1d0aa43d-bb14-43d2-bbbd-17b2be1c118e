using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Models;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Common.Models.Submission;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.BrokerSubmissions;

[ApiAuthorize(PermissionType = PermissionTypes.GenerateBrokerSubmission)]
public class RetrieveSubmissionsForBrokerSubmission(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService) : EndpointBaseAsync
    .WithRequest<WebSafeGuid>.WithResult<ApplicationFormsForBrokerSubmissionModel[]>
{
    [HttpGet("api/brokers/get-submissions-for-broker/{submissionId}")]
    public override async Task<ApplicationFormsForBrokerSubmissionModel[]> HandleAsync(
        [FromRoute] WebSafeGuid submissionId,
        CancellationToken cancellationToken = new())
    {
        await submissionPermissionService
            .CheckReadAccessOrThrowAsync(submissionId: submissionId, cancellationToken: cancellationToken)
            .ConfigureAwait(false);

        return await dbContext.SubmissionApplicationForms
            .Join(dbContext.ApplicationFormVersions,
                saf => saf.ApplicationFormVersionId,
                afv => afv.Id,
                (saf, afv) => new { Saf = saf, Afv = afv })
            .Where(model =>
                model.Saf.SubmissionId == submissionId
                && model.Saf.Status == ApplicationFormStatus.Completed)
            .Select(model => new ApplicationFormsForBrokerSubmissionModel
            {
                SubmissionId = submissionId,
                SubmittedOn = model.Saf.CompletedOn.HasValue
                    ? model.Saf.CompletedOn.Value.ToString(DefaultSettings.DateFormatConstants.DateFormat)
                    : DateTime.MinValue.ToString(DefaultSettings.DateFormatConstants.DateFormat),
                ApplicationFormName = $"{model.Afv.Version} - {model.Afv.ApplicationForm.Name}",
                ApplicationFormVersion = model.Afv.Version,
                ApplicationFormVersionId = model.Afv.Id,
                SubmissionApplicationId = model.Saf.Id
            })
            .ToArrayAsync(cancellationToken)
            .ConfigureAwait(false);
    }
}