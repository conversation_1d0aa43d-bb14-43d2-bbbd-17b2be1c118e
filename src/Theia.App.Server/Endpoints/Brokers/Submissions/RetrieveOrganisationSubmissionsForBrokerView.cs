using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Broking.DTOs;
using Theia.App.Shared.Broking.Organisations.Submissions;
using Theia.Application.Services;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Submissions;

[ApiAuthorize(PermissionType = PermissionTypes.ViewOrganisationDetails)]
public class RetrieveOrganisationSubmissionsForBrokerView(
        IApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService,
        BrokingHousePermissionService brokingHousePermissionService)
    : EndpointBaseAsync.WithRequest<RetrieveOrganisationSubmissionsForBrokerViewRequest>.WithResult<DataEnvelope<GetSubmissionsWithBrokerSubmissionsDto>>
{
    [HttpPost("api/brokers/retrieve-organisations-submissions")]
    public override async Task<DataEnvelope<GetSubmissionsWithBrokerSubmissionsDto>> HandleAsync(
        [FromBody] RetrieveOrganisationSubmissionsForBrokerViewRequest request,
        CancellationToken cancellationToken = new())
    {
        Guid? tenantId = tenantResolverService.GetTenantId();
        if (tenantId is null)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, HttpStatusCode.BadRequest.ToInt());
        }

        await brokingHousePermissionService
            .EnsureCurrentBrokerCanSeeOrganisationAsync(request.OrganisationId, cancellationToken)
            .ConfigureAwait(false);

        DataSourceResult toReturn =
            await dbContext
                .OrganisationSubmissions
                .AsNoTracking()
                .Where(s => s.PrimaryBrokingHouseTenantId == tenantId && s.IsSubmitted)
                .Where(model =>
                    model.PrimaryBrokingHouseTenantId == tenantId
                    && model.RequestedForOrganisationId == request.OrganisationId
                    && model.IsSubmitted)
                .Select(model => new GetSubmissionsWithBrokerSubmissionsDto
                {
                    SubmissionName = model.SubmissionName,
                    SubmissionId = model.Id,
                    SubmittedBy = model.SubmittedBy,
                    SubmittedOn = model.SubmittedOnDate,
                    RequestedOnDate = model.RequestedOnDate,
                    BrokerSubmissions = 
                        model.Layers.Any() && model.Layers.All(y => y.Id != null)
                            ? model.Layers
                                .SelectMany(x => x.IndicationRequests)
                                .Select(x => new BrokerSubmissionsDto
                                {
                                    BrokerSubmissionName = x.SubmissionName,
                                    BrokerSubmissionId = x.Id,
                                    CreatedOn = EF.Property<DateTimeOffset>(x, ShadowProperties.CreatedOn)
                                }).ToArray()
                            : null
                })
                .ToDataSourceResultAsync(request.DataSourceRequest)
                .ConfigureAwait(false);
            
        return DataEnvelope<GetSubmissionsWithBrokerSubmissionsDto>.FromDataSourceResult(toReturn, request.DataSourceRequest);
    }
}