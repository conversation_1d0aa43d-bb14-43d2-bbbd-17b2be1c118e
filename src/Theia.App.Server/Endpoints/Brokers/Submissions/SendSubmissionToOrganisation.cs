using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using NuGet.Protocol;
using Theia.App.Shared.Models.Notifications;
using Theia.Application.Services;
using Theia.Application.Services.Notifications;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Submissions;

[ApiAuthorize(PermissionType = PermissionTypes.ManageSubmissions)]
public class SendSubmissionToOrganisation(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService,
        NotificationService notificationService,
        IUserService userService) : EndpointBaseAsync
    .WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpPost("api/brokers/send-broker-submission")]
    public override async Task HandleAsync(
        [FromBody] WebSafeGuid submissionId, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService
            .CheckReadAccessOrThrowAsync(submissionId: submissionId, cancellationToken: cancellationToken)
            .ConfigureAwait(false);

        Submission? submissionToUpdate = await dbContext.Submissions
            .Include(x => x.RequestedForOrganisation)
            .Include(x => x.PrimaryBrokingHouseTenant)
            .ThenInclude(x => x.BrokingHouse)
            .SingleOrDefaultAsync(x => x.Id == submissionId, cancellationToken);
        
        if (submissionToUpdate is null)
        {
            throw new ApiProblemDetailsException(Resource.Submission_not_found, StatusCodes.Status404NotFound);
        }
        
        submissionToUpdate.HasBeenSentToOrganisation = true;
        notificationService.CreateAndAddNotificationInstance(new()
        {
            SentToTenantId = submissionToUpdate.RequestedForOrganisation.TenantId,
            RecipientIds = await dbContext.UserTenantControls
                .Where(x =>
                    x.TenantId == submissionToUpdate.RequestedForOrganisation.TenantId)
                .Select(x => x.UserId)
                .ToArrayAsync(cancellationToken: cancellationToken),
            SenderId = userService.EnsureAuthId(),
            AdditionalData = new BrokerSentOrganisationSubmissionModel
            {
                SubmissionName = submissionToUpdate.SubmissionName,
                BrokingHouseName = submissionToUpdate.PrimaryBrokingHouseTenant.BrokingHouse.Name,
                SubmissionId = submissionToUpdate.Id
            }
        });

        await dbContext.SaveChangesAsync(cancellationToken);
    }
}