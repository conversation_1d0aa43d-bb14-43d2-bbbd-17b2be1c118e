using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Broking.DTOs;
using Theia.Application.Interfaces;
using Theia.Application.Services;
using Theia.Domain.Entities.Organisations;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Organisations;

[ApiAuthorize(PermissionType = PermissionTypes.ManageSubmissions)]
public class GetOrganisationEmployeeInformation(
        IApplicationDbContext dbContext,
        ICommonQueryFactory commonQueryFactory,
        BrokingHousePermissionService brokingHousePermissionService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithResult<OrganisationEmployeeInformation[]>
{
    [HttpGet("api/brokers/get-organisation-employee-information/{request}")]
    public override async Task<OrganisationEmployeeInformation[]> HandleAsync(
        [FromRoute] WebSafeGuid request,
        CancellationToken cancellationToken = new())
    {
        if (request == Guid.Empty)
        {
            throw new ApiProblemDetailsException(Resource.value_cannot_be_null, HttpStatusCode.BadRequest.ToInt());
        }

        await brokingHousePermissionService
            .EnsureCurrentBrokerCanSeeOrganisationAsync(request, cancellationToken)
            .ConfigureAwait(false);

        Guid? organisationTenantId = await commonQueryFactory.GetOrganisationsTenantIdAsync(request, cancellationToken)
            .ConfigureAwait(false);
        if (organisationTenantId == Guid.Empty)
        {
            throw new ApiProblemDetailsException(Resource.value_cannot_be_null, HttpStatusCode.BadRequest.ToInt());
        }
        
        Organisation? organisation = await commonQueryFactory
            .BuildOrgBrokingAssociationsBaseQuery(organisationTenantId ?? Guid.Empty)
            .Select(x => x.Organisation)
            .FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);
        
        if (organisation is null)
        {
            throw new ApiProblemDetailsException(Resource.value_cannot_be_null, HttpStatusCode.BadRequest.ToInt());
        }
        
        return await dbContext.UserTenantControls
            .Join(dbContext.Users,
                utc => utc.UserId,
                user => user.Id,
                (utc, users) => new { Utc = utc, Users = users })
            .Where(model => model.Utc.TenantId == organisation.TenantId)
            .Select(model => new OrganisationEmployeeInformation
            {
                Forename = model.Users.Name,
                Surname = model.Users.Surname,
                UserName = model.Users.Email
            }).ToArrayAsync(cancellationToken).ConfigureAwait(false);
    }
}