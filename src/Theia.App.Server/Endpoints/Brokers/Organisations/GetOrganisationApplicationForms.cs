using Ardalis.ApiEndpoints;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Broking.DTOs;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Brokers.Organisations;

[ApiAuthorize(PermissionType = PermissionTypes.ManageSubmissions)]
public class GetOrganisationApplicationForms(
        IApplicationDbContext dbContext)
    : EndpointBaseAsync.WithRequest<DataSourceRequest>.WithResult<DataEnvelope<GetOrganisationApplicationFormsDTO>>
{
    [HttpPost("api/brokers/get-application-forms")]
    public override async Task<DataEnvelope<GetOrganisationApplicationFormsDTO>> HandleAsync(
        DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        DataSourceResult availableFormVersions =
            await dbContext.ApplicationForms
                .Where(x => x.ApplicationFormType == ApplicationFormType.Organisation)
                .Select(x => new GetOrganisationApplicationFormsDTO 
                {
                    ApplicationFormId = x.Id,
                    ApplicationFormName = x.Name,
                    Versions = x.Versions
                        .Where(y => y.IsActive
                                    && y.SurveyJson != null
                                    && y.IsComplete)
                        .Select(y => new GetOrganisationApplicationFormVersionsDto
                    {
                        ApplicationFormVersionId = y.Id,
                        ApplicationFormVersion = y.Version,
                        ChildToApplicationFormId = x.Id
                    }).ToArray()
                })
                .Where(x => x.Versions.Any())
                .ToDataSourceResultAsync(request)
                .ConfigureAwait(true);

        return DataEnvelope<GetOrganisationApplicationFormsDTO>.FromDataSourceResult(availableFormVersions, request);
    }
}