using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.OrganisationRequests.GetOrganisationRequests;
using Theia.Application.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.OrganisationRequests;

[ApiAuthorize(PermissionType = PermissionTypes.GetOrganisationRequests)]
public class GetBrokingHouseOrganisationRequests : 
    EndpointBaseAsync
    .WithRequest<DataSourceRequest>
    .WithResult<DataEnvelope<GetBrokingHouseOrganisationRequestsResponseItem>>
{
    private readonly ApplicationDbContext _dbContext;

    public GetBrokingHouseOrganisationRequests(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpPost("api/organisationRequests/get-organisation-requests")]
    public override async Task<DataEnvelope<GetBrokingHouseOrganisationRequestsResponseItem>> HandleAsync(
        DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        DataSourceResult requests =
            await _dbContext
                .OrganisationRequests
                .Select(or => new
                {
                    or.Id, OrganisationName = or.Name, or.ContactName, or.ContactEmail, or.ContactPhoneNumber, or.Status,
                    or.AdminFullName, or.AdminEmail
                })
                .ToDataSourceResultAsync(
                    request,
                    or => new GetBrokingHouseOrganisationRequestsResponseItem(
                        or.Id, or.OrganisationName, or.ContactName, or.ContactEmail, or.ContactPhoneNumber, or.Status,
                        or.AdminFullName, or.AdminEmail))
                .ConfigureAwait(true);

        return requests;
    }
}