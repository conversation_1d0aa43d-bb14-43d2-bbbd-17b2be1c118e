using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.Domain.Entities.OrganisationRequests;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.OrganisationRequests;

[ApiAuthorize(PermissionType = PermissionTypes.DeleteOrganisationRequest)]
public class RemoveOrganisationRequest(ApplicationDbContext dbContext) : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpDelete("api/organisationRequests/{id:required}")]
    public override async Task HandleAsync(
        [FromRoute] WebSafeGuid id, CancellationToken cancellationToken = new())
    {
        OrganisationRequest? request =
            await dbContext
                .OrganisationRequests
                .SingleOrDefaultAsync(or => or.Id == id && or.Status == OrganisationRequestStatus.Pending, cancellationToken);

        if (request is null)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_request_not_found, StatusCodes.Status404NotFound);
        }
        
        dbContext.OrganisationRequests.Remove(request);
        await dbContext.SaveChangesAsync(cancellationToken);
    }
}