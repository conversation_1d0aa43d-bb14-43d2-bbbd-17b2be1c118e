using Ardalis.ApiEndpoints;
using System.Net;
using Theia.App.Shared.Admin.Insurers.AddInsurer;
using Theia.Application.Common.Interfaces.UseCases.Tenants;
using Theia.Domain.Entities;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Insurers;

[ApiAuthorize(PermissionType = PermissionTypes.ManageInsurers)]
public class AddInsurer : EndpointBaseAsync.WithRequest<AddInsurerRequest>.WithoutResult
{
    private readonly IApplicationDbContext _dbContext;
    private readonly ITenantUseCase _tenantUseCase;

    public AddInsurer(IApplicationDbContext dbContext, ITenantUseCase tenantUseCase)
    {
        _dbContext = dbContext;
        _tenantUseCase = tenantUseCase;
    }

    [HttpPost("api/admin/insurers")]
    public override async Task HandleAsync(AddInsurerRequest request, CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid)
        {
            throw new ApiProblemDetailsException(ModelState);
        }

        CreateTenantCommand command = new() {Name = request.Subdomain, Type = TenantType.Insurer};
        Tenant tenant = await _tenantUseCase.CreateTenantAsync(command, cancellationToken).ConfigureAwait(true);

        Insurer bh =
            new()
            {
                Name = request.Insurer,
                ContactName = request.ContactName,
                Email = request.Email,
                Phone = request.Phone,
                Active = request.Active,
                Tenant = tenant
            };

        _dbContext.Insurers.Add(bh);
        
        await _dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}