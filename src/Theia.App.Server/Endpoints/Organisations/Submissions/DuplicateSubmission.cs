using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Organisation.DTOs;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.Organisation;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.Submissions;

[ApiAuthorize(PermissionType = PermissionTypes.OrganisationCreateSubmission)]
public class DuplicateSubmission(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService) : EndpointBaseAsync
    .WithRequest<NewDuplicateSubmission>.WithoutResult
{
    [HttpPost("api/organisations/duplicate-submission")]
    public override async Task HandleAsync(
        [FromBody] NewDuplicateSubmission request, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(cancellationToken, request.SubmissionId).ConfigureAwait(false);

        Submission currentSubmission =
            await dbContext.Submissions
                .Include(s => s.ApplicationForms)
                .AsNoTracking()
                .SingleAsync(x => x.Id == request.SubmissionId, cancellationToken)
                .ConfigureAwait(false);

        OrganisationSubmission duplicateSubmission = new()
        {
            RequestedBy = currentSubmission.RequestedBy,
            RequestedByApplicationUser = null,
            RequestedOnDate = DateTimeOffset.UtcNow,
            SubmissionName = request.SubmissionName,
            DueBy = currentSubmission.DueBy,
            TenantRequestedById = currentSubmission.TenantRequestedById,
            RequestedByTenant = null,
            HasBeenSentToOrganisation = true,
            RequestedForOrganisationId = currentSubmission.RequestedForOrganisationId,
            PrimaryBrokingHouseTenantId = currentSubmission.PrimaryBrokingHouseTenantId,
            ApplicationForms =
                currentSubmission.ApplicationForms.Select(submissionApplicationForm => new SubmissionApplicationForm
                    {
                        ApplicationFormVersionId = submissionApplicationForm.ApplicationFormVersionId,
                        ApplicationFormVersion = null,
                        Status = currentSubmission.ApplicationForms
                            .Any(x => x.SubmissionId == currentSubmission.Id && x.ApplicationFormVersionId == submissionApplicationForm.ApplicationFormVersionId)
                            ? ApplicationFormStatus.InProgress
                            : ApplicationFormStatus.Assigned,
                        PercentageComplete = submissionApplicationForm.PercentageComplete,
                        Signed = false,
                        Signature = submissionApplicationForm.Signature,
                        SurveyAnswers = submissionApplicationForm.SurveyAnswers
                    })
                    .ToList()
        };
        
        dbContext.Submissions.Add(duplicateSubmission);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}