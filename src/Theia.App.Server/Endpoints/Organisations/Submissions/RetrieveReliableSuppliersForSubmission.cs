using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Dtos;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.Submissions;

[ApiAuthorize(PermissionType = PermissionTypes.GetReliableSuppliers)]
public class RetrieveReliableSuppliersForSubmission(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService)
    : EndpointBaseAsync.WithRequest<ReliableSuppliersForSubmissionDto>.WithResult<DataEnvelope<ReliableSuppliers>>
{
    [HttpPost("api/suppliers/get-reliable-suppliers-for-submission")]
    public override async Task<DataEnvelope<ReliableSuppliers>> HandleAsync(
        [FromBody] ReliableSuppliersForSubmissionDto request,
        CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(cancellationToken, request.OrganisationSubmissionId).ConfigureAwait(false);
        
        DataSourceResult response =
            await dbContext
                .SubmissionSupplierAppFormVersionSnapshots
                .AsNoTracking()
                .Where(x => x.SubmissionsSubmissionId == request.OrganisationSubmissionId)
                .Select(x => new ReliableSuppliers
                {
                    Score = x.TheiaAnalysisJob.AnalysedControlFrameworks.FirstOrDefault().Score,
                    ScorePosition = x.TheiaAnalysisJob.AnalysedControlFrameworks.FirstOrDefault().ScorePosition,
                    ControlFrameworkId = x.TheiaAnalysisJob.AnalysedControlFrameworks.FirstOrDefault().ControlFramework.Id,
                    ControlFrameworkName = x.TheiaAnalysisJob.AnalysedControlFrameworks.FirstOrDefault().ControlFramework.Name,
                    SupplierName = x.SubmissionSupplier.Supplier.Name,
                    ApplicationFormNameAndVersion =
                        x.Snapshot.SupplierApplicationFormVersion.ApplicationFormVersion.ApplicationForm.Name + " (" +
                        x.Snapshot.SupplierApplicationFormVersion.ApplicationFormVersion.Version + ")",
                    SupplierApplicationFormVersionSnapshotId = x.SnapshotId,
                    SupplierApplicationFormVersionId = x.Snapshot.SupplierApplicationFormVersionId.Value
                }).ToDataSourceResultAsync(request.DataSourceRequest);

        return response;
    }
}