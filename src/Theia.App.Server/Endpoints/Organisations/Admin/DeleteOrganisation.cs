using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.Domain.Entities.Organisations;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.Admin;

[ApiAuthorize(PermissionType = PermissionTypes.AdminDeleteOrganisation)]
public class DeleteOrganisation : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    private readonly ApplicationDbContext _dbContext;

    public DeleteOrganisation(ApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpDelete("api/admin/organisations/{id:required}")]
    public override async Task HandleAsync(
        [FromRoute] WebSafeGuid id, CancellationToken cancellationToken = new())
    {
        Organisation? organisation = 
            await _dbContext
                .Organisations
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken)
                .ConfigureAwait(true);

        if (organisation is null)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        }

        _dbContext.Organisations.Remove(organisation);
        await _dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}