using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Organisation.Suppliers;
using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.Vendors;

[ApiAuthorize(PermissionType = PermissionTypes.OrgViewSubmissions)]
public class EditSupplierDetails(IApplicationDbContext dbContext, ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithRequest<EditSuppliersDetailsRequest>.WithoutResult
{
    [HttpPost("api/organisations/suppliers/details")]
    public override async Task HandleAsync(EditSuppliersDetailsRequest request, CancellationToken cancellationToken = new())
    {
        Guid? tenantId = tenantResolverService.GetTenantId();

        if (!tenantId.HasValue)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, StatusCodes.Status400BadRequest);
        }

        var entities =
            await dbContext.OrganisationSupplierAssociations
                .Include(osa => osa.Supplier)
                .Include(osa => osa.OrganisationSupplierAssociationSupplierServices)
                .Include(osa => osa.Supplier.Organisations)
                .ThenInclude(osa => osa.OrganisationSupplierAssociationSupplierServiceTypes)
                .Where(osa => osa.Organisation.TenantId == tenantId && osa.SupplierId == (WebSafeGuid)request.SupplierId)
                .Select(osa => new {Association = osa, osa.Supplier})
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (entities is null)
        {
            throw new ApiProblemDetailsException(Resource.Supplier_is_not_found, StatusCodes.Status404NotFound);
        }

        entities.Association.ContractStart = request.ContractStart;
        entities.Association.RenewalDate = request.RenewalDate;
        entities.Association.Criticality = request.SupplierCriticality;
        entities.Supplier.ContactEmail = request.ContactEmail;

        IEnumerable<OrganisationSupplierAssociationSupplierService> newServices = 
            request.ServicesIds
                .Where(reqServiceId => entities.Association.OrganisationSupplierAssociationSupplierServices.All(supService => supService.SupplierServiceId != reqServiceId))
                .Select(reqServiceId => new OrganisationSupplierAssociationSupplierService
                {
                    SupplierService = null,
                    SupplierServiceId = reqServiceId,
                    OrganisationSupplierAssociation = entities.Association,
                    OrganisationSupplierAssociationId = entities.Association.Id
                });
        
        dbContext.OrganisationSupplierAssociationSupplierServices.AddRange(newServices);
        entities.Association.OrganisationSupplierAssociationSupplierServices.RemoveAll(supService => !request.ServicesIds.Contains(supService.SupplierServiceId.Value));

        List<OrganisationSupplierAssociationServiceType> newServiceTypes =
            request.TypeIds
                .Where(x => entities.Supplier.Organisations.FirstOrDefault(
                        y => y.OrganisationId == entities.Association.OrganisationId).OrganisationSupplierAssociationSupplierServiceTypes
                    .All(y => y.SupplierServiceTypeId != x))
                .Select(x => new OrganisationSupplierAssociationServiceType
                {
                    SupplierServiceTypeId = x,
                    AssociationId = entities.Association.Id
                }).ToList();

        List<OrganisationSupplierAssociationServiceType> serviceTypesToRemove =
            entities.Supplier.Organisations.FirstOrDefault(x => x.OrganisationId == entities.Association.OrganisationId)
                .OrganisationSupplierAssociationSupplierServiceTypes
                .Where(x => !request.TypeIds.Contains(x.SupplierServiceTypeId)).ToList();

        serviceTypesToRemove.ForEach(x => dbContext.OrganisationSupplierAssociationServiceType.Remove(x));
        newServiceTypes.ForEach(x => entities.Supplier.Organisations.FirstOrDefault(y => y.OrganisationId == entities.Association.OrganisationId).OrganisationSupplierAssociationSupplierServiceTypes.Add(x));
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}