using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Dtos.Products;
using Theia.App.Shared.Supplier.Products;
using Theia.Application.Interfaces;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.Products;

[ApiAuthorize(PermissionType = PermissionTypes.ReadProducts)]
public class GetProducts(
        IApplicationDbContext dbContext,
        ICommonServices commonServices)
    : EndpointBaseAsync.WithRequest<GetProductsRequest>.WithResult<DataEnvelope<GetProductsResponseProduct>>
{
    [HttpPost("api/organisations/suppliers/products/get")]
    public override async Task<DataEnvelope<GetProductsResponseProduct>> HandleAsync(
        [FromBody] GetProductsRequest request,
        CancellationToken cancellationToken = new())
    {
        DataSourceResult products;
        Guid currentUsersOrganisationId = await commonServices.GetUsersCurrentOrganisationAsync().ConfigureAwait(false);
        if (currentUsersOrganisationId == Guid.Empty)
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        
        if (await dbContext.Suppliers
                .Where(x => x.Id == request.SupplierId)
                .Select(x => x.Status)
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false) == SupplierStatus.Assessment)
        {
            if (request.ShowAllProducts)
            {
                products =
                    await dbContext.Products
                        .Where(x =>
                            x.SupplierId == request.SupplierId
                            && !x.IsDeleted)
                        .Select(x => new GetProductsResponseProduct
                        {
                            Id = x.Id,
                            Name = x.Name,
                            Version = x.Version,
                            IsSupplierProduct = true,
                            IsActive = x.AssociatedProducts.Any(y =>
                                    y.OrganisationId == currentUsersOrganisationId),
                            IsInUse = true
                        })
                        .ToDataSourceResultAsync(request.DataSourceRequest)
                        .ConfigureAwait(false);
            }
            else
            {
                products =
                    await dbContext.OrganisationAssociatedSupplierProducts
                        .Where(x =>
                            x.SupplierProduct.SupplierId == request.SupplierId
                            && !x.SupplierProduct.IsDeleted
                            && x.OrganisationId == currentUsersOrganisationId)
                        .Select(x => new GetProductsResponseProduct
                        {
                            Id = x.SupplierProductId,
                            Name = x.SupplierProduct.Name,
                            Version = x.SupplierProduct.Version,
                            IsSupplierProduct = true,
                            IsActive = x.IsActive,
                            IsInUse = true
                        })
                        .ToDataSourceResultAsync(request.DataSourceRequest)
                        .ConfigureAwait(false);
            }
        }
        else
        {
            products =
                await dbContext.OrganisationSuggestedSupplierProducts
                    .Where(x =>
                        x.OrganisationId == currentUsersOrganisationId
                        && x.SuggestedForSupplierId == request.SupplierId
                        && !x.IsDeleted)
                    .Select(x => new GetProductsResponseProduct
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Version = x.Version,
                        IsSupplierProduct = false,
                        IsActive = x.IsActive,
                        IsInUse = false
                    })
                    .ToDataSourceResultAsync(request.DataSourceRequest)
                    .ConfigureAwait(false);
        }

        return DataEnvelope<GetProductsResponseProduct>.FromDataSourceResult(products, request.DataSourceRequest);
    }
}