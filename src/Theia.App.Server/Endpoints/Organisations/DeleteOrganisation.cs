using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.Domain.Entities.Organisations;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations;

[ApiAuthorize(PermissionType = PermissionTypes.DeleteOrganisation)]
public class DeleteOrganisation(
        ApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpDelete("api/organisations/{id:required}")]
    public override async Task HandleAsync(
        [FromRoute] WebSafeGuid id, CancellationToken cancellationToken = new())
    {
        Guid? tenantId = tenantResolverService.GetTenantId();
        if (tenantId is null)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, HttpStatusCode.NotFound.ToInt());
        }

        if (!dbContext.OrgBrokingAssociations.Any(x => x.OrgId == id && x.BrokingId == tenantId))
        {
            throw new ApiProblemDetailsException(Resource.Cannot_access_tenant, HttpStatusCode.Forbidden.ToInt());
        }
        
        Organisation? organisation = await dbContext
            .Organisations
            .FirstOrDefaultAsync(x => x.Id == id, cancellationToken)
            .ConfigureAwait(false);

        if (organisation is null)
        {
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, HttpStatusCode.NotFound.ToInt());
        }

        dbContext.Organisations.Remove(organisation);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}