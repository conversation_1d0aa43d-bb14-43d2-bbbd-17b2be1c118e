using Ardalis.ApiEndpoints;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.ApplicationForms;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Organisations.ApplicationForms;

[ApiAuthorize(PermissionType = PermissionTypes.OrganisationRetrieveApplicationForms)]
public class GetAppFormsForOrganisation(
    IApplicationDbContext dbContext,
    ITenantResolverService tenantResolverService) 
    : EndpointBaseAsync.WithRequest<GetAppFormsRequest>.WithResult<DataEnvelope<GetAppFormsResponseItem>>
{
    [HttpPost("api/organisations/forms")]
    public override async Task<DataEnvelope<GetAppFormsResponseItem>> HandleAsync(GetAppFormsRequest request, CancellationToken cancellationToken = new())
    {
        Guid tenantId = tenantResolverService.EnsureTenantId();

        DataSourceResult result =
            await dbContext.ApplicationForms
                .Where(x => x.ApplicationFormType == ApplicationFormType.Supplier)
                .Select(x => new GetAppFormsResponseItem
                {
                    ApplicationFormId = x.Id,
                    ApplicationFormName = x.Name,
                    Versions = x.Versions
                        .Where(y => y.IsActive
                                    && y.SurveyJson != null
                                    && y.IsComplete)
                        .Select(y => new GetAppFormVersionsResponseItem
                    {
                        InUse = y.Suppliers
                            !.Where(s =>
                                s.SupplierId == request.SupplierId
                                && s.Supplier!.CanBeAccessedByOrganisation(tenantId))
                            .Any(fv =>
                                fv.Supplier!.SubmissionRequests
                                    !.Where(sr => sr.CompletedOnUtc.HasValue && sr.Organisation!.TenantId == tenantId)
                                    .OrderByDescending(sr => sr.CompletedOnUtc)
                                    .FirstOrDefault()
                                    !.Snapshots
                                    !.Any(s =>
                                        s.SupplierApplicationFormVersionSnapshot!.SupplierApplicationFormVersion!.ApplicationFormVersionId == y.Id)),
                        ApplicationFormVersion = y.Version,
                        ApplicationFormVersionId = y.Id
                    }).ToArray()
                })
                .Where(x => x.Versions.Any())
                .ToDataSourceResultAsync(request.DataSourceRequest)
                .ConfigureAwait(false);

        return result;
    }
}