using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Theia.App.Server.Models.Requests;
using Theia.App.Server.Services;
using Theia.App.Shared.Models;
using Theia.Application.Exceptions;
using Theia.Application.Models.Excel.Suppliers;
using Theia.Application.Models.Excel.Suppliers.Validators;
using Theia.Application.Services;
using Theia.Application.Services.Excel;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.Submissions;
using Theia.Email.Contract;
using Theia.Email.Contract.Models;
using Theia.Email.Contract.TemplatesData;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Admin.Import;

[ApiAuthorize(PermissionType = PermissionTypes.AdminManageSuppliers)]
public class ImportSupplierExcelData(
        IApplicationDbContext dbContext,
        ExcelProcessingService excelProcessingService,
        UrlService urlService,
        TimeProvider timeProvider,
        IEmailService emailService,
        IUserService userService)
    : EndpointBaseAsync.WithRequest<ImportSupplierInformationRequest>.WithoutResult
{
    private const string AlliantApplicationFormName = "Alliant Supplier Assessment";
    
    [HttpPost("api/admin/import/suppliers/{OrganisationId:guid:required}")]
    public override async Task HandleAsync(ImportSupplierInformationRequest request, CancellationToken cancellationToken = new())
    {
        byte[] fileContent = await request.File.OpenReadStream().ToByteArrayAsync(cancellationToken);
        
        await using MemoryStream memoryStream = new(fileContent);
        SuppliersExcelDocument document = excelProcessingService.ProcessSuppliersExcelFile(memoryStream);
        
        Supplier newSupplier = await CreateSupplierAndApplicationFormAsync(document.SupplierContactDetails, request.OrganisationId, document.SupplierQuestions);
        await AddSuppliersProductsAsync(document.SupplierProducts, newSupplier);
        await dbContext.SaveChangesAsync(cancellationToken);
        
        string organisationName = await dbContext.Organisations
            .Where(x => x.Id == request.OrganisationId)
            .Select(x => x.Name).SingleAsync(cancellationToken);
        await SendEmailToNewSupplierAsync(newSupplier, organisationName, cancellationToken);
    }
    
    private async Task<Supplier> CreateSupplierAndApplicationFormAsync(SuppliersContactModel contact, Guid organisationId, SuppliersQuestionsModel[] questions)
    {
        SuppliersContactValidator validator = new();
        await validator.ValidateAndThrowAsync(contact);
        
        if (await dbContext.Suppliers.AnyAsync(x => x.CompanyNumber == contact.CompanyNumber))
            throw new ApiProblemDetailsException(Resource.Supplier_already_exists, StatusCodes.Status400BadRequest);
        
        Organisation? existingOrganisationEntity = await dbContext.Organisations
            .SingleOrDefaultAsync(x => x.Id == organisationId);
        
        if (existingOrganisationEntity is null)
            throw new ApiProblemDetailsException(Resource.Organisation_not_found, StatusCodes.Status400BadRequest);
        
        var dbForm = await dbContext.ApplicationFormVersions
            .Where(x => x.ApplicationForm.Name == AlliantApplicationFormName)
            .OrderByDescending(x => x.Version)
            .Select(x => new
            {
                x.SurveyJson,
                ApplicationFormVersionId = x.Id
            })
            .FirstOrDefaultAsync();
        
        if (dbForm is null)
            throw new FormNotFoundException();
        
        ApplicationFormSurveyJsonModel formJson = JsonSerializer.Deserialize<ApplicationFormSurveyJsonModel>(dbForm.SurveyJson!) ?? throw new FormNotFoundException();
        ElementsModel[] allQuestionsAndChoices = formJson.pages
            .SelectMany(x => x.elements)
            .Select(x => x)
            .ToArray();

        Dictionary<string, ApplicationFormAnswers> answers = allQuestionsAndChoices
            .ToDictionary(
                x => x.name,
                x => new ApplicationFormAnswers
                {
                    answer = questions.SingleOrDefault(y => y.QuestionName == x.title)?.Answer,
                    isCorrect = false,
                    AnswerType = AnswerType.SingleValue,
                    comment = string.Empty,
                    ArrayOfAnswers = null
                });
        
        if (await dbContext.Suppliers.AnyAsync(x => x.Name == contact.Name || x.CompanyNumber == contact.CompanyNumber))
            throw new ApiProblemDetailsException(Resource.Supplier_already_exists, StatusCodes.Status400BadRequest);
        
        Supplier newSupplier = new()
        {
            CompanyNumber = contact.CompanyNumber,
            ContactEmail = contact.ContactEmail, 
            Name = contact.Name,
            Status = SupplierStatus.NotRegistered,
            Address = contact.Address,
            Organisations = [new OrganisationSupplierAssociation
            {
                OrganisationId = organisationId,
                SupplierId = Guid.Empty,
                Supplier = null,
                Organisation = null,
                OrganisationSupplierAssociationSupplierServices = !string.IsNullOrWhiteSpace(contact.SupplierServices) ? [new OrganisationSupplierAssociationSupplierService
                {
                    OrganisationSupplierAssociationId = Guid.Empty,
                    SupplierServiceId = dbContext.SupplierServices.FirstOrDefault(x => contact.SupplierServices.Contains(x.Name))?.Id ?? Guid.Empty,
                    OrganisationSupplierAssociation = null,
                    SupplierService = null
                }] : [],
                OrganisationSupplierAssociationSupplierServiceTypes = [new OrganisationSupplierAssociationServiceType
                {
                    AssociationId = Guid.Empty,
                    Association = null,
                    SupplierServiceTypeId = dbContext.SupplierServiceTypes.FirstOrDefault(x => contact.SupplierType.Contains(x.Type))?.Id ?? Guid.Empty,
                    SupplierServiceType = null
                }]
            }]
        };
        await dbContext.Suppliers.AddAsync(newSupplier);

        SupplierSubmissionRequest newSupplierSubmissionRequest = new()
        {
            OrganisationId = organisationId,
            SupplierId = newSupplier.Id,
            CreatedOnUtc = timeProvider.GetUtcNow(),
            CreatedByUserId = userService.EnsureAuthId(),
            CreatedByUser = null,
            ApplicationFormVersions = [new SupplierSubmissionApplicationFormVersion
            {
                SupplierApplicationFormVersionId = Guid.Empty,
                SupplierApplicationFormVersion = new()
                {
                    ApplicationFormVersionId = dbForm.ApplicationFormVersionId,
                    SupplierId = newSupplier.Id,
                    Supplier = null,
                    ApplicationFormVersion = null,
                    RequestedOn = timeProvider.GetUtcNow(),
                    Status = ApplicationFormStatus.Assigned,
                    SurveyAnswers = JsonSerializer.Serialize(answers)
                },
                SupplierSubmissionId = Guid.Empty,
                SupplierSubmission = null
            }],
            Supplier = newSupplier,
            Organisation = existingOrganisationEntity
        };
        await dbContext.SupplierSubmissions.AddAsync(newSupplierSubmissionRequest);
        
        return newSupplier;
    }
    
    private async Task AddSuppliersProductsAsync(SuppliersProductModel[] products, Supplier newSupplier)
    {
        await dbContext.Products.AddRangeAsync(products
            .DistinctBy(x => new { x.ProductName, x.Version })
            .Select(x => new Product
            {
                SupplierId = newSupplier.Id,
                Name = x.ProductName,
                Version = x.Version,
                Supplier = null,
                AssociatedProducts = [new OrganisationAssociatedSupplierProduct
                {
                    OrganisationId = newSupplier.Organisations.First().OrganisationId,
                    SupplierProductId = Guid.Empty,
                    IsActive = true
                }]
            }));
    }
    
    private async Task SendEmailToNewSupplierAsync(Supplier newSupplier, string organisationName, CancellationToken cancellationToken = new())
    {
        string urlParameter = urlService.GetRegisterNewSupplierUrlAsync(
            new ExpiringUrl<Guid>(newSupplier.Id, timeProvider.GetUtcNow().AddDays(1)));

        await emailService.SendEmailAsync(
            [newSupplier.ContactEmail],
            new InviteNewSupplierTemplate(newSupplier.Name, organisationName, urlParameter),
            cancellationToken);
    }
}