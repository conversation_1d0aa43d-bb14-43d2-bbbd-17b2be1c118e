using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Theia.Domain.Entities.Settings.IdentitySettings;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Admin.AppSettings;

[ApiAuthorize(PermissionType = PermissionTypes.UpdateIdentitySettings)]
public class UpdateIdentitySettings(
        IApplicationDbContext dbContext,
        IConfigReaderService configReaderService,
        IOptions<IdentityOptions> identityOptions)
    : EndpointBaseAsync.WithRequest<UpdateIdentitySettingsCommand>.WithResult<IdentitySettingsResponse>
{
    [HttpPut("api/AppSettings/UpdateIdentitySettings")]
    public override async Task<IdentitySettingsResponse> HandleAsync(
        UpdateIdentitySettingsCommand request, CancellationToken cancellationToken = new())
    {
        if (!Guid.TryParse(request.PasswordSettingsCommand.Id, out Guid passwordSettingsId))
        {
            throw new ApiProblemDetailsException(Resource.Invalid_password_settings_Id, StatusCodes.Status400BadRequest);
        }

        if (!Guid.TryParse(request.LockoutSettingsCommand.Id, out Guid lockoutSettingsId))
        {
            throw new ApiProblemDetailsException(Resource.Invalid_lockout_settings_Id, StatusCodes.Status400BadRequest);
        }

        PasswordSettings passwordSettings =
            await dbContext.PasswordSettings.FirstOrDefaultAsync(ps => ps.Id == passwordSettingsId, cancellationToken: cancellationToken).ConfigureAwait(false) ??
            configReaderService.GetAppPasswordOptions().MapToEntity();

        LockoutSettings lockoutSettings =
            await dbContext.LockoutSettings.FirstOrDefaultAsync(ls => ls.Id == lockoutSettingsId, cancellationToken: cancellationToken).ConfigureAwait(false) ??
            configReaderService.GetAppLockoutOptions().MapToEntity();

        dbContext.PasswordSettings.Update(passwordSettings);
        dbContext.LockoutSettings.Update(lockoutSettings);

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
        
        identityOptions.Value.Password.RequiredLength = passwordSettings.RequiredLength;
        identityOptions.Value.Password.RequiredUniqueChars = passwordSettings.RequiredUniqueChars;
        identityOptions.Value.Password.RequireNonAlphanumeric = passwordSettings.RequireNonAlphanumeric;
        identityOptions.Value.Password.RequireLowercase = passwordSettings.RequireLowercase;
        identityOptions.Value.Password.RequireUppercase = passwordSettings.RequireUppercase;
        identityOptions.Value.Password.RequireDigit = passwordSettings.RequireDigit;

        IdentitySettingsResponse identitySettingsResponse = new()
        {
            LockoutSettingsId = lockoutSettings.Id,
            PasswordSettingsId = passwordSettings.Id,
            SuccessMessage = Resource.Identity_settings_have_been_updated_successfully
        };

        return identitySettingsResponse;
    }
}