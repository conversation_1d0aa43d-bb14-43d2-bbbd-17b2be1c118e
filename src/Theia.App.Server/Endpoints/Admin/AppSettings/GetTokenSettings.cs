using Ardalis.ApiEndpoints;
using Theia.Application.Common.Interfaces.UseCases.Settings;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Admin.AppSettings;

[ApiAuthorize(PermissionType = PermissionTypes.GetTokenSettings)]
public class GetTokenSettings(
        IAppSettingsUseCase appSettingsUseCase)
    : EndpointBaseAsync.WithoutRequest.WithResult<TokenSettingsForEditResponse>
{
    [HttpGet("api/AppSettings/GetTokenSettings")]
    public override async Task<TokenSettingsForEditResponse> HandleAsync(
        CancellationToken cancellationToken = new())
    {
       return await appSettingsUseCase.GetTokenSettings();
    }
}