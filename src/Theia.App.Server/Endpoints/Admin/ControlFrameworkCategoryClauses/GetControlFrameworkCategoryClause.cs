using Ardalis.ApiEndpoints;
using Theia.Application.Common.Interfaces.UseCases.SecurityControlFramework;
using Theia.Application.Features.SecurityControlFramework.ControlFrameworkCategoryClauses.Queries.GetControlFrameworkCategoryClauseForEdit;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Admin.ControlFrameworkCategoryClauses;

[ApiAuthorize(PermissionType = PermissionTypes.GetControlFrameworkCategoryClause)]
public class GetControlFrameworkCategoryClause(
        IControlFrameworkCategoryClauseUseCase controlFrameworkCategoryClauseUseCase)
    : EndpointBaseAsync.WithRequest<GetControlFrameworkCategoryClauseForEditQuery>.WithResult<ControlFrameworkCategoryClauseForEditResponse>
{
    [HttpGet("api/ControlFrameworkCategoryClauses/GetControlFrameworkCategoryClause")]
    public override async Task<ControlFrameworkCategoryClauseForEditResponse> HandleAsync(
        GetControlFrameworkCategoryClauseForEditQuery request,
        CancellationToken cancellationToken = new())
    {
       return await controlFrameworkCategoryClauseUseCase.GetControlFrameworkCategoryClause(request);
    }
}