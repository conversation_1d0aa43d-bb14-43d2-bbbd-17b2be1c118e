using Ardalis.ApiEndpoints;
using Theia.App.Shared.Submissions;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Questions;

[ApiAuthorize(PermissionType = PermissionTypes.ManageSubmissionQuestion)]
public class AddSubmissionQuestion(
    IApplicationDbContext dbContext, 
    ISubmissionPermissionService submissionPermissionService,
    IHttpContextAccessor httpContextAccessor) 
    : EndpointBaseAsync.WithRequest<AddSubmissionQuestionRequest>.WithoutResult
{
    [HttpPost("api/submissions/questions")]
    public override async Task HandleAsync(AddSubmissionQuestionRequest request, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService
            .CheckReadAccessOrThrowAsync(
                brokerSubmissionId: request.BrokerSubmissionId,
                cancellationToken: cancellationToken)
            .ConfigureAwait(true);

        string currentUserId = httpContextAccessor.EnsureAuthId();
        
        SubmissionQuestion question = new()
        {
            Content = request.Content,
            BrokerSubmissionId = request.BrokerSubmissionId,
            AskedById = currentUserId,
            AskedAt = DateTime.Now,
            BrokerSubmission = null,
            AskedBy = null
        };

        dbContext.SubmissionQuestions.Add(question);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}