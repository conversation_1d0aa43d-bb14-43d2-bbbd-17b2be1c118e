using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.Application.Services;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Questions;

[ApiAuthorize(PermissionType = PermissionTypes.ManageSubmissionQuestion)]
public class DeleteSubmissionQuestion(
        IApplicationDbContext dbContext,
        SubmissionQuestionPermissionService submissionQuestionPermissionService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpDelete("api/submissions/questions/{questionId:required}")]
    public override async Task HandleAsync(
        [FromRoute] WebSafeGuid questionId,
        CancellationToken cancellationToken = new())
    {
        await submissionQuestionPermissionService.CheckAccessOrThrowAsync(questionId, cancellationToken);

        SubmissionQuestion? question =
            await dbContext.SubmissionQuestions
                .SingleOrDefaultAsync(sq => sq.Id == questionId, cancellationToken);

        if (question is null)
        {
            throw new ApiProblemDetailsException(Resource.Question_not_found, StatusCodes.Status404NotFound);
        }
        
        dbContext.SubmissionQuestions.Remove(question);
        await dbContext.SaveChangesAsync(cancellationToken);
    }
}