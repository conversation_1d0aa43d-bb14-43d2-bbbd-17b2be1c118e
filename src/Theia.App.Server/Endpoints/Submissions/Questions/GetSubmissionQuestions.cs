using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Submissions;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Questions;

[ApiAuthorize(PermissionType = PermissionTypes.GetSubmissionQuestions)]
public class GetSubmissionQuestions(
        IApplicationDbContext dbContext,
        IHttpContextAccessor httpContextAccessor,
        ISubmissionPermissionService submissionPermissionService,
        IRoleService roleService) 
    : EndpointBaseAsync.WithRequest<GetSubmissionsQuestionsRequest>.WithResult<GetSubmissionsQuestionsResponse>
{
    private readonly string _userId = httpContextAccessor.GetAuthId();

    [HttpGet("api/submissions/{BrokerSubmissionId}/questions")]
    public override async Task<GetSubmissionsQuestionsResponse> HandleAsync(
        [FromRoute] GetSubmissionsQuestionsRequest request, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(
            brokerSubmissionId: request.BrokerSubmissionId,
            cancellationToken: cancellationToken).ConfigureAwait(true);

        IQueryable<SubmissionQuestion> query =
            dbContext
                .SubmissionQuestions
                .Where(sq => sq.BrokerSubmissionId == request.BrokerSubmissionId);

        if (roleService.IsUserInOrganisationRole)
        {
            query = query.Where(sq => sq.Status == SubmissionsQuestionStatus.Approved);
        }
        
        GetSubmissionsQuestionsQuestion[] submissionQuestions =
            await query
                .Select(sq => new GetSubmissionsQuestionsQuestion(
                    (WebSafeGuid)sq.Id,
                    sq.AskedAt,
                    sq.AskedBy.Name,
                    sq.AskedBy.Surname,
                    sq.Content,
                    sq.Status,
                    sq.EditedAt,
                    sq.Answers
                        .Select(sqa =>
                            new GetSubmissionsQuestionsAnswer(
                                sqa.Id,
                                sqa.AnsweredAt,
                                sqa.AnsweredBy.Name,
                                sqa.AnsweredBy.Surname,
                                sqa.Content,
                                sqa.AnsweredById == _userId,
                                sqa.EditedAt))
                        .ToArray()
                ))
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(true);

        return new GetSubmissionsQuestionsResponse(request.BrokerSubmissionId, submissionQuestions); 
    }
}