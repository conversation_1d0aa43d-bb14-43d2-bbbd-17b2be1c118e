using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Organisation.Submissions;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.Suppliers;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.Submissions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Suppliers;

[ApiAuthorize(PermissionType = PermissionTypes.AddSubmissionSupplier)]
public class AddSuppliers(IApplicationDbContext dbContext, ISubmissionPermissionService submissionPermissionService, ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithRequest<AddSuppliersRequest>.WithoutResult
{
    [HttpPost("api/submissions/suppliers")]
    public override async Task HandleAsync(AddSuppliersRequest request, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(cancellationToken, request.SubmissionId).ConfigureAwait(false);

        Guid[] supplierIds = request.Suppliers.Select(s => s.Id).ToArray();
        
        Submission? submission = 
            await dbContext.Submissions
                .Include(x => x.Suppliers)
                .ThenInclude(x => x.ApplicationFormsVersionSnapshots)
                .Include(x => x.Suppliers)
                .ThenInclude(x => x.Files)
                .FirstOrDefaultAsync(s => s.Id == request.SubmissionId, cancellationToken)
                .ConfigureAwait(false);

        if (submission is null)
        {
            throw new ApiProblemDetailsException(Resource.Submission_not_found, StatusCodes.Status404NotFound);
        }

        Guid? tenantId = tenantResolverService.GetTenantId();
        
        Supplier[] suppliers =
            await dbContext.Suppliers
                .Where(s => 
                    supplierIds.Contains(s.Id) 
                    && s.Organisations
                        .Any(o => o.Organisation.TenantId == tenantId && !o.Inactive))
                .Include(x => x.Files)
                .ThenInclude(x => x.AssociatedSubmissionFiles)
                .Include(x => x.ApplicationFormVersions)
                .ThenInclude(x => x.Snapshots)
                .Include(x => x.SubmissionRequests.Where(sr => sr.Status == SupplierSubmissionRequestStatus.Completed))
                .ThenInclude(x => x.Snapshots)
                .ThenInclude(x => x.SupplierApplicationFormVersionSnapshot)
                .ThenInclude(x => x.SupplierApplicationFormVersion)
                .Include(x => x.Products)
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(false);

        if (suppliers is null)
        {
            throw new ApiProblemDetailsException(Resource.Supplier_is_not_found, StatusCodes.Status404NotFound);
        }
        
        submission.Suppliers.RemoveAll(s => !supplierIds.Contains(s.SupplierId));

        foreach (Supplier supplier in suppliers)
        {
            AddSupplierRequestSupplier? requestSupplier = request.Suppliers.SingleOrDefault(s => s.Id == supplier.Id);
            if (requestSupplier is not null)
            {
                EditSupplier(submission, supplier, requestSupplier);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }

    private void EditSupplier(
        Submission submission,
        Supplier supplier,
        AddSupplierRequestSupplier requestSupplier)
    {
        Guid[] appFormIds = requestSupplier.SupplierAppFormVersionIds ?? [];
        Guid[] fileIds = requestSupplier.SupplierFilesIds ?? [];
        SupplierSubmissionRequestFile[] selectedFiles = [];
        if (fileIds is { Length: > 0 })
        {
            selectedFiles = dbContext.SupplierSubmissionRequestFile
                .Where(x => fileIds.Contains(x.Id))
                .ToArray();
        }

        var latestSupSubRequest =
            supplier
                .SubmissionRequests
                .Where(sr => sr.Status == SupplierSubmissionRequestStatus.Completed)
                .OrderByDescending(sr => sr.CompletedOnUtc)
                .Select(sr => new
                {
                    sr.Id,
                    Snapshots =
                        sr.Snapshots
                            .Select(s => s.SupplierApplicationFormVersionSnapshot)
                            .Where(s => appFormIds.Contains(s.SupplierApplicationFormVersionId.Value))
                            .ToArray()
                })
                .FirstOrDefault();

        SubmissionSupplier existingSubmissionSupplier = submission.Suppliers.SingleOrDefault(x => x.SupplierId == requestSupplier.Id);
        if (existingSubmissionSupplier is null)
        {
            SubmissionSupplier newSubmissionSupplier = new()
            {
                SubmissionId = submission.Id,
                Submission = submission,
                SupplierId = requestSupplier.Id,
                Supplier = null,
                ApplicationFormsVersionSnapshots =
                    latestSupSubRequest?
                        .Snapshots
                        .Select(s => new SubmissionSupplierAppFormVersionSnapshot
                        {
                            Snapshot = s,
                            SnapshotId = Guid.Empty,
                            SubmissionSupplier = null,
                            SubmissionsSubmissionId = Guid.Empty,
                            TheiaAnalysisJobId = s.TheiaAnalysisJobId,
                            TheiaAnalysisJob = null
                        })
                        .ToList(),
                Files =
                    selectedFiles?
                        .Select(f => new SubmissionSupplierSupplierFile
                        {
                            SubmissionsSubmissionId = Guid.Empty,
                            SubmissionSupplier = null,
                            AssociatedFileId = Guid.Empty,
                            AssociatedFile = f
                        })
                        .ToList(),
                SupplierSubmissionRequestId = latestSupSubRequest?.Id,
                SupplierSubmissionRequest = null,
                AssociatedProducts = null
            };

            dbContext.SubmissionSupplier.Add(newSubmissionSupplier);
        }
        else
        {
            IEnumerable<SubmissionSupplierAppFormVersionSnapshot> newSnapshots =
                latestSupSubRequest?
                    .Snapshots
                    .Where(x => existingSubmissionSupplier.ApplicationFormsVersionSnapshots.All(y => y.SnapshotId != x.Id))
                    .Select(x => new SubmissionSupplierAppFormVersionSnapshot
                    {
                        SubmissionsSubmissionId = Guid.Empty,
                        SubmissionSupplier = null,
                        SnapshotId = x.Id,
                        Snapshot = null,
                        TheiaAnalysisJobId = x.TheiaAnalysisJobId,
                        TheiaAnalysisJob = null
                    });

            if(newSnapshots is not null) existingSubmissionSupplier.ApplicationFormsVersionSnapshots?.AddRange(newSnapshots);

            if (latestSupSubRequest != null)
            {
                existingSubmissionSupplier.ApplicationFormsVersionSnapshots?
                    .RemoveAll(x => 
                        latestSupSubRequest.Snapshots.All(y => y.Id != x.SnapshotId));
            }

            IEnumerable<SubmissionSupplierSupplierFile> newFiles =
                selectedFiles
                    .Where(f => existingSubmissionSupplier.Files.All(subFile => subFile.AssociatedFileId != f.Id))
                    .Select(f => new SubmissionSupplierSupplierFile
                    {
                        SubmissionsSubmissionId = Guid.Empty,
                        SubmissionSupplier = null,
                        AssociatedFileId = Guid.Empty,
                        AssociatedFile = f
                    });

            existingSubmissionSupplier.Files?.AddRange(newFiles);
            existingSubmissionSupplier.Files?.RemoveAll(f => selectedFiles.All(sf => sf.Id != f.AssociatedFile.Id));
        }
    }
}