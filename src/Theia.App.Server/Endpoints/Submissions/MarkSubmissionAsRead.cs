using Ardalis.ApiEndpoints;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class MarkSubmissionAsRead(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    ISubmissionPermissionService submissionPermissionService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithoutResult
{
    [HttpPost("api/submissions/{submissionId:required}/mark-as-read")]
    public override async Task HandleAsync([FromRoute] WebSafeGuid submissionId, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService
            .CheckReadAccessOrThrowAsync(cancellationToken, submissionId)
            .ConfigureAwait(true);

        string currentUserId = httpContextAccessor.GetAuthId();

        if (dbContext.SubmissionViews
            .Any(x =>
                x.ViewedById == currentUserId
                && x.SubmissionId == submissionId))
        {
            return;
        }

        SubmissionView view = new()
        {
            ViewedById = currentUserId,
            SubmissionId = submissionId,
            Submission = null,
            ViewedBy = null
        };

        dbContext.SubmissionViews.Add(view);
        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(true);
    }
}