using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Submissions.ProgramStack.GetOptionDetails;
using Theia.Application.Services.Layers;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.ProgramStack;

[ApiAuthorize(PermissionType = PermissionTypes.ReadLayers)]
public class GetIndicationOptionDetails(
    IApplicationDbContext dbContext,
    LayerPermissionService layerPermissionService) 
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<IndicationOptionDetailsResponse>
{
    [HttpGet("api/submissions/program-stack/options/{optionId:guid:required}")]
    public override async Task<IndicationOptionDetailsResponse> HandleAsync(
        [FromRoute] Guid optionId, 
        CancellationToken cancellationToken = new())
    {
        var option =
            await dbContext.Options
                .Where(o => o.Id == optionId)
                .Select(o => new
                {
                    o.Id,
                    o.Premium,
                    AggregateLimitOfLiability = o.LimitOfLiability,
                    o.InsurerLine,
                    IndicationDate = EF.Property<DateTimeOffset>(o.Indication!, ShadowProperties.CreatedOn),
                    InsurerName = o.Indication!.Insurer!.Name,
                    o.Indication.IndicationRequest!.LayerId,
                    o.Indication.IndicationRequest.Layer!.TargetPremium,
                    o.Indication.IndicationRequest.Layer!.Currency,
                    o.Indication.IndicationRequestId,
                    o.IndicationId
                })
                .FirstOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (option is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        await layerPermissionService.CheckOrThrowAsync(option.LayerId, cancellationToken).ConfigureAwait(false);

        return new IndicationOptionDetailsResponse
        {
            OptionId = option.Id,
            IndicationDate = option.IndicationDate,
            Insurer = option.InsurerName,
            TargetPremium = option.TargetPremium,
            LimitOfLiability = option.AggregateLimitOfLiability,
            Premium = option.Premium,
            Line = option.InsurerLine,
            Currency = option.Currency,
            IndicationRequestId = option.IndicationRequestId,
            IndicationId = option.IndicationId
        };
    }
}