using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Submissions.Files;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Files;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class GetBrokerSubmissionFiles(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService,
        ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithRequest<WebSafeGuid>.WithResult<GetSubmissionFilesResponse>
{
    [HttpGet("api/submissions-broker/{brokerSubmissionId}/files")]
    public override async Task<GetSubmissionFilesResponse> HandleAsync(
        [FromRoute] WebSafeGuid brokerSubmissionId,
        CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(brokerSubmissionId: brokerSubmissionId,
                cancellationToken: cancellationToken)
            .ConfigureAwait(true);

        GetSubmissionFilesResponseFile[] files =
            await dbContext
                .BrokerSubmissionSubmissionFiles
                .Join(dbContext.SubmissionFiles,
                    bssf => bssf.SubmissionFileId,
                    sf => sf.Id,
                    (bssf, sf) => new { Sf = sf, Bssf = bssf })
                .Where(model => model.Bssf.BrokerSubmissionId == brokerSubmissionId)
                .Select(model => new GetSubmissionFilesResponseFile(
                    model.Sf.Id,
                    model.Sf.OriginalName,
                    model.Sf.TenantId == tenantResolverService.GetTenantId()))
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(true);

        return new GetSubmissionFilesResponse(files);
    }
}