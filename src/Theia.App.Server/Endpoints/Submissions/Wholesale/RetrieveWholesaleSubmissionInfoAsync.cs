using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.DTOs.WholesaleSubmissions;
using Theia.Application.Services;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Submissions.Wholesale;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class RetrieveWholesaleSubmissionInfoAsync(
        IApplicationDbContext dbContext,
        ISubmissionPermissionService submissionPermissionService,
        ITenantResolverService tenantResolverService)
    : EndpointBaseAsync.WithRequest<Guid>.WithResult<WholesaleSubmissionInfoResponse>
{
    [HttpGet("api/submissions/wholesale/{wholesaleSubmissionId:guid:required}/info")]
    public override async Task<WholesaleSubmissionInfoResponse> HandleAsync(
        [FromRoute] Guid wholesaleSubmissionId, CancellationToken cancellationToken = new())
    {
        await submissionPermissionService.CheckReadAccessOrThrowAsync(submissionId: wholesaleSubmissionId, cancellationToken: cancellationToken).ConfigureAwait(false);
        Guid tenantId = tenantResolverService.EnsureTenantId();
        
        var layerAndWholesaleSubmission = await dbContext.WholesaleSubmissions
            .Where(x => x.Id == wholesaleSubmissionId)
            .Select(x => new
            {
                x.IndicationRequestId,
                x.IndicationRequest.ShowUnderlying,
                x.IndicationRequest.LimitOfLiability,
                Layer = new 
                {
                    x.IndicationRequest.Layer.Id,
                    x.IndicationRequest.Layer.Limit,
                    x.IndicationRequest.Layer.Excess,
                    x.IndicationRequest.Layer.TargetPremium,
                    x.IndicationRequest.Layer.Currency
                },
                x.HasBeenSentBackToBroker,
                x.IsApproved,
                IsShareableWithRetail = x.IsShareableWithRetail(),
                IsUserPartOfWholesaleBroker = x.BrokingHouse.Tenant.Id == tenantId,
                IsUserPartOfRequestingTenant = x.PrimaryBrokingHouseTenantId == tenantId,
                IsUserPartOfOrganisation = x.RequestedForOrganisationId == dbContext.Organisations
                    .Where(o => o.TenantId == tenantId)
                    .Select(o => o.Id)
                    .SingleOrDefault()
            })
            .SingleOrDefaultAsync(cancellationToken)
            .ConfigureAwait(false);
        
        if (layerAndWholesaleSubmission is null)
        {
            throw new ApiProblemDetailsException(Resource.Layer_not_found, StatusCodes.Status404NotFound);
        }

        var wholesaleData =
            await dbContext.WholesaleSubmissions
                .Where(x => x.Id == wholesaleSubmissionId)
                .OrderBy(x => EF.Property<DateTimeOffset>(x.IndicationRequest.Layer,
                    ShadowProperties.CreatedOn))
                .Select(x => new { LayerId = x.IndicationRequest.Layer.Id})
                .ToArrayAsync(cancellationToken);

        return new WholesaleSubmissionInfoResponse
        {
            Limit = layerAndWholesaleSubmission.LimitOfLiability,
            Excess = layerAndWholesaleSubmission.Layer.Excess ?? 0,
            TargetPremium = layerAndWholesaleSubmission.Layer.TargetPremium ?? 0,
            IsSharedWithBroker = layerAndWholesaleSubmission.HasBeenSentBackToBroker,
            Currency = layerAndWholesaleSubmission.Layer.Currency,
            LayerPosition = wholesaleData
                            .TakeWhile(x => x.LayerId != layerAndWholesaleSubmission.Layer.Id)
                            .Count() + 1,
            LayerId = layerAndWholesaleSubmission.Layer.Id,
            IndicationRequestId = layerAndWholesaleSubmission.IndicationRequestId,
            ShowUnderlying = layerAndWholesaleSubmission.ShowUnderlying,
            IsUserPartOfRequestingTenant = layerAndWholesaleSubmission.IsUserPartOfRequestingTenant,
            IsUserPartOfOrganisation = layerAndWholesaleSubmission.IsUserPartOfOrganisation,
            IsUserPartOfWholesaleBroker = layerAndWholesaleSubmission.IsUserPartOfWholesaleBroker,
            IsApproved = layerAndWholesaleSubmission.IsApproved ?? false,
            IsShareableWithRetail = layerAndWholesaleSubmission.IsShareableWithRetail
        };
    }
}