using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Underwriting.Info;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Underwriters.Info.Brokers;

[ApiAuthorize(PermissionType = PermissionTypes.UnderwriterHomePageView)]
public class GetBrokingHousesCount(SubmissionsService submissionsService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetBrokingHousesCountResponse>
{
    [HttpGet("api/underwriter/info/broking-houses")]
    public override async Task<GetBrokingHousesCountResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        int brokingHouses =
            await submissionsService
                .GetAllBrokerSubmissionAvailableForCurrentUsersInsurerQuery()
                .Select(bsu => bsu.RequestedByBrokingHouseId)
                .Distinct()
                .CountAsync(cancellationToken)
                .ConfigureAwait(false);

        return new GetBrokingHousesCountResponse(brokingHouses);
    }
}