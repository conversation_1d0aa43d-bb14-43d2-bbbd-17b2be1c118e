using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Models;
using Theia.App.Shared.Underwriting.Info;
using Theia.Application.Common.Extensions;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Underwriters.Info;

[ApiAuthorize(PermissionType = PermissionTypes.UnderwriterHomePageView)]
public class GetOrganisationRevenueSplit(
    IApplicationDbContext dbContext,
    IHttpContextAccessor httpContextAccessor,
    ITenantResolverService tenantResolverService,
    SubmissionsService submissionsService)
    : EndpointBaseAsync.WithoutRequest.WithResult<GetOrganisationRevenueSplitResponse>
{
    [HttpGet("api/underwriter/info/org-revenue-split")]
    public override async Task<GetOrganisationRevenueSplitResponse> HandleAsync(CancellationToken cancellationToken = new())
    {
        int submissionsCount =
            await submissionsService
                .GetAllBrokerSubmissionAvailableForCurrentUsersInsurerQuery()
                .CountAsync(cancellationToken)
                .ConfigureAwait(true);

        if (submissionsCount < 3)
        {
            return new GetOrganisationRevenueSplitResponse([]);
        }

        int lastYear = DateTime.Now.Year - 1;
        
        string currentUserId = httpContextAccessor.GetAuthId();
        Guid? tenantId = tenantResolverService.GetTenantId();

        decimal[] revenueSplit =
            await dbContext.AnnualFinances
                .Where(af => af.Year == lastYear)
                .Where(af =>
                    af.Organisation
                        .Submissions
                        .SelectMany(o => o.Layers)
                        .SelectMany(x => x.IndicationRequests)
                        .Any(sbs => submissionsService.UserHasAccessToBrokerSubmissionInInsurerTenant(sbs, tenantId, currentUserId)))
                .Select(af => af.Value)
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(true);

        if (revenueSplit.Length == 0)
        {
            return new GetOrganisationRevenueSplitResponse([]);
        }

        return new GetOrganisationRevenueSplitResponse(
            new PieChartItem[]
            {
                new("> USD 2B", revenueSplit.Count(rs => rs > 2_000_000_000).ToString()), 
                new("USD 500K - USD 2B", revenueSplit.Count(rs => rs is <= 2_000_000_000 and >= 500_000).ToString()),
                new("< USD 500K", revenueSplit.Count(rs => rs < 500_000).ToString())
            });
    }
}