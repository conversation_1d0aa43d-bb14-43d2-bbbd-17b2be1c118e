using Ardalis.ApiEndpoints;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared;
using Theia.App.Shared.Underwriting.Models;
using Theia.Application.Services;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Underwriters.Organisations;

[ApiAuthorize(PermissionType = PermissionTypes.UnderwritingManageSubmission)]
public class GetOrganisationsForUnderwriter(OrganisationPermissionService organisationPermissionService)
    : EndpointBaseAsync.WithRequest<DataSourceRequest>.WithResult<DataEnvelope<UnderwritersOrganisations>>
{
    [HttpPost("api/underwriters/get-organisations-from-broker-submissions")]
    public override async Task<DataEnvelope<UnderwritersOrganisations>> HandleAsync(
        DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        DataSourceResult allOrganisationsForUnderwriter =
            await organisationPermissionService
                .GetAvailableOrganisations()
                .Select(model => new UnderwritersOrganisations
                {
                    OrganisationName = model.Name,
                    OrganisationId = model.Id,
                    ContactEmail = model.ContactEmail,
                    ContactNumber = model.ContactPhoneNumber,
                    ContactName = model.ContactName
                })
                .ToDataSourceResultAsync(request)
                .ConfigureAwait(true);

        return new DataEnvelope<UnderwritersOrganisations>
        {
            Data = allOrganisationsForUnderwriter.Data.Cast<UnderwritersOrganisations>().DistinctBy(x => x.OrganisationId),
            Total = allOrganisationsForUnderwriter.Total
        };
    }
}