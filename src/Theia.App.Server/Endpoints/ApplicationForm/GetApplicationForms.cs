using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Telerik.DataSource;
using Telerik.DataSource.Extensions;
using Theia.App.Shared.Admin.ApplicationForms;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ApplicationForm;

[ApiAuthorize(PermissionType = PermissionTypes.GetApplicationForms)]
public class GetApplicationForms(IApplicationDbContext dbContext)
    : EndpointBaseAsync.WithRequest<DataSourceRequest>.WithResult<GetApplicationFormsResponse>
{

    [HttpPost("api/get-application-forms")]
    public override async Task<GetApplicationFormsResponse> HandleAsync(
        DataSourceRequest request, CancellationToken cancellationToken = new())
    {
        DataSourceResult questionnaireItems =
            await dbContext.ApplicationForms
                .Include(applicationForm => applicationForm.Versions)
                .AsNoTracking()
                .Select(applicationForm =>
                    new {
                        applicationForm.Id,
                        applicationForm.ApplicationFormCode,
                        applicationForm.Name,
                        applicationForm.Description,
                        CreatedOn = EF.Property<DateTimeOffset?>(applicationForm, ShadowProperties.CreatedOn),
                        Versions = applicationForm.Versions
                            .OrderByDescending(v => EF.Property<DateTimeOffset?>(applicationForm, ShadowProperties.CreatedOn))
                            .Select(r =>
                                new GetApplicationFormsResponseVersion(
                                    r.Id,
                                    r.Version,
                                    EF.Property<DateTimeOffset?>(applicationForm, ShadowProperties.CreatedOn),
                                    r.IsComplete,
                                    r.InUse,
                                    r.IsActive))
                            .ToArray(),
                        applicationForm.ApplicationFormType})
                .ToDataSourceResultAsync(request,
                    applicationForm =>
                        new GetApplicationFormsResponseForm(
                            applicationForm.Id,
                            applicationForm.ApplicationFormCode,
                            applicationForm.Name,
                            applicationForm.Description,
                            applicationForm.CreatedOn,
                            applicationForm.Versions,
                            applicationForm.ApplicationFormType))
                .ConfigureAwait(true);

        return new GetApplicationFormsResponse
        {
            Data = questionnaireItems.Data.Cast<GetApplicationFormsResponseForm>(),
            Total = questionnaireItems.Total
        };
    }
}