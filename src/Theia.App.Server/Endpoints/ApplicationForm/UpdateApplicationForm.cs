using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Net;
using Theia.App.Shared.Admin.Commands.ApplicationForm.UpdateApplicationForm;
using Theia.Domain.Entities.ApplicationForms;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ApplicationForm;

[ApiAuthorize(PermissionType = PermissionTypes.EditApplicationFormWithFormBuilder)]
public class UpdateApplicationForm(
        IApplicationDbContext dbContext) :
    EndpointBaseAsync.WithRequest<UpdateApplicationFormCommand>.WithActionResult<string>
{
    [HttpPut("api/update-application-form")]
    public override async Task<ActionResult<string>> HandleAsync(
        UpdateApplicationFormCommand request, CancellationToken cancellationToken = new())
    {
        ApplicationFormVersion? applicationFormVersion =
            await dbContext.ApplicationFormVersions
                .Where(r => r.Id == request.Id)
                .FirstOrDefaultAsync(cancellationToken).ConfigureAwait(false);

        List<string> controlFrameworkReferences = new();
        List<string> controlFrameworkNames = new();
        foreach (string payloadItem in request.ControlFrameworkClauses)
        {
            int indexOfSplitter = payloadItem.IndexOf(" - ", StringComparison.Ordinal);
            string[] splitString = new string[2];
            splitString[0] = payloadItem.Substring(0, indexOfSplitter).Trim();
            splitString[1] = payloadItem.Substring(indexOfSplitter + 1).Trim();
            if (splitString[1].StartsWith("- "))
            {
                splitString[1] = splitString[1].Remove(0, 2);
            }

            Guid? clause = dbContext.ControlFrameworkCategoryClauses
                .AsNoTracking()
                .SingleOrDefault(x =>
                    x.Name == splitString[1]
                    && x.Reference == splitString[0])?.Id;
            if (clause is null)
            {
                throw new ApiProblemDetailsException(Resource.Control_framework_not_found, HttpStatusCode.NotFound.ToInt());
            }

            controlFrameworkReferences.Add(splitString[0]);
            controlFrameworkNames.Add(splitString[1]);
        }

        if (applicationFormVersion is null)
        {
            throw new ApiProblemDetailsException(Resource.Unable_to_load_form, HttpStatusCode.NotFound.ToInt());
        }

        if (applicationFormVersion.InUse)
        {
            throw new ApiProblemDetailsException(Resource.Cannot_edit_a_version_in_use, HttpStatusCode.Conflict.ToInt());
        }

        dynamic? survey = JsonConvert.DeserializeObject(request.SurveyJson) ?? null;
        if (survey is null)
        {
            throw new ApiProblemDetailsException(Resource.Application_form_version_data_malformed,
                HttpStatusCode.UnprocessableContent.ToInt());
        }

        survey.showPreviewBeforeComplete = "showAllQuestions";
        applicationFormVersion.SurveyJson = JsonConvert.SerializeObject(survey);

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        return Resource.Form_has_been_updated_successfully;
    }
}