using Ardalis.ApiEndpoints;
using System.Net;
using Theia.App.Shared.Admin.Commands.ApplicationForm.CreateApplicationForm;
using Theia.Application.Common.Interfaces.UseCases.Survey;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ApplicationForm;

[ApiAuthorize(PermissionType = PermissionTypes.CreateApplicationForm)]
public class CreateApplicationForm(IApplicationFormsUseCase applicationFormsUseCase) 
    : EndpointBaseAsync.WithRequest<CreateApplicationFormCommand>.WithActionResult<CreateApplicationFormResponse>
{
    [HttpPost("api/create-application-form")]
    public override async Task<ActionResult<CreateApplicationFormResponse>> HandleAsync(
        CreateApplicationFormCommand request, CancellationToken cancellationToken = new())
    {
       CreateApplicationFormResponse response =
            await applicationFormsUseCase
                .CreateApplicationFormAsync(request, cancellationToken)
                .ConfigureAwait(true);
        
        return response;
    }
}