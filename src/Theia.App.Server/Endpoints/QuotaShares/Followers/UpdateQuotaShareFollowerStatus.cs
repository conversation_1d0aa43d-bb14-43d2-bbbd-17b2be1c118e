using Ardalis.ApiEndpoints;
using FluentValidation.Results;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.QuotaShares;
using Theia.App.Shared.Submissions.ProgramStack.GetSubmissionProgramStack;
using Theia.Application.Services;
using Theia.Application.Services.Layers.LayerPlacement;
using Theia.Application.Services.QuotaShares;
using Theia.Domain.Entities.Indications;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.QuotaShares.Followers;

[ApiAuthorize(PermissionType = PermissionTypes.QuotaShareRespond)]
public class UpdateQuotaShareFollowerStatus(
    IApplicationDbContext dbContext,
    IUserService userService,
    ITenantResolverService tenantResolverService,
    IRoleService roleService,
    PlacementService placementService,
    UpdateQuotaShareFollowerStatusRequestValidator validator,
    QuotaSharePermissionService quotaSharePermissionService)
    : EndpointBaseAsync.WithRequest<UpdateQuotaShareFollowerStatusRequest>.WithoutResult
{
    [HttpPut("api/quota-shares/followers/status")]
    public override async Task HandleAsync(
        UpdateQuotaShareFollowerStatusRequest request,
        CancellationToken cancellationToken = new())
    {
        await validator.ValidateAndThrowAsync(request, cancellationToken);
        
        GetPlacementResponse placement = 
            await placementService.GetQuotaSharePlacementAsync(request.QuotaShareId, cancellationToken);

        decimal? maxLineSize = placement.Layers.FirstOrDefault()?.Indications.FirstOrDefault()?.Unassigned;

        if (maxLineSize is null)
        {
            throw new ApiProblemDetailsException(Resource.UpdateQuotaShareFollowerStatus_HandleAsync_Quota_share_is_incomplete__so_max_line_size_is_unknown, StatusCodes.Status409Conflict);
        }

        if (request.LineSize > maxLineSize)
        {
            throw new ApiProblemDetailsException(Resource.Line_size_exceeds_maximum_line_size, StatusCodes.Status409Conflict);
        }

        string userId = userService.EnsureAuthId();
        Guid tenantId = tenantResolverService.EnsureTenantId();
        bool isUnderwriterAdmin = roleService.IsUserInUnderwriterAdminRole;

        QuotaShareFollower? quotaShareFollower =
            await dbContext.QuotaShareFollowers
                .Where(x => 
                    x.QuotaShareId == request.QuotaShareId 
                    && QuotaSharePermissionService.IsQuotaShareFollowerAccessibleByCurrentUser(x, userId, tenantId, isUnderwriterAdmin))
                .SingleOrDefaultAsync(cancellationToken)
                .ConfigureAwait(false);

        if (quotaShareFollower is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        bool isResponseAllowed = await quotaSharePermissionService.CanCurrentUserRespondToQuotaShareAsync(request.QuotaShareId, cancellationToken).ConfigureAwait(false);

        if (!isResponseAllowed)
        {
            throw new ApiProblemDetailsException(Resource.Quota_share_is_in_a_state_that_prevents_from_accepting_or_declining_it, StatusCodes.Status403Forbidden);
        }

        bool hasStatusChangeSucceeded = request.Status switch
        {
            QuotaShareFollowerStatus.Declined => quotaShareFollower.TryDecline(request.Reason!),
            QuotaShareFollowerStatus.Accepted => quotaShareFollower.TryAccept(request.LineSize!.Value, request.Subjectivities)
        };

        if (!hasStatusChangeSucceeded)
        {
            throw new ApiProblemDetailsException(Resource.Quota_share_is_in_a_state_that_prevents_from_accepting_or_declining_it, StatusCodes.Status403Forbidden);
        }

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}
