using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.QuotaShares.Followers;
using Theia.Application.Services.Indications;
using Theia.Application.Services.QuotaShares;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Indications;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.QuotaShares.Followers;

[ApiAuthorize(PermissionType = PermissionTypes.QuotaShareWrite)]
public class AddQuotaShareFollowers(
    IApplicationDbContext dbContext,
    AddMultipleUnderwritersToQuotaShareRequestValidator validator,
    QuotaSharePermissionService quotaSharePermissionService)
    : EndpointBaseAsync.WithRequest<AddQuotaShareFollowersRequest>.WithResult<AddQuotaShareFollowersResponse>
{
    [HttpPost("api/quota-shares")]
    public override async Task<AddQuotaShareFollowersResponse> HandleAsync(
        AddQuotaShareFollowersRequest request,
        CancellationToken cancellationToken = new())
    {
        await validator.ValidateAndThrowAsync(request, cancellationToken).ConfigureAwait(false);
        await quotaSharePermissionService.EnsureWriteAccessAsync(request.LeadOptionId, cancellationToken).ConfigureAwait(false);

        Option? leadOption = 
            await dbContext.Options
                .Include(x => x.QuotaShare)
                .ThenInclude(q => q!.Followers)
                .ThenInclude(f => f.Underwriters)
                .SingleOrDefaultAsync(x => x.Id == request.LeadOptionId, cancellationToken)
                .ConfigureAwait(false);

        if (leadOption is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }

        Dictionary<Guid, ApplicationUser[]> insurers = await GetInsurersWithUnderwritersAsync(request).ConfigureAwait(false);

        QuotaShare? quotaShare = leadOption.QuotaShare;

        if (quotaShare is null)
        {
            quotaShare = new QuotaShare {LeadOptionId = leadOption.Id, LeadOption = leadOption};
            dbContext.QuotaShares.Add(quotaShare);
        }
        
        foreach (AddQuotaShareFollowersRequestFollower requestInsurer in request.Insurers)
        {
            QuotaShareFollower? quotaInsurer = 
                quotaShare.Followers!.SingleOrDefault(x => x.InsurerId == requestInsurer.InsurerId);

            if (!insurers.TryGetValue(requestInsurer.InsurerId, out ApplicationUser[] dbUnderwriters))
            {
                continue;
            }
            
            ICollection<QuotaShareFollowerUnderwriter> currentUnderwriters = 
                dbUnderwriters
                    .Where(x => requestInsurer.UnderwritersIds.Contains(x.Id))
                    .Select(x => new QuotaShareFollowerUnderwriter
                    {
                        QuotaShareFollowerId = Guid.Empty,
                        QuotaShareFollower = null,
                        UnderwriterId = x.Id,
                        Underwriter = x
                    })
                    .ToList();

            if (quotaInsurer is null)
            {
                QuotaShareFollower newFollower = new()
                {
                    QuotaShareId = quotaShare.Id,
                    QuotaShare = quotaShare,
                    InsurerId = requestInsurer.InsurerId,
                    Underwriters = currentUnderwriters
                };
                    
                quotaShare.Followers!.Add(newFollower);
            }
            
            if (quotaInsurer is not null)
            {
                quotaInsurer.Underwriters = currentUnderwriters;
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        return new AddQuotaShareFollowersResponse {QuotaShareId = quotaShare.Id};
    }
    
    private Task<Dictionary<Guid, ApplicationUser[]>> GetInsurersWithUnderwritersAsync(AddQuotaShareFollowersRequest request)
    {
        Guid[] allInsurersIds = request.Insurers.Select(x => x.InsurerId).ToArray();

        return dbContext.Insurers
            .Include(x => x.Tenant)
            .ThenInclude(x => x.UserTenantControls)
            .ThenInclude(x => x.ApplicationUser)
            .Where(x => allInsurersIds.Contains(x.Id))
            .ToDictionaryAsync(x => x.Id, x => x.Tenant.UserTenantControls.Select(y => y.ApplicationUser!).ToArray());
    }
}
