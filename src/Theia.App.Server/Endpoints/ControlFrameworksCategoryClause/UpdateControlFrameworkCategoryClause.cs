using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using System.Net;
using Theia.App.Shared.Admin.ControlFrameworksCategoryClauses;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.ControlFrameworksCategoryClause;

[ApiAuthorize(PermissionType = PermissionTypes.CreateControlFrameworkCategoryClause)]
public class UpdateControlFrameworkCategoryClause 
    : EndpointBaseAsync.WithRequest<UpdateControlFrameworkCategoryClauseCommand>.WithActionResult
{
    private readonly IApplicationDbContext _dbContext;

    public UpdateControlFrameworkCategoryClause(IApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    [HttpPut("api/ControlFrameworkCategoryClauses/UpdateControlFrameworkCategoryClause")]
    public override async Task<ActionResult> HandleAsync(
        UpdateControlFrameworkCategoryClauseCommand request,
        CancellationToken cancellationToken = new())
    {
        if (!ModelState.IsValid) throw new ApiProblemDetailsException(ModelState);

        if (_dbContext.ControlFrameworkCategoryClauses.Any(a =>
                a.Name.ToLower().Trim() == request.Name.ToLower().Trim() && a.Id != request.Id))
        {
            throw new ApiProblemDetailsException(Resource.Control_Framework_Category_Name_already_exist,
                statusCode: HttpStatusCode.BadRequest.ToInt());
        }

        int updated =
            await _dbContext.ControlFrameworkCategoryClauses
                .Where(c => c.Id == request.Id)
                .ExecuteUpdateAsync(props =>
                    props.SetProperty(p => p.Name, request.Name)
                        .SetProperty(p => p.Reference, request.Reference)
                        .SetProperty(p => p.Description, request.Description)
                        .SetProperty(p => p.Weighting, request.Weighting)
                        .SetProperty(p => p.IsActive, request.IsActive), cancellationToken)
                .ConfigureAwait(true);

        if (updated == 0)
        {
            throw new ApiProblemDetailsException(Resource.Unable_to_load_Control_Framework_Category_Clause,
                statusCode: HttpStatusCode.BadRequest.ToInt());
        }

        return Ok();
    }
}