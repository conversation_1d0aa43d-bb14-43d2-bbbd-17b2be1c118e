<!DOCTYPE html>
<html>
<head>
    <link crossorigin="anonymous" href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css"
          integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" rel="stylesheet">
</head>

<body>
<h1>{{Logo}}</h1>
<h2>Applicants List</h2>
<table class="table table-striped" id='applicants'>
    <thead>
    <th>SSN</th>
    <th>FirstName</th>
    <th>LastName</th>
    <th>DateOfBirth</th>
    <th>Height</th>
    <th>Weight</th>
    <th>CreatedOn</th>
    <th>ModifiedOn</th>
    </thead>
    {{#Applicants}}
    <tr>
        <td>{{Ssn}}</td>
        <td>{{FirstName}}</td>
        <td>{{LastName}}</td>
        <td>{{DateOfBirth}}</td>
        <td>{{Height}}</td>
        <td>{{Weight}}</td>
        <td>{{CreatedOn}}</td>
        <td>{{ModifiedOn}}</td>
    </tr>
    <tr>
        <td colspan="4">
            <table class="table mb-0">
                <thead>
                <th scope="col">Name</th>
                <th scope="col">JobTitle</th>
                <th scope="col">Phone</th>
                <th scope="col">CreatedOn</th>
                <th scope="col">ModifiedOn</th>
                </thead>
                {{#References}}
                <tr>
                    <td>{{Name}}</td>
                    <td>{{JobTitle}}</td>
                    <td>{{Phone}}</td>
                    <td>{{CreatedOn}}</td>
                    <td>{{ModifiedOn}}</td>
                </tr>
                {{/References}}

                <tbody>
                </tbody>
            </table>
        </td>
    </tr>
    {{/Applicants}}
</table>
<script crossorigin="anonymous"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<!--https://www.google.com/search?q=html+report+header+footer+logo&oq=html+report+header+footer+logo&aqs=chrome..69i57j33i160l2.8163j0j9&sourceid=chrome&ie=UTF-8-->
<!--https://medium.com/@Idan_Co/the-ultimate-print-html-template-with-header-footer-568f415f6d2a-->
<!--https://stackoverflow.com/questions/1360869/how-to-use-html-to-print-header-and-footer-on-every-printed-page-of-a-document-->