@using Microsoft.AspNetCore.Components.Authorization
@using Theia.App.Client.Common.Login.Pages

<Fluxor.Blazor.Web.StoreInitializer/>

<Router AppAssembly="@typeof(Program).Assembly" AdditionalAssemblies="@AdditionalAssemblies">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <Authorizing>
                <Logo Message="@Resource.Authorising"/>
            </Authorizing>
            <NotAuthorized>
                @if (context.User.Identity?.IsAuthenticated != true)
                {
                    <RedirectToLogin/>
                }
                else
                {
                    <p role="alert">@Resource.You_are_not_authorized_to_access_this_page</p>
                }
            </NotAuthorized>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <CascadingAuthenticationState>
            <LayoutView Layout="@typeof(MainLayout)">
                <_404/>
            </LayoutView>
        </CascadingAuthenticationState>
    </NotFound>
</Router>