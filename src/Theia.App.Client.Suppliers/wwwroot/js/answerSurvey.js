let survey;
let isConfirmingCompletion = false;
const pdfDocOptions = {
    fontSize: 12
}

window.addEventListener('load', function() {
    const surveyJsKey = "NDU5YzlkNDQtNzM3Ni00NjA2LWJhNmMtYjkxZjI4NzU2ODk4OzE9MjAyNS0xMS0wMSwyPTIwMjUtMTEtMDEsND0yMDI1LTExLTAx";
    Survey.setLicenseKey(surveyJsKey);
});

window.initializeInterop = function(dotNetObjectRef) {
    window.invokeSaveSurvey = async function(surveyAnswers) {
        await dotNetObjectRef.invokeMethodAsync('SaveApplicationFormAnswers', surveyAnswers);
    };
    
    window.invokeFinishPopup = async function() {
        return dotNetObjectRef.invokeMethodAsync('ConfirmFinishForm');
    }

    window.invokeFinishSurvey = async function(surveyAnswers) {
        await dotNetObjectRef.invokeMethodAsync('FinishAssessment', surveyAnswers);
    }
};

function loadSurveyForDisplay(surveyJson) {
    let surveyElement = document.getElementById("surveyAnswer");

    survey = new Survey.Model(surveyJson);
    survey.mode = "display";

    survey.render(surveyElement);
}

function loadSurveyJson(surveyJson, answers, isEditable) {
    let surveyElement = document.getElementById("surveyAnswer");

    survey = new Survey.Model(surveyJson);
    if(answers) {
        survey.data = JSON.parse(answers);
    }
    
    if (isEditable && isEditable === true) {
        survey.onCompleting.add(function (sender, options) {
            if(!isConfirmingCompletion) {
                options.allowComplete = false;
                isConfirmingCompletion = true;

                window.invokeFinishPopup().then(function (allowComplete) {
                    if(allowComplete) {
                        sender.doComplete();
                    }

                    isConfirmingCompletion = false;
                });
            }
        });
        survey.onComplete.add(finishSurvey);
    } else {
        survey.mode = "display";
    }

    survey.render(surveyElement);
}

async function saveSurvey() {
    await window.invokeSaveSurvey(survey.data);
}

async function finishSurvey() {
    await window.invokeFinishSurvey(survey.data);
}

function saveSurveyAsPdf() {
    const surveyPdf = new SurveyPDF.SurveyPDF(survey.toJSON(), pdfDocOptions);
    surveyPdf.data = survey.data;
    surveyPdf.save("Application Form");
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}