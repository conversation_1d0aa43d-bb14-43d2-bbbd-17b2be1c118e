using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Shared;
using Theia.App.Shared.Dtos.Products;
using Theia.App.Shared.Suppliers.Dashboard;
using Theia.App.Shared.Suppliers.DTOs;
using Theia.App.Shared.Suppliers.Files;
using Theia.App.Shared.Suppliers.Products;
using Theia.App.Shared.Suppliers.SupplierSubmissionRequests;
using Theia.App.Shared.Vendors;
using Theia.Http.Services;

namespace Theia.App.Client.Suppliers.Interfaces;

public interface ISupplierClient
{
    Task<ApiResponse<DataEnvelope<RetrieveAssessmentsForSupplierModel>>> RetrieveAssessmentsForSupplier(DataSourceRequest request, CancellationToken ct);
    Task<ApiResponse<RetrieveApplicationFormToAnswerModel>> RetrieveApplicationForm(WebSafeGuid applicationFormVersionId,
        CancellationToken ct);
    Task<NoPayloadApiResponse> SaveSupplierApplicationForm(SaveSupplierApplicationForm request, CancellationToken ct);
    Task<NoPayloadApiResponse> FinishSupplierApplicationForm(SaveSupplierApplicationForm request, CancellationToken ct);
    Task<ApiResponse<SupplierApplicationForms[]>> RetrieveSupplierApplicationForms(CancellationToken ct);
    Task<NoPayloadApiResponse> CreateSelfAssessment(SupplierApplicationForms[] request, CancellationToken ct);
    Task<ApiResponse<DataEnvelope<GetSupplierClientsResponseItem>>> GetMyClientsAsync(DataSourceRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> AddFileAsync(MultipartFormDataContent content, CancellationToken ct);
    Task<ApiResponse<DataEnvelope<GetFilesResponseItem>>> GetFilesAsync(DataSourceRequest request, CancellationToken ct);
    Task<Stream> DownloadFileAsync(Guid fileId, CancellationToken ct);
    Task<NoPayloadApiResponse> ChangeFileVisibilityAsync(ChangeFileVisibilityRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> RemoveFileAsync(Guid fileId, CancellationToken ct);
    Task<NoPayloadApiResponse> AssociateFileAsync(AssociateFileRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> FinishSubmissionRequestAsync(WebSafeGuid submissionRequestId, CancellationToken ct);
    Task<ApiResponse<GetSubmissionRequestStatusResponse>> GetSubmissionRequestStatusAsync(WebSafeGuid submissionRequestId, CancellationToken ct);
    Task<ApiResponse<SupplierApplicationFormDto[]>> GetVersionedApplicationFormsAsync(CancellationToken ct);
    Task<NoPayloadApiResponse> SaveProfileAsync(SaveProfileRequest request, CancellationToken ct);
    Task<ApiResponse<GetSupplierProfileResponse>> GetProfileAsync(CancellationToken ct);
    Task<ApiResponse<GetSupplierMainContactResponse>> GetSupplierMainContactAsync(string organisationId, CancellationToken ct);
    Task<NoPayloadApiResponse> EditSupplierMainContactAsync(EditSupplierMainContactRequest request, CancellationToken ct);
    Task<ApiResponse<GetSupplierDashboardResponse>> GetDashboardDataAsync(CancellationToken ct);
    public Task<ApiResponse<DataEnvelope<GetProductsResponseProduct>>> GetProductsForMyClientAsync(
        GetProductsRequest request, CancellationToken ct);
    Task<ApiResponse<DataEnvelope<GetProductsResponseProduct>>> GetProductsForSupplier(DataSourceRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> DeleteProductAsync(Guid productId, CancellationToken ct);
    Task<ApiResponse<Guid>> AddProductAsync(SupplierProductsDto newProductDto, CancellationToken ct);
    Task<NoPayloadApiResponse> UpdateProductAsync(SupplierProductsDto updateProductDto, CancellationToken ct);
}