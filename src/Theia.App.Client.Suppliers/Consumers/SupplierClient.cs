using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Client.Suppliers.Interfaces;
using Theia.App.Shared;
using Theia.App.Shared.Dtos.Products;
using Theia.App.Shared.Suppliers.Dashboard;
using Theia.App.Shared.Suppliers.DTOs;
using Theia.App.Shared.Suppliers.Files;
using Theia.App.Shared.Suppliers.Products;
using Theia.App.Shared.Suppliers.SupplierSubmissionRequests;
using Theia.App.Shared.Vendors;
using Theia.Http.Services;

namespace Theia.App.Client.Suppliers.Consumers;

public class SupplierClient(HttpService httpService) : ISupplierClient
{
    public Task<ApiResponse<DataEnvelope<RetrieveAssessmentsForSupplierModel>>> RetrieveAssessmentsForSupplier(DataSourceRequest request, CancellationToken ct)
        => httpService.PostAsync<DataSourceRequest, DataEnvelope<RetrieveAssessmentsForSupplierModel>>(
            "suppliers/retrieve-supplier-assessments", request, ct);

    public Task<ApiResponse<RetrieveApplicationFormToAnswerModel>> RetrieveApplicationForm(
        WebSafeGuid applicationFormVersionId, CancellationToken ct)
        => httpService.GetAsync<RetrieveApplicationFormToAnswerModel>(
            $"suppliers/retrieve-supplier-application-form/{applicationFormVersionId}", ct);

    public Task<NoPayloadApiResponse> SaveSupplierApplicationForm(SaveSupplierApplicationForm request,
        CancellationToken ct)
        => httpService.PostAsync("suppliers/save-supplier-application-form", request, ct);

    public Task<NoPayloadApiResponse> FinishSupplierApplicationForm(SaveSupplierApplicationForm request,
        CancellationToken ct)
        => httpService.PostAsync("suppliers/finish-supplier-application-form", request, ct);

    public Task<ApiResponse<SupplierApplicationForms[]>> RetrieveSupplierApplicationForms(CancellationToken ct)
        => httpService.GetAsync<SupplierApplicationForms[]>("suppliers/retrieve-supplier-application-forms", ct);

    public Task<NoPayloadApiResponse> CreateSelfAssessment(SupplierApplicationForms[] request, CancellationToken ct)
        => httpService.PostAsync("suppliers/create-self-assessment", request, ct);
    
    public Task<ApiResponse<DataEnvelope<GetSupplierClientsResponseItem>>> GetMyClientsAsync(DataSourceRequest request, CancellationToken ct)
        => httpService.PostAsync<DataSourceRequest, DataEnvelope<GetSupplierClientsResponseItem>>("suppliers/clients", request, ct);
    
    public Task<NoPayloadApiResponse> AddFileAsync(MultipartFormDataContent content, CancellationToken ct)
        => httpService.PostFormDataAsync("suppliers/files/upload", content, ct);
    
    public Task<ApiResponse<DataEnvelope<GetFilesResponseItem>>> GetFilesAsync(DataSourceRequest request, CancellationToken ct)
        => httpService.PostAsync<DataSourceRequest, DataEnvelope<GetFilesResponseItem>>("suppliers/files", request, ct);
    
    public Task<Stream> DownloadFileAsync(Guid fileId, CancellationToken ct)
        => httpService.DownloadFileAsync($"suppliers/files/{fileId}/download", ct);

    public Task<NoPayloadApiResponse> ChangeFileVisibilityAsync(ChangeFileVisibilityRequest request, CancellationToken ct)
        => httpService.PostAsync("suppliers/files/visibility", request, ct);
    
    public Task<NoPayloadApiResponse> RemoveFileAsync(Guid fileId, CancellationToken ct)
        => httpService.DeleteAsync($"suppliers/files/{fileId}", ct);

    public Task<NoPayloadApiResponse> AssociateFileAsync(AssociateFileRequest request, CancellationToken ct)
        => httpService.PostAsync("submission-requests/files/associate", request, ct);
    
    public Task<NoPayloadApiResponse> FinishSubmissionRequestAsync(WebSafeGuid submissionRequestId, CancellationToken ct) 
        => httpService.PostAsync($"submission-requests/{submissionRequestId}/finish", submissionRequestId, ct);
    
    public Task<ApiResponse<GetSubmissionRequestStatusResponse>> GetSubmissionRequestStatusAsync(WebSafeGuid submissionRequestId, CancellationToken ct)
        => httpService.GetAsync<GetSubmissionRequestStatusResponse>($"submission-requests/{submissionRequestId}/status", ct);

    public Task<ApiResponse<SupplierApplicationFormDto[]>> GetVersionedApplicationFormsAsync(CancellationToken ct)
        => httpService.GetAsync<SupplierApplicationFormDto[]>("suppliers/retrieve-supplier-submissions", ct);
    
    public Task<NoPayloadApiResponse> SaveProfileAsync(SaveProfileRequest request, CancellationToken ct)
        => httpService.PostAsync("suppliers/profile", request, ct);

    public Task<ApiResponse<GetSupplierProfileResponse>> GetProfileAsync(CancellationToken ct)
        => httpService.GetAsync<GetSupplierProfileResponse>("suppliers/profile", ct);
    
    public Task<ApiResponse<GetSupplierMainContactResponse>> GetSupplierMainContactAsync(string organisationId, CancellationToken ct)
        => httpService.GetAsync<GetSupplierMainContactResponse>($"suppliers/organisations/{organisationId}/contact", ct);

    public Task<NoPayloadApiResponse> EditSupplierMainContactAsync(EditSupplierMainContactRequest request, CancellationToken ct)
        => httpService.PostAsync("suppliers/organisations/contact", request, ct);
    
    public Task<ApiResponse<GetSupplierDashboardResponse>> GetDashboardDataAsync(CancellationToken ct)
        => httpService.GetAsync<GetSupplierDashboardResponse>("suppliers/dashboard", ct);

    public Task<ApiResponse<DataEnvelope<GetProductsResponseProduct>>> GetProductsForSupplier(DataSourceRequest request, CancellationToken ct)
        => httpService.PostAsync<DataSourceRequest, DataEnvelope<GetProductsResponseProduct>>("supplier/products/get", request, ct);

    public Task<NoPayloadApiResponse> DeleteProductAsync(Guid productId, CancellationToken ct)
        => httpService.DeleteAsync($"supplier/products/{productId}", ct);

    public Task<ApiResponse<Guid>> AddProductAsync(SupplierProductsDto newProductDto, CancellationToken ct)
        => httpService.PostAsync<SupplierProductsDto, Guid>("supplier/product/add", newProductDto, ct);

    public Task<NoPayloadApiResponse> UpdateProductAsync(SupplierProductsDto updateProductDto, CancellationToken ct)
        => httpService.PostAsync("supplier/product/update", updateProductDto, ct);

    public Task<ApiResponse<DataEnvelope<GetProductsResponseProduct>>> GetProductsForMyClientAsync(
        GetProductsRequest request, CancellationToken ct)
        => httpService.PostAsync<GetProductsRequest, DataEnvelope<GetProductsResponseProduct>>(
            "suppliers/client/products", request, ct);
}