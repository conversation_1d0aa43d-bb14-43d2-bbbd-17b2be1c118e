@using Theia.Domain.Common.Enums
<PageHeader Header="@Resource.Assessment_Requests"/>

<TelerikTooltip TargetSelector=".view-submission-request" />

<TelerikGrid
    @ref="_gridRef"
    TItem="RequestVm"
    @bind-PageSize="_pageSize"
    OnRead="FetchDataAsync"
    Sortable
    Reorderable
    Pageable
    EnableLoaderContainer="DefaultSettings.Grid.IsLoaderVisible">
    <GridToolBarTemplate>
        <AppGridSearchBox/> 
    </GridToolBarTemplate> 
    <GridSettings>
        <GridPagerSettings ButtonCount="DefaultSettings.Pager.ButtonCount" PageSizes="DefaultSettings.Pager.PageSizes"/>
    </GridSettings>
    <GridColumns>
        <GridColumn Field="@nameof(RequestVm.OrganisationName)" Title="@Resource.Organisation_name"/>
        <GridColumn Field="@nameof(RequestVm.CreatedOnUtc)" Title="@Resource.Date_Sent" DisplayFormat="@DefaultSettings.DateFormatConstants.TelerikDateFormat"/>
        <GridColumn Field="@nameof(RequestVm.Status)" Title="@Resource.Status">
            <Template>
                @if (context is RequestVm vm)
                {
                    if (vm.Status == SupplierSubmissionRequestStatus.Incomplete)
                    {
                        <TelerikChip ThemeColor="@ThemeConstants.Chip.ThemeColor.Warning">@Resource.Incomplete</TelerikChip>
                    }
                    else if (vm.Status == SupplierSubmissionRequestStatus.Completed)
                    {
                        <TelerikChip ThemeColor="@ThemeConstants.Chip.ThemeColor.Success">@Resource.Completed</TelerikChip>
                    }
                }
            </Template>
        </GridColumn>
        <GridColumn Field="@nameof(RequestVm.CompletedOnUtc)" Title="@Resource.Date_Completed_On" DisplayFormat="@DefaultSettings.DateFormatConstants.TelerikDateFormat"/>
        <GridCommandColumn ShowColumnMenu="false">
            @if (context is RequestVm vm)
            {
                <span title="@Resource.View_Assessment_Request" class="view-submission-request k-float-right">
                    <GridCommandButton
                        Class="k-float-right"
                        Size="@ThemeConstants.Button.Size.Small"
                        ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                        Icon="@FontIcon.FolderOpen"
                        OnClick="@(() => OpenSubmissionRequest(vm.Id))"/>
                </span>
            }
        </GridCommandColumn>
    </GridColumns>
</TelerikGrid>