<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="Access_token_timespan_cannot_be_null" xml:space="preserve">
    <value>Die Zeitspanne des Zugriffstokens darf nicht null sein.</value>
  </data>
    <data name="Access_token_timespan_is_required" xml:space="preserve">
    <value>Zugriffstoken-Zeitspanne ist erforderlich.</value>
  </data>
    <data name="Allowed_username_characters_are_required" xml:space="preserve">
    <value>Zulässige Benutzernamenzeichen sind erforderlich.</value>
  </data>
    <data name="An_unknown_failure_has_occurred" xml:space="preserve">
    <value>Ein unbekannter Fehler ist aufgetreten.</value>
  </data>
    <data name="Applicant_has_been_created_successfully" xml:space="preserve">
    <value>Bewerber wurde erfolgreich erstellt</value>
  </data>
    <data name="Applicant_has_been_deleted_successfully" xml:space="preserve">
    <value>Der Antragsteller wurde erfolgreich gelöscht.</value>
  </data>
    <data name="Applicant_has_been_updated_successfully" xml:space="preserve">
    <value>Der Antragsteller wurde erfolgreich aktualisiert.</value>
  </data>
    <data name="A_user_with_this_login_already_exists" xml:space="preserve">
    <value>Ein Benutzer mit diesem Login ist bereits vorhanden.</value>
  </data>
    <data name="Cannot_disable_2FA_for_user_with_Id" xml:space="preserve">
    <value>Die Zwei-Faktor-Authentifizierung für Benutzer mit der ID "{0}" kann nicht deaktiviert werden, da sie derzeit nicht aktiviert ist.</value>
  </data>
    <data name="Cannot_generate_recovery_codes" xml:space="preserve">
    <value>Es können keine Wiederherstellungscodes für den Benutzernamen "{0}" generiert werden, da für sie die Zwei-Faktor-Authentifizierung nicht aktiviert ist.</value>
  </data>
    <data name="Code_is_required" xml:space="preserve">
    <value>Code ist erforderlich.</value>
  </data>
    <data name="Confirmation_link_to_change_email_has_been_sent" xml:space="preserve">
    <value>Bestätigungslink zum Ändern der E-Mail wurde gesendet. Bitte überprüfen Sie Ihre E-Mail.</value>
  </data>
    <data name="Confirm_password_is_required" xml:space="preserve">
    <value>Bestätigen Sie, dass das Kennwort erforderlich ist.</value>
  </data>
    <data name="Confirm_your_email" xml:space="preserve">
    <value>Bestätigen Sie Ihre E-Mail-Adresse</value>
  </data>
    <data name="Date_of_Birth_is_required" xml:space="preserve">
    <value>Geburtsdatum ist erforderlich</value>
  </data>
    <data name="Default_lockout_time_Span_is_required" xml:space="preserve">
    <value>Standardmäßige Sperrzeitspanne ist erforderlich</value>
  </data>
    <data name="Deletion_of_entity_failed" xml:space="preserve">
    <value>Das Löschen der Entität "{0}" ({1}) ist fehlgeschlagen. {2}.</value>
  </data>
    <data name="Email_is_already_taken" xml:space="preserve">
    <value>E-Mail '{0}' ist bereits vergeben.</value>
  </data>
    <data name="Email_is_invalid" xml:space="preserve">
    <value>E-Mail '{0}' ist ungültig.</value>
  </data>
    <data name="Entity_with_key_was_not_found" xml:space="preserve">
    <value>Entität "{0}" mit Schlüssel "{1}" wurde nicht gefunden.</value>
  </data>
    <data name="Error_changing_user_name" xml:space="preserve">
    <value>Fehler beim Ändern des Benutzernamens.</value>
  </data>
    <data name="Error_changing_your_user_name" xml:space="preserve">
    <value>Fehler beim Ändern Des Benutzernamens.</value>
  </data>
    <data name="File_has_not_been_uploaded" xml:space="preserve">
    <value>Die Datei wurde nicht hochgeladen.</value>
  </data>
    <data name="File_is_empty" xml:space="preserve">
    <value>Datei ist leer.</value>
  </data>
    <data name="File_storage_settings_have_been_updated_successfully" xml:space="preserve">
    <value>Die Dateispeichereinstellungen wurden erfolgreich aktualisiert.</value>
  </data>
    <data name="First_name_is_required" xml:space="preserve">
    <value>Vorname ist erforderlich.</value>
  </data>
    <data name="Height_is_required" xml:space="preserve">
    <value>Die Höhe ist erforderlich.</value>
  </data>
    <data name="Identity_settings_have_been_updated_successfully" xml:space="preserve">
    <value>Die Identitätseinstellungen wurden erfolgreich aktualisiert.</value>
  </data>
    <data name="Incorrect_password" xml:space="preserve">
    <value>Falsches Passwort.</value>
  </data>
    <data name="Invalid_applicant_Id" xml:space="preserve">
    <value>Ungültige Bewerber-ID.</value>
  </data>
    <data name="Invalid_authenticator_code_entered" xml:space="preserve">
    <value>Ungültiger Authentifikatorcode eingegeben.</value>
  </data>
    <data name="Invalid_client_request" xml:space="preserve">
    <value>Ungültige Clientanforderung.</value>
  </data>
    <data name="Invalid_file_storage_Id" xml:space="preserve">
    <value>Ungültige Dateispeicher-ID.</value>
  </data>
    <data name="Invalid_lockout_settings_Id" xml:space="preserve">
    <value>Ungültige Sperreinstellungs-ID.</value>
  </data>
    <data name="Invalid_login_attempt" xml:space="preserve">
    <value>Ungültiger Anmeldeversuch.</value>
  </data>
    <data name="Invalid_password_settings_Id" xml:space="preserve">
    <value>Ungültige Kennworteinstellungen Id.</value>
  </data>
    <data name="Invalid_recovery_code_entered" xml:space="preserve">
    <value>Ungültiger Wiederherstellungscode eingegeben.</value>
  </data>
    <data name="Invalid_report_Id" xml:space="preserve">
    <value>Ungültige Berichts-ID</value>
  </data>
    <data name="Invalid_role_Id" xml:space="preserve">
    <value>Ungültige Rollen-ID.</value>
  </data>
    <data name="Invalid_sign_in_settings_Id" xml:space="preserve">
    <value>ID der ungültigen Anmeldeeinstellungen.</value>
  </data>
    <data name="Invalid_tenant_name" xml:space="preserve">
    <value>Ungültiger Mandantenname.</value>
  </data>
    <data name="Invalid_token" xml:space="preserve">
    <value>Ungültiges Token.</value>
  </data>
    <data name="Invalid_token_settings_Id" xml:space="preserve">
    <value>Ungültige Tokeneinstellungen Id.</value>
  </data>
    <data name="Invalid_user_Id" xml:space="preserve">
    <value>Ungültige Benutzer-ID.</value>
  </data>
    <data name="Invalid_user_settings_Id" xml:space="preserve">
    <value>ID für ungültige Benutzereinstellungen</value>
  </data>
    <data name="Job_title_is_required" xml:space="preserve">
    <value>Berufsbezeichnung ist erforderlich.</value>
  </data>
    <data name="Last_name_is_required" xml:space="preserve">
    <value>Nachname ist erforderlich</value>
  </data>
    <data name="Lockout_is_not_enabled_for_this_user" xml:space="preserve">
    <value>Die Sperre ist für diesen Benutzer nicht aktiviert.</value>
  </data>
    <data name="Maximum_allowed_number_of_attachments_must_not_exceed_3" xml:space="preserve">
    <value>Die maximal zulässige Anzahl von Anhängen darf 3 nicht überschreiten.</value>
  </data>
    <data name="Max_failed_access_attempt_is_required" xml:space="preserve">
    <value>Max fehlgeschlagener Zugriffsversuch ist erforderlich.</value>
  </data>
    <data name="Method_cannot_be_null" xml:space="preserve">
    <value>Methode darf nicht null sein.</value>
  </data>
    <data name="Minimum_allowed_number_of_attachments_must_be_at_least_1" xml:space="preserve">
    <value>Die zulässige Mindestanzahl von Anlagen muss mindestens 1 sein.</value>
  </data>
    <data name="New_password_is_required" xml:space="preserve">
    <value>Ein neues Passwort ist erforderlich.</value>
  </data>
    <data name="New_password_must_be_at_least_6_characters" xml:space="preserve">
    <value>Das neue Passwort muss mindestens 6 Zeichen lang sein.</value>
  </data>
    <data name="New_password_must_not_exceed_200_characters" xml:space="preserve">
    <value>Das neue Kennwort darf 200 Zeichen nicht überschreiten.</value>
  </data>
    <data name="N_A" xml:space="preserve">
    <value>Nicht verfügbar</value>
  </data>
    <data name="Old_password_is_required" xml:space="preserve">
    <value>Altes Passwort ist erforderlich.</value>
  </data>
    <data name="Only_those_between_the_ages_of_18_and_28_are_allowed_for_enlisting" xml:space="preserve">
    <value>Nur Personen zwischen 18 und 28 Jahren dürfen sich einschreiben.</value>
  </data>
    <data name="Only_those_whose_BMI_between_18_5_and_24_9_are_allowed_for_enlisting" xml:space="preserve">
    <value>Nur diejenigen, deren BMI zwischen 18,5 und 24,9 liegt, dürfen sich einschreiben.</value>
  </data>
    <data name="Only_those_whose_heights_between_100_and_250_with_normal_BMI_are_allowed_for_enlisting"
          xml:space="preserve">
    <value>Nur diejenigen, deren Körpergröße zwischen 100 und 250 mit normalem BMI liegt, dürfen sich anmelden.</value>
  </data>
    <data name="Only_those_who_weigh_between_50_and_200_with_normal_BMI_are_allowed_for_enlisting" xml:space="preserve">
    <value>Nur diejenigen, die zwischen 50 und 200 mit normalem BMI wiegen, dürfen sich einschreiben.</value>
  </data>
    <data name="Optimistic_concurrency_failure" xml:space="preserve">
    <value>Fehler bei optimistischer Parallelität, Objekt wurde geändert.</value>
  </data>
    <data name="Passwords_must_be_at_least_length_characters" xml:space="preserve">
    <value>Kennwörter müssen mindestens {0} Zeichen lang sein.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_digit" xml:space="preserve">
    <value>Passwörter müssen mindestens eine Ziffer ('0'-'9') haben.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_lowercase" xml:space="preserve">
    <value>Passwörter müssen mindestens einen Kleinbuchstaben ('a'-'z') haben.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_non_alphanumeric_character" xml:space="preserve">
    <value>Kennwörter müssen mindestens ein nicht alphanumerisches Zeichen aufweisen.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_uppercase" xml:space="preserve">
    <value>Passwörter müssen mindestens einen Großbuchstaben ('A'-'Z') haben.</value>
  </data>
    <data name="Password_cannot_contain_password" xml:space="preserve">
    <value>Das Kennwort darf kein "Kennwort" enthalten.</value>
  </data>
    <data name="Password_cannot_contain_username" xml:space="preserve">
    <value>Das Kennwort darf keinen Benutzernamen enthalten.</value>
  </data>
    <data name="Password_is_required" xml:space="preserve">
    <value>Passwort ist erforderlich.</value>
  </data>
    <data name="Password_must_be_at_least_6_characters" xml:space="preserve">
    <value>Das Passwort muss mindestens 6 Zeichen lang sein.</value>
  </data>
    <data name="Password_must_not_exceed_200_characters" xml:space="preserve">
    <value>Das Kennwort darf 200 Zeichen nicht überschreiten.</value>
  </data>
    <data name="Password_reset_link_was_sent" xml:space="preserve">
    <value>Link zum Zurücksetzen des Kennworts wurde gesendet. Bitte überprüfen Sie Ihre E-Mail.</value>
  </data>
    <data name="Phone_number_is_required" xml:space="preserve">
    <value>Telefonnummer ist erforderlich.!</value>
  </data>
    <data name="Please_confirm_your_account_by_clicking_here" xml:space="preserve">
    <value>Bitte bestätigen Sie Ihr Konto, indem &lt;a href='{0}'&gt;Sie hier klicken.&lt;/a&gt;</value>
  </data>
    <data name="Please_confirm_your_email" xml:space="preserve">
    <value>Bitte bestätigen Sie Ihre E-Mail.</value>
  </data>
    <data name="Please_reset_your_password_by_clicking_here" xml:space="preserve">
    <value>Bitte setzen Sie Ihr Passwort zurück, indem &lt;a href='{0}'&gt;Sie hier klicken.&lt;/a&gt;</value>
  </data>
    <data name="Please_specify_the_application_tenant_mode" xml:space="preserve">
    <value>Geben Sie den Anwendungsmandantenmodus an.</value>
  </data>
    <data name="Profile_picture_is_required" xml:space="preserve">
    <value>Profilbild ist erforderlich.</value>
  </data>
    <data name="Recovery_Code_is_required" xml:space="preserve">
    <value>Wiederherstellungscode ist erforderlich.</value>
  </data>
    <data name="Recovery_Code_Redemption_Failed" xml:space="preserve">
    <value>Wiederherstellungscode-Einlösung fehlgeschlagen</value>
  </data>
    <data name="Reference_name_is_required" xml:space="preserve">
    <value>Der Name wird benötigt!</value>
  </data>
    <data name="Refresh_token_timespan_cannot_be_null" xml:space="preserve">
    <value>Die Zeitspanne für Aktualisierungstoken darf nicht null sein.</value>
  </data>
    <data name="Refresh_token_timespan_is_required" xml:space="preserve">
    <value>Die Zeitspanne für Aktualisierungstoken ist erforderlich.</value>
  </data>
    <data name="Refresh_token_timespan_must_be_greater_than_access_token_expiry_time" xml:space="preserve">
    <value>Die Zeitspanne für Aktualisierungstoken muss größer sein als die Ablaufzeit des Zugriffstokens.</value>
  </data>
    <data name="Repeated_Ones_are_not_valid_Social_security_number" xml:space="preserve">
    <value>111111111 ist keine gültige Sozialversicherungsnummer.</value>
  </data>
    <data name="Repeated_Threes_are_not_valid_Social_security_number" xml:space="preserve">
    <value>333333333 ist keine gültige Sozialversicherungsnummer.</value>
  </data>
    <data name="Required_length_is_required" xml:space="preserve">
    <value>Die erforderliche Länge ist erforderlich.</value>
  </data>
    <data name="Required_unique_characters_is_required" xml:space="preserve">
    <value>Erforderliche eindeutige Zeichen sind erforderlich.</value>
  </data>
    <data name="Reset_your_password" xml:space="preserve">
    <value>Passwort zurücksetzen</value>
  </data>
    <data name="Role_has_been_created_successfully" xml:space="preserve">
    <value>Die Rolle wurde erfolgreich erstellt.</value>
  </data>
    <data name="Role_has_been_deleted_successfully" xml:space="preserve">
    <value>Die Rolle wurde erfolgreich gelöscht.</value>
  </data>
    <data name="Role_has_been_updated_successfully" xml:space="preserve">
    <value>Die Rolle wurde erfolgreich aktualisiert.</value>
  </data>
    <data name="Role_name_is_already_taken" xml:space="preserve">
    <value>Der Rollenname '{0}' ist bereits vergeben.</value>
  </data>
    <data name="Role_name_is_invalid" xml:space="preserve">
    <value>Der Rollenname '{0}' ist ungültig.</value>
  </data>
    <data name="Role_name_is_required" xml:space="preserve">
    <value>Rollenname ist erforderlich.</value>
  </data>
    <data name="Social_security_number_is_required" xml:space="preserve">
    <value>Sozialversicherungsnummer ist erforderlich</value>
  </data>
    <data name="Social_security_number_must_contain_only_9_digits" xml:space="preserve">
    <value>Die Sozialversicherungsnummer darf nur 9-stellige Stellen enthalten.</value>
  </data>
    <data name="Social_security_number_must_not_contain_consecutive_digits" xml:space="preserve">
    <value>Die Sozialversicherungsnummer darf keine fortlaufenden Ziffern enthalten.</value>
  </data>
    <data name="Storage_type_is_required" xml:space="preserve">
    <value>Speichertyp ist erforderlich.</value>
  </data>
    <data name="Surname_is_required" xml:space="preserve">
    <value>Nachname ist erforderlich.</value>
  </data>
    <data name="Tenant_has_been_created_successfully" xml:space="preserve">
    <value>Der Mandant wurde erfolgreich erstellt.</value>
  </data>
    <data name="Tenant_has_been_deleted_successfully" xml:space="preserve">
    <value>Mandant wurde erfolgreich gelöscht.</value>
  </data>
    <data name="Tenant_has_been_updated_successfully" xml:space="preserve">
    <value>Der Mandant wurde erfolgreich aktualisiert.</value>
  </data>
    <data name="Tenant_name_is_required" xml:space="preserve">
    <value>Der Mandantenname ist erforderlich.</value>
  </data>
    <data name="The_applicant_is_not_found" xml:space="preserve">
    <value>Der Antragsteller wird nicht gefunden.</value>
  </data>
    <data name="The_password_and_confirmation_password_do_not_match" xml:space="preserve">
    <value>Das Kennwort und das Bestätigungskennwort stimmen nicht überein.</value>
  </data>
    <data name="The_specified_role_is_already_registered" xml:space="preserve">
    <value>Die angegebene Rolle ist bereits registriert.</value>
  </data>
    <data name="The_specified_role_is_already_registered_in_the_given_tenant" xml:space="preserve">
    <value>Die angegebene Rolle ist bereits im angegebenen Mandanten registriert.</value>
  </data>
    <data name="The_specified_username_and_email_are_already_registered" xml:space="preserve">
    <value>Der angegebene Benutzername und die E-Mail-Adresse sind bereits registriert.</value>
  </data>
    <data name="The_specified_username_and_email_are_already_registered_in_the_given_tenant" xml:space="preserve">
    <value>Der angegebene Benutzername und die E-Mail-Adresse sind bereits im angegebenen Mandanten registriert.</value>
  </data>
    <data name="Token_settings_have_been_updated_successfully" xml:space="preserve">
    <value>Die Tokeneinstellungen wurden erfolgreich aktualisiert.</value>
  </data>
    <data name="Two_factor_authentication_code_is_required" xml:space="preserve">
    <value>Zwei-Faktor-Authentifizierungscode ist erforderlich.</value>
  </data>
    <data name="Two_factor_authentication_code_must_be_at_least_6_character_long" xml:space="preserve">
    <value>Der Zwei-Faktor-Authentifizierungscode muss mindestens 6 Zeichen lang sein.</value>
  </data>
    <data name="Two_factor_authentication_code_must_not_exceed_7_characters" xml:space="preserve">
    <value>Der Zwei-Faktor-Authentifizierungscode darf 7 Zeichen nicht überschreiten.</value>
  </data>
    <data name="Two_factor_authentication_has_been_disabled" xml:space="preserve">
    <value>2Fa wurde deaktiviert. Sie können die Zwei-Faktor-Authentifizierung wieder aktivieren, wenn Sie eine Authentifizierungs-App einrichten.</value>
  </data>
    <data name="Two_factor_authentication_required" xml:space="preserve">
    <value>Zwei-Faktor-Authentifizierung erforderlich.</value>
  </data>
    <data name="Unable_to_add_static_role" xml:space="preserve">
    <value>Statische Rolle kann nicht hinzugefügt werden</value>
  </data>
    <data name="Unable_to_create_new_tenant_in_single_tenant_mode" xml:space="preserve">
    <value>Neuer Mandant im Einzelmandantenmodus kann nicht erstellt werden.</value>
  </data>
    <data name="Unable_to_delete_static_role" xml:space="preserve">
    <value>Statische Rolle kann nicht gelöscht werden.</value>
  </data>
    <data name="Unable_to_delete_static_user" xml:space="preserve">
    <value>Statischer Benutzer kann nicht gelöscht werden.</value>
  </data>
    <data name="Unable_to_load_applicant" xml:space="preserve">
    <value>Bewerber kann nicht geladen werden.</value>
  </data>
    <data name="Unable_to_load_report" xml:space="preserve">
    <value>Bericht kann nicht geladen werden.</value>
  </data>
    <data name="Unable_to_load_role" xml:space="preserve">
    <value>Rolle kann nicht geladen werden.</value>
  </data>
    <data name="Unable_to_load_user" xml:space="preserve">
    <value>Benutzer kann nicht geladen werden.</value>
  </data>
    <data name="Unable_to_update_static_role" xml:space="preserve">
    <value>Statische Rolle kann nicht aktualisiert werden</value>
  </data>
    <data name="Unable_to_update_static_user" xml:space="preserve">
    <value>Statischer Benutzer kann nicht aktualisiert werden.</value>
  </data>
    <data name="Unexpected_error_occurred_deleting_user_with_Id" xml:space="preserve">
    <value>Beim Löschen des Benutzers mit der ID '{0}' ist ein unerwarteter Fehler aufgetreten.</value>
  </data>
    <data name="Unexpected_error_occurred_disabling_2FA" xml:space="preserve">
    <value>Beim Deaktivieren der Zwei-Faktor-Authentifizierung für Benutzer mit der ID "{0}" ist ein unerwarteter Fehler aufgetreten.</value>
  </data>
    <data name="Username_has_been_confirmed_successfully" xml:space="preserve">
    <value>Der Benutzername wurde erfolgreich bestätigt.</value>
  </data>
    <data name="Username_is_already_taken" xml:space="preserve">
    <value>Benutzername '{0}' ist bereits vergeben.</value>
  </data>
    <data name="Username_is_invalid" xml:space="preserve">
    <value>Benutzername '{0}' ist ungültig, darf nur Buchstaben oder Ziffern enthalten.</value>
  </data>
    <data name="Username_is_required" xml:space="preserve">
    <value>Benutzername ist erforderlich.</value>
  </data>
    <data name="Username_must_be_at_least_6_characters" xml:space="preserve">
    <value>Der Benutzername muss mindestens 6 Zeichen lang sein.</value>
  </data>
    <data name="Username_must_not_exceed_200_characters" xml:space="preserve">
    <value>Der Benutzername darf 200 Zeichen nicht überschreiten.</value>
  </data>
    <data name="User_already_has_a_password_set" xml:space="preserve">
    <value>Der Benutzer hat bereits ein Kennwort festgelegt.</value>
  </data>
    <data name="User_already_in_role" xml:space="preserve">
    <value>Benutzer bereits in der Rolle '{0}'.</value>
  </data>
    <data name="User_has_been_added_without_files" xml:space="preserve">
    <value>Benutzer wurde ohne Dateien hinzugefügt.</value>
  </data>
    <data name="User_has_been_created_successfully" xml:space="preserve">
    <value>Der Benutzer wurde erfolgreich erstellt.</value>
  </data>
    <data name="User_has_been_deleted_successfully" xml:space="preserve">
    <value>Benutzer wurde erfolgreich gelöscht.</value>
  </data>
    <data name="User_has_been_updated_successfully" xml:space="preserve">
    <value>Der Benutzer wurde erfolgreich aktualisiert.</value>
  </data>
    <data name="User_has_been_updated_successfully_without_updating_his_her_roles_as_the_user_is_static_type"
          xml:space="preserve">
    <value>Der Benutzer wurde erfolgreich aktualisiert, ohne seine Rollen zu aktualisieren, da der Benutzer statischer Typ ist.</value>
  </data>
    <data name="User_Id_is_required" xml:space="preserve">
    <value>Benutzer-ID ist erforderlich.</value>
  </data>
    <data name="User_permissions_have_been_updated_successfully" xml:space="preserve">
    <value>Benutzerberechtigungen wurden erfolgreich aktualisiert.</value>
  </data>
    <data name="User_with_Id_deleted" xml:space="preserve">
    <value>Benutzer mit der ID '{0}' wurde gelöscht.</value>
  </data>
    <data name="User_with_Id_UserId_logged_in_with_2Fa" xml:space="preserve">
    <value>Benutzer mit der ID '{0}' mit Zwei-Faktor-Authentifizierung angemeldet.</value>
  </data>
    <data name="value_cannot_be_null" xml:space="preserve">
    <value>{0} darf nicht null sein</value>
  </data>
    <data name="Verification_code_is_invalid" xml:space="preserve">
    <value>Der Bestätigungscode ist ungültig.</value>
  </data>
    <data name="Verification_email_has_been_sent" xml:space="preserve">
    <value>Bestätigungs-E-Mail wurde gesendet. Bitte überprüfen Sie Ihre E-Mail.</value>
  </data>
    <data name="Weight_is_required" xml:space="preserve">
    <value>Gewicht ist erforderlich.</value>
  </data>
    <data name="Your_account_is_deactivated_Please_contact_your_administrator" xml:space="preserve">
    <value>Ihr Konto ist deaktiviert. Wenden Sie sich an Ihren Administrator.</value>
  </data>
    <data name="Your_authenticator_app_has_been_verified" xml:space="preserve">
    <value>Ihre Authentifizierungs-App wurde überprüft.</value>
  </data>
    <data name="Your_authenticator_app_key_has_been_reset" xml:space="preserve">
    <value>Ihr Authentifizierungs-App-Schlüssel wurde zurückgesetzt, Sie müssen Ihre Authentifizierungs-App mit dem neuen Schlüssel konfigurieren.</value>
  </data>
    <data name="Your_email_has_been_changed_successfully" xml:space="preserve">
    <value>Ihre E-Mail-Adresse wurde erfolgreich geändert.</value>
  </data>
    <data name="Your_email_has_been_successfully_changed" xml:space="preserve">
    <value>Ihre E-Mail-Adresse wurde erfolgreich geändert.</value>
  </data>
    <data name="Your_email_is_unchanged" xml:space="preserve">
    <value>Ihre E-Mail-Adresse bleibt unverändert.</value>
  </data>
    <data name="Your_password_has_been_changed" xml:space="preserve">
    <value>Ihr Passwort wurde geändert.</value>
  </data>
    <data name="Your_password_has_been_reset" xml:space="preserve">
    <value>Ihr Passwort wurde zurückgesetzt.</value>
  </data>
    <data name="Your_password_has_been_set" xml:space="preserve">
    <value>Ihr Passwort wurde festgelegt.</value>
  </data>
    <data name="You_are_forbidden" xml:space="preserve">
    <value>Es ist Ihnen untersagt, auf {0} zuzugreifen.</value>
  </data>
    <data name="You_are_locked_out" xml:space="preserve">
    <value>Du bist ausgesperrt.</value>
  </data>
    <data name="You_are_not_authorized" xml:space="preserve">
    <value>Sie sind nicht berechtigt, auf {0} zuzugreifen.</value>
  </data>
    <data name="You_have_generated_new_recovery_codes" xml:space="preserve">
    <value>Sie haben neue Wiederherstellungscodes generiert.</value>
  </data>
    <data name="You_have_successfully_created_a_new_account" xml:space="preserve">
    <value>Sie haben erfolgreich ein neues Konto erstellt.</value>
  </data>
</root>