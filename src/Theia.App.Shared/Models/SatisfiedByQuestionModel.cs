using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Shared.Models;

public class SatisfiedByQuestionModel
{
    public required string QuestionName { get; init; }
    public object? Answer { get; init; }
    public required AnswerType AnswerType { get; init; }
    public required string QuestionText { get; init; }
    public string? QuestionComment { get; init; }
    public required int? QuestionWeighting { get; init; }
    public required bool IsCorrect { get; init; }
}