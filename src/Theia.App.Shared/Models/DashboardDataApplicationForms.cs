using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Shared.Models;

public class DashboardDataApplicationForms
{
    public required string SubmissionApplicationFormId { get; set; }
    public required string ApplicationFormName { get; set; }
    public required string ApplicationFormCode { get; set; }
    public DataState? AnswerStates { get; init; }
    public bool HasApplicationFormBeenAnswered => SubmissionApplicationFormId != string.Empty;
    public string? OrganisationSubmissionId { get; init; }
    public required ApplicationFormStatus Status { get; init; }
    public bool IsTenantUnderwriter { get; init; }
}