namespace Theia.App.Shared.ApplicationForms;

public record GetAppFormsResponseItem
{
    public Guid ApplicationFormId { get; init; }
    public required string? ApplicationFormName { get; init; }
    public required GetAppFormVersionsResponseItem[]? Versions { get; init; }
}

public record GetAppFormVersionsResponseItem
{
    public required Guid ApplicationFormVersionId { get; init; }
    public required string? ApplicationFormVersion { get; init; }
    public required bool InUse { get; init; }
}