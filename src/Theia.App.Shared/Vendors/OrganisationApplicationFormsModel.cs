namespace Theia.App.Shared.Vendors;

public class OrganisationApplicationFormsModel
{
    public Guid ApplicationFormId { get; init; }
    public string ApplicationFormName { get; init; }
    public OrganisationApplicationFormVersionModel[] Forms { get; init; }
    public Guid? SelectedFormVersionId { get; set; }
}

public class OrganisationApplicationFormVersionModel
{
    public Guid ApplicationFormVersionId { get; set; }
    public string ApplicationFormVersion { get; init; }
    public Guid ChildToApplicationFormId { get; init; }
    public bool IsInUse { get; init; }
}