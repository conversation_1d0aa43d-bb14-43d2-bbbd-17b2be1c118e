using FluentValidation;
using Theia.FrontendResources;

namespace Theia.App.Shared.Vendors;

public record AddVendorRequest
{
    public required string CompanyNumber { get; init; }
    public required string VendorName { get; init; }
    public required string ContactEmail { get; init; }
    public required Guid[] VendorTypeIds { get; init; } 
    public required Guid[] AppFormIds { get; init; }
    public required string[] Files { get; init; }
    public required Guid[] SupplierServicesIds { get; init; }
    public SupplierProductsDto[]? OrganisationSuggestedSupplierProducts { get; init; }
    public SupplierProductsDto[]? SelectedSupplierServices { get; init; }
}

public class SupplierProductsDto
{
    public required string Name { get; set; }
    public required string? Version { get; set; }
    public Guid LocalId { get; set; }
    public bool IsSelected { get; set; }
    public bool IsInUse { get; init; }
}

public class AddVendorRequestValidator : AbstractValidator<AddVendorRequest>
{
    public AddVendorRequestValidator()
    {
        RuleFor(x => x)
            .Must(x => x.AppFormIds.Length > 0 || x.Files.Length > 0)
            .WithMessage(Resource.Request_does_not_contain_any_form_s__or_file_s_);
        RuleForEach(x => x.OrganisationSuggestedSupplierProducts).SetValidator(new SupplierProductsValidator());
    }
}

public class SupplierProductsValidator : AbstractValidator<SupplierProductsDto>
{
    public SupplierProductsValidator()
    {
        RuleFor(x => x.Name).NotEmpty()
            .WithMessage(Resource.Please_provide_product_name);
    }
}