using Telerik.DataSource;

namespace Theia.App.Shared;

public static class DataEnvelope
{
    public static TEnvelope FromDataSourceResult<TEnvelope, TEnvelopeData>(DataSourceResult result) where TEnvelope : DataEnvelope<TEnvelopeData>
    {
        return (TEnvelope)new DataEnvelope<TEnvelopeData> {Data = result.Data.Cast<TEnvelopeData>(), Total = result.Total};
    }
}

public static class DataSourceResultExtensions
{
    public static TEnvelope ToDataEnvelope<TEnvelope, TEnvelopeData>(this DataSourceResult result) where TEnvelope : DataEnvelope<TEnvelopeData>
    {
        return (TEnvelope)new DataEnvelope<TEnvelopeData> {Data = result.Data.Cast<TEnvelopeData>(), Total = result.Total};
    }
}

public record DataEnvelope<T>
{
    public List<AggregateFunctionsGroup> GroupedData { get; init; } = [];
    public IEnumerable<T> Data { get; init; } = [];
    public required int Total { get; init; }

    public static implicit operator DataEnvelope<T>(DataSourceResult result)
    {
        return new DataEnvelope<T> {Data = result.Data.Cast<T>(), Total = result.Total};
    }

    public static DataEnvelope<T> FromDataSourceResult(DataSourceResult result, DataSourceRequest request)
    {
        DataEnvelope<T> envelope;
        
        if (request.Groups?.Any() is true)
        {
            envelope = new DataEnvelope<T>
            {
                GroupedData = result.Data.Cast<AggregateFunctionsGroup>().ToList(),
                Total = result.Total
            }; 
        }
        else
        {
            envelope = new DataEnvelope<T> {Data = result.Data.Cast<T>(), Total = result.Total};
        }

        return envelope;
    }
}