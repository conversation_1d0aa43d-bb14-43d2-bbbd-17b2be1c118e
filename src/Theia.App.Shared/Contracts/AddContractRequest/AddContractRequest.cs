using FluentValidation;
using Theia.App.Shared.Contracts.Common;

namespace Theia.App.Shared.Contracts.AddContractRequest;

public record AddContractRequest(
    string ClientName,
    Guid IndustryId,
    string ProductServiceName,
    decimal ContractValue,
    int Duration) : IContractRequestModel;

public class AddContractRequestValidator : AbstractValidator<AddContractRequest>
{
    public AddContractRequestValidator()
    {
        Include(new ContractRequestModelValidator());
    }
}
    
    