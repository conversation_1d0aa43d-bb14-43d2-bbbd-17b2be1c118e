using FluentValidation;

namespace Theia.App.Shared.Contracts.AddContractRequest;

public class AddContractInfoRequestValidator : AbstractValidator<AddContractsInfoRequest>
{
    public AddContractInfoRequestValidator()
    {
        RuleFor(vm => vm.ApproximateContractsNumber).GreaterThanOrEqualTo(0).NotNull();
        RuleFor(vm => vm.AverageContractSize).GreaterThanOrEqualTo(0).NotNull();
        RuleFor(vm => vm.AverageContractDuration).GreaterThanOrEqualTo(0).NotNull();
    }
}