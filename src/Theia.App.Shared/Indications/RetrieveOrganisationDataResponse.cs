namespace Theia.App.Shared.Indications;

public record RetrieveOrganisationDataResponse
{
    public required string OrganisationName { get; init; }
    public required string BrokerName { get; init; }
    public required string BrokerContactEmail { get; init; }
    public required string? UnderwriterName { get; init; }
    public required string InsurerName { get; init; }
    public required string Currency { get; init; }
    public required DateTimeOffset? IndicatedDate { get; init; }
}