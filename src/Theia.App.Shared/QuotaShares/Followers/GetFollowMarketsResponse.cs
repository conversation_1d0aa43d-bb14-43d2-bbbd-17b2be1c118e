using Theia.Domain.Common.Enums;

namespace Theia.App.Shared.QuotaShares.Followers;

public record GetFollowMarketsResponse
{
    public required GetFollowMarketsResponseItem[] Items { get; init; } = [];
    public required string Currency { get; init; }
}

public record GetFollowMarketsResponseItem
{
    public required Guid Id { get; init; }
    public required string InsurerName { get; init; }
    public required decimal? LineSize { get; init; }
    public required QuotaShareFollowerStatus Status { get; init; }
    public required bool CanBeAcceptedOrDeclined { get; init; }
    public required bool CanBeAddedByBroker { get; init; }
    public required bool CanBeUnfollowedByBroker { get; init; }
}
