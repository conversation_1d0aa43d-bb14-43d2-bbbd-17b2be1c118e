using FluentValidation;

namespace Theia.App.Shared.QuotaShares.Followers;

public record UnfollowQuotaShareFollowerRequest
{
    public required Guid QuotaShareFollowerId { get; init; }
}

public class UnfollowQuotaShareFollowerRequestValidator : AbstractValidator<UnfollowQuotaShareFollowerRequest>
{
    public UnfollowQuotaShareFollowerRequestValidator()
    {
        RuleFor(x => x.QuotaShareFollowerId).NotEmpty();
    }
}
