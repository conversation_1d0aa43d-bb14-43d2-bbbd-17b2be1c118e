using FluentValidation;
using Theia.Domain.Common.Enums;

namespace Theia.App.Shared.QuotaShares;

public record UpdateQuotaShareFollowerStatusRequest
{
    public required Guid QuotaShareId { get; init; }
    public required QuotaShareFollowerStatus Status { get; init; }
    public required decimal? LineSize { get; init; }
    public required string? Reason { get; init; }
    public required string[]? Subjectivities { get; init; }
}

public class UpdateQuotaShareFollowerStatusRequestValidator : AbstractValidator<UpdateQuotaShareFollowerStatusRequest>
{
    public UpdateQuotaShareFollowerStatusRequestValidator()
    {
        RuleFor(x => x.QuotaShareId).NotEmpty();
        RuleFor(x => x.Status).IsInEnum();
        RuleFor(x => x.LineSize)
            .GreaterThan(0)
            .When(x => x.Status is QuotaShareFollowerStatus.Accepted or QuotaShareFollowerStatus.AcceptedWithAmendments);
        RuleFor(x => x.Reason)
            .NotEmpty()
            .When(x => x.Status is QuotaShareFollowerStatus.Declined);
    }
}
