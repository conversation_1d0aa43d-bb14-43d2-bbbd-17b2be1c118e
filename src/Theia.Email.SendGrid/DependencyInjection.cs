using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SendGrid.Extensions.DependencyInjection;
using Theia.Email.Contract;
using Theia.Email.Contract.TemplatesData;

namespace Theia.Email.SendGrid;

public static class DependencyInjection
{
    public static void AddSendGridEmailService(this IServiceCollection services, IConfiguration configuration)
    {
        SendGridApiConfiguration sendGridApiConfiguration = new();
        configuration.GetRequiredSection(SendGridApiConfiguration.ConfigurationSectionName).Bind(sendGridApiConfiguration);

        services.AddSendGrid(opt =>
        {
            opt.ApiKey = sendGridApiConfiguration.ApiKey;
        });

        SendGridEmailConfiguration emailConfiguration = new();
        configuration.GetRequiredSection(SendGridEmailConfiguration.ConfigurationSectionName).Bind(emailConfiguration);

        services.AddScoped<IEmailService, SendGridEmailService>();

        services.AddSingleton(_ =>
        {
            DataTemplateMapping templateMapping = new();

            templateMapping.ConfigureSendToDeveloper(emailConfiguration.SendToDeveloperEmailAddress);
            templateMapping.ConfigureEmailFrom(emailConfiguration.EmailFrom);
            templateMapping.AddMapping(typeof(BrokerBrokerApprovedTemplateData), emailConfiguration.TheialensBrokerBrokerApproved);
            templateMapping.AddMapping(typeof(BrokerBrokerUnapprovedTemplateData), emailConfiguration.TheialensBrokerBrokerUnapproved);
            templateMapping.AddMapping(typeof(BrokingAdminBrokerApprovedTemplateData), emailConfiguration.TheialensBrokingAdminBrokerApproved);
            templateMapping.AddMapping(typeof(BrokingAdminBrokerUnapprovedTemplateData), emailConfiguration.TheialensBrokingAdminBrokerUnapproved);
            templateMapping.AddMapping(typeof(SendForgotPasswordEmailTemplateData), emailConfiguration.ForgotPasswordEmailTemplateId);
            templateMapping.AddMapping(typeof(SendNewAssociationInfoForBrokingRequestTemplateData), emailConfiguration.TheiaAdminBrokingHouseAdminNewAssociation);
            templateMapping.AddMapping(typeof(SendNewAssociationInfoForOrgRequestTemplateData), emailConfiguration.TheiaAdminOrgAdminNewAssociation);
            templateMapping.AddMapping(typeof(BrokerCreateSubmissionNotifyOrganisationAdmin), emailConfiguration.TheiaBrokerCreateSubmissionNotifyOrganisationAdmin);
            templateMapping.AddMapping(typeof(BrokerVoidOrganisationSubmission), emailConfiguration.TheiaBrokerVoidSubmissionNotifyOrganisationAdmin);
            templateMapping.AddMapping(typeof(BrokerRemoveApplicationForm), emailConfiguration.TheiaBrokerRemoveApplicationFormNotifyOrganisationAdmin);
            templateMapping.AddMapping(typeof(OrganisationSubmitsSubmissionForBroker), emailConfiguration.TheiaOrganisationSubmitsSubmissionNotification);
            templateMapping.AddMapping(typeof(EmailChangeTemplateData), emailConfiguration.TheialensEmailChange);
            templateMapping.AddMapping(typeof(TheiaLensSupplierInviteUser), emailConfiguration.TheiaLensSupplierInviteUser);
            templateMapping.AddMapping(typeof(TheiaLensSupplierInviteAdmin), emailConfiguration.TheiaLensSupplierInviteAdmin);
            templateMapping.AddMapping(typeof(TheiaLensInsurerInviteUnderwriter), emailConfiguration.TheiaLensInsurerInviteUnderwriter);
            templateMapping.AddMapping(typeof(TheiaLensInsurerInviteUnderwriterAdmin), emailConfiguration.TheiaLensInsurerInviteUnderwriterAdmin);
            templateMapping.AddMapping(typeof(InviteNewSupplierTemplate), emailConfiguration.TheiaLensInviteNewSupplierTemplate);
            templateMapping.AddMapping(typeof(ConfirmEmailTemplate), emailConfiguration.TheiaLensConfirmEmailTemplate);
            templateMapping.AddMapping(typeof(PasswordChangedTemplateData), emailConfiguration.PasswordChangedTemplate);

            return templateMapping;
        });
    }
}