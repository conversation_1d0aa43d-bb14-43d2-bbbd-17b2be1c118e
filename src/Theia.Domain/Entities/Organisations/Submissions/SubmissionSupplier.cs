using Theia.Domain.Entities.Organisations.Submissions.Suppliers;
using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.Submissions;

namespace Theia.Domain.Entities.Organisations.Submissions;

public class SubmissionSupplier
{
    public required Guid SubmissionId { get; init; }
    public required Submission? Submission { get; init; }
    public required Guid SupplierId { get; init; }
    public required Supplier? Supplier { get; init; }
    
    public required Guid? SupplierSubmissionRequestId { get; init; }
    public required SupplierSubmissionRequest? SupplierSubmissionRequest { get; init; }
    
    public List<SubmissionSupplierAppFormVersionSnapshot>? ApplicationFormsVersionSnapshots { get; init; } 
    public List<SubmissionSupplierSupplierFile>? Files { get; init; }
    public required ICollection<OrganisationAssociatedSupplierProduct>? AssociatedProducts { get; init; }
}