using Theia.Domain.Entities.Suppliers;

namespace Theia.Domain.Entities.Organisations.Submissions.Suppliers;

public class SubmissionSupplierProductSnapshot
{
    public Guid Id { get; init; }
    public required Guid SupplierId { get; init; }
    public Supplier? Supplier { get; init; }
    public required Guid SubmissionId { get; init; }
    public Submission? Submission { get; init; }
    public required string Name { get; init; }
    public string? Version { get; init; }
    public bool IsSupplierProduct { get; init; }
}