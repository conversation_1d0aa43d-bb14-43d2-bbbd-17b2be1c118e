using System.ComponentModel.DataAnnotations;
using Theia.Domain.Entities.ApplicationForms;
using Theia.Domain.Entities.Identity;
using Theia.Infrastructure.Common.Enums;

namespace Theia.Domain.Entities.Organisations.Submissions;

public class SubmissionApplicationForm
{
    [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid Id { get; init; }
    public ApplicationFormStatus Status { get; set; }
    public DateTime? CompletedOn { get; set; }
    public string? SurveyAnswers { get; set; }
    public bool Signed { get; set; }
    public int PercentageComplete { get; set; }
    public string? Signature { get; set; }
    public bool HasBeenModifiedByBroker { get; set; }
    
    public Guid SubmissionId { get; init; }
    [ForeignKey(nameof(SubmissionId))]
    public Submission? Submission { get; init; }

    public required Guid ApplicationFormVersionId { get; init; }
    [ForeignKey(nameof(ApplicationFormVersionId))]
    public required ApplicationFormVersion? ApplicationFormVersion { get; init; }

    public void Void()
    {
        Status = ApplicationFormStatus.Voided;
    }
}