using EntityFrameworkCore.Projectables;
using System.ComponentModel.DataAnnotations;
using Theia.BackendResources;
using Theia.Domain.Entities.BrokingHouses;
using Theia.Domain.Entities.Indications;
using Theia.Domain.Entities.Organisations.Submissions.Wholesale;
using Theia.Domain.Entities.Reporting;

namespace Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;

public class BrokerSubmission
{
    [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid Id { get; init; }
    public string? SubmissionName { get; init; }
    
    public required Guid RequestedByBrokingHouseId { get; init; }
    [ForeignKey(nameof(RequestedByBrokingHouseId))]
    public required BrokingHouse? RequestedByBrokingHouse { get; init; }
    
    public required decimal LimitOfLiability { get; set; }
    
    public string? Comments { get; init; }
    public bool ShowUnderlying { get; init; }

    public required Guid LayerId { get; init; }
    public required Layer? Layer { get; init; }

    public List<SubmissionQuestion> Questions { get; init; } = [];
    public required List<BrokerSubmissionUnderwriter> Underwriters { get; init; } = [];
    public List<BrokerSubmissionBroker> Brokers { get; init; } = [];
    public List<BrokerSubmissionSubmissionFile> Files { get; set; } = [];
    public ICollection<BrokerSubmissionView>? Views { get; init; }
    public ICollection<Indication>? Indications { get; set; }
    public ICollection<TheiaAnalysisJob>? TheiaAnalysisJobs { get; set; }
    public ICollection<AnalysedControlFramework>? AnalysedControlFrameworks { get; init; }
    public ICollection<WholesaleSubmission>? WholesaleSubmissions { get; init; }

    [Projectable]
    public int ApprovedUnansweredQuestionsCount()
        => Questions.Count(q => q.Status == SubmissionsQuestionStatus.Approved && q.Answers.Count == 0);

    [Projectable]
    public bool HasApprovedUnansweredQuestions() => ApprovedUnansweredQuestionsCount() > 0;
}