using Theia.Domain.Entities.ApplicationForms;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Suppliers.Submissions;
using Theia.Infrastructure.Common.Enums;

namespace Theia.Domain.Entities.Suppliers.ApplicationForms;

public class SupplierApplicationFormVersion
{
    public Guid Id { get; init; }
    public required Guid SupplierId { get; init; }
    public required Supplier? Supplier { get; init; }
    public required Guid ApplicationFormVersionId { get; init; }
    public required ApplicationFormVersion? ApplicationFormVersion { get; init; }
    
    public bool IsDeleted { get; set; }
    public ApplicationFormStatus Status { get; set; }
    public DateTimeOffset? CompletedOn { get; private set; }
    public DateTimeOffset? LastModifiedOn { get; set; }
    public DateTimeOffset RequestedOn { get; init; }
    public string? SurveyAnswers { get; set; }
    public int PercentageComplete { get; set; }
    public bool IsComplete { get; private set; }
    
    public List<SubmissionSupplier>? Submissions { get; init; }
    public List<SupplierSubmissionApplicationFormVersion>? SupplierSubmissionRequestFormVersions { get; init; }
    public List<SupplierApplicationFormVersionSnapshot>? Snapshots { get; init; }
    
    public string? CompletedByUserId { get; private set; }
    public ApplicationUser? CompletedByUser { get; init; }
    
    public void Complete(string userWhomCompletedId, int percentageComplete)
    {
        CompletedOn = DateTimeOffset.Now;
        LastModifiedOn = DateTimeOffset.Now;
        PercentageComplete = percentageComplete;
        Status = ApplicationFormStatus.Completed;
        IsComplete = true;
        CompletedByUserId = userWhomCompletedId;
    }
}