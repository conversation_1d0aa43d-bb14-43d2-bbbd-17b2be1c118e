using Theia.Domain.Entities.Identity;
using Theia.Infrastructure.Common.Enums;

namespace Theia.Domain.Entities.ApplicationForms;

public class ApplicationForms
{
    public Guid Id { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? ApplicationFormCode { get; set; }
    public ApplicationFormType ApplicationFormType { get; set; }

    public List<ApplicationFormVersion> Versions { get; init; } = [];
}