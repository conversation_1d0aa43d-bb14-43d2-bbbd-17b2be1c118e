using System.ComponentModel.DataAnnotations;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Reporting;
using Theia.Infrastructure.Common.Constants;

namespace Theia.Domain.Entities.ControlFrameworks;

public class ControlFramework
{
    public Guid Id { get; init; }
    
    [MaxLength(ControlFrameworkConstant.NameMaxLength)]
    public required string Name { get; init; }
    public string? Description { get; init; }
    public required bool IsActive { get; init; }
    public required string Code { get; init; }
    public required ControlFrameworkType Type { get; init; }

    public List<ControlFrameworkCategory> ControlFrameworkCategories { get; init; } = [];
    public List<AnalysedControlFramework> AnalysedControlFrameworks { get; init; } = [];
}