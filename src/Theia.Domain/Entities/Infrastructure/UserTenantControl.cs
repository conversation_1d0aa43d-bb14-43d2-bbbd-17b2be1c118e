using System.ComponentModel.DataAnnotations;
using Theia.Domain.Entities.Identity;

namespace Theia.Domain.Entities.Settings;

public class UserTenantControl
{
    [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public Guid Id { get; init; }
    
    public required Guid TenantId { get; init; }
    public required Tenant? Tenant { get; init; }
    
    public required string UserId { get; init; }
    public required ApplicationUser? ApplicationUser { get; init; }
}