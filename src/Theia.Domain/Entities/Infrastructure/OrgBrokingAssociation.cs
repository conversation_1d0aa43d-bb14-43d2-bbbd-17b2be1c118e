using System.ComponentModel.DataAnnotations;
using Theia.Domain.Entities.BrokingHouses;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Settings;

namespace Theia.Domain.Entities;

public class OrgBrokingAssociation
{
    [Key]
    public Guid Id { get; init; }
    
    public required Guid OrgId { get; init; }
    [ForeignKey(nameof(OrgId))]
    public required Organisation? Organisation { get; init; }
    
    public required Guid BrokingId { get; init; }
    [ForeignKey(nameof(BrokingId))]
    public required BrokingHouse? BrokingHouse { get; init; }

    public List<UserTenantControl> Brokers { get; init; } = [];
}