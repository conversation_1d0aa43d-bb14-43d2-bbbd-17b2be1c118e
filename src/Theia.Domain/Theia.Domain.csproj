<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup Label="Globals">
        <SccProjectName>SAK</SccProjectName>
        <SccProvider>SAK</SccProvider>
        <SccAuxPath>SAK</SccAuxPath>
        <SccLocalPath>SAK</SccLocalPath>
        <PackageId>Theia.Domain</PackageId>
        <LangVersion>12</LangVersion>
        <Nullable>enable</Nullable>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    </PropertyGroup>

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="EntityFrameworkCore.Projectables.Abstractions" Version="3.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.14" />
        <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.0" />
        <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Theia.App.Shared\Theia.App.Shared.csproj" />
        <ProjectReference Include="..\Theia.BackendResources\Theia.BackendResources.csproj" />
        <ProjectReference Include="..\Theia.Domain.Common\Theia.Domain.Common.csproj" />
        <ProjectReference Include="..\Theia.Infrastructure.Common\Theia.Infrastructure.Common.csproj" />
    </ItemGroup>
    
    <ItemGroup>
      <Compile Remove="Entities\Survey\Question.cs" />
      <Compile Remove="Entities\UserTokenControl.cs" />
      <Compile Remove="Entities\Organisations\Finances\AnnualFinancesType.cs" />
      <Compile Remove="Entities\Industry.cs" />
      <Compile Remove="Entities\Region.cs" />
      <Compile Remove="Entities\Contract.cs" />
      <Compile Remove="Entities\Organisations\DataCentreLocation.cs" />
      <Compile Remove="Entities\POC\Applicant.cs" />
      <Compile Remove="Entities\Report.cs" />
    </ItemGroup>
    
</Project>
