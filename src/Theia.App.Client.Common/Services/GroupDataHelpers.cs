using System.Collections;
using System.Text.Json;
using System.Text.Json.Serialization;
using Telerik.Blazor.Components;
using Telerik.DataSource;
using Theia.App.Shared;
using Theia.App.Shared.Converters;

namespace Theia.App.Client.Common.Services;

public class DataEnvelopeGridMapper<TResponseItem, TMappedItem>(DataEnvelope<TResponseItem> DataEnvelope, Func<TResponseItem, TMappedItem> Projection)
{
    public (IEnumerable Data, int Total, List<TMappedItem> Items) Map(DataSourceRequest request)
    {
        IEnumerable data;
        List<TMappedItem> mappedItems;
        
        if (request.Groups.Any())
        {
            (List<AggregateFunctionsGroup> deserializedData, List<TMappedItem> deserializedItems) = DeserializeGroups(DataEnvelope.GroupedData);

            mappedItems = deserializedItems;
            data = deserializedData.Cast<object>().ToList();
        }
        else
        {
            mappedItems = DataEnvelope.Data.Select(Projection).ToList();
            data = mappedItems.Cast<object>().ToList();
        }

        return (data, DataEnvelope.Total, mappedItems);
    }

    private (List<AggregateFunctionsGroup> AggregateGroups, List<TMappedItem> Items) DeserializeGroups(List<AggregateFunctionsGroup> groups)
    {
        List<TMappedItem> firstLevelItems = [];
        
        foreach (AggregateFunctionsGroup group in groups)
        {
            List<JsonElement> groupItems = group.Items.Cast<JsonElement>().ToList();

            if (group.HasSubgroups)
            {
                List<AggregateFunctionsGroup?> deserializedItems = groupItems
                    .Select(x => x.Deserialize<AggregateFunctionsGroup>(GroupDataHelpers.DefaultJsonSerializerOptions))
                    .ToList();

                List<AggregateFunctionsGroup> items = deserializedItems.Cast<AggregateFunctionsGroup>().ToList();
                (List<AggregateFunctionsGroup> subgroups, _) = DeserializeGroups(items);

                group.Items = subgroups;
            }
            else
            {
                List<TMappedItem> deserializedItems =
                    groupItems
                        .Select(x => x.Deserialize<TResponseItem>(GroupDataHelpers.DefaultJsonSerializerOptions)!)
                        .Select(Projection)
                        .ToList();

                firstLevelItems.AddRange(deserializedItems);
                group.Items = deserializedItems;
            }
        }

        return (groups, firstLevelItems);
    }
}

public class DataEnvelopeGridMapper<TResponseItem>(DataEnvelope<TResponseItem> DataEnvelope)
{
    public DataEnvelopeGridMapper<TResponseItem, TMappedItem> AddMapping<TMappedItem>(Func<TResponseItem, TMappedItem> projection)
    {
        return new DataEnvelopeGridMapper<TResponseItem, TMappedItem>(DataEnvelope, projection);
    }

    public (IEnumerable Data, int Total) Map(GridReadEventArgs args)
    {
        IEnumerable data;
        
        if (args.Request.Groups.Any())
        {
            List<AggregateFunctionsGroup> deserializeGroups = GroupDataHelpers.DeserializeGroups<TResponseItem>(DataEnvelope.GroupedData);

            data = deserializeGroups.Cast<object>().ToList();
        }
        else
        {
            data = DataEnvelope.Data.Cast<object>().ToList();
        }
        
        return (data, DataEnvelope.Total);
    }
}

public static class GroupDataHelpers
{
    public static JsonSerializerOptions DefaultJsonSerializerOptions => new() {PropertyNameCaseInsensitive = true, Converters = {new JsonStringEnumConverter(), new JsonWebSafeGuidConverter()}};
    
    public static List<AggregateFunctionsGroup> DeserializeGroups<TGroupItem>(List<AggregateFunctionsGroup> groups)
    {
        if (groups != null)
        {
            for (int i = 0; i < groups.Count; i++)
            {
                AggregateFunctionsGroup group = groups[i];
                List<JsonElement> groupItems = group.Items.Cast<JsonElement>().ToList();

                if (group.HasSubgroups)
                {
                    List<AggregateFunctionsGroup?> deserializedItems = groupItems
                        .Select(x => x.Deserialize<AggregateFunctionsGroup>(DefaultJsonSerializerOptions))
                        .ToList();

                    List<AggregateFunctionsGroup> items = deserializedItems.Cast<AggregateFunctionsGroup>().ToList();
                    List<AggregateFunctionsGroup> subgroups = DeserializeGroups<TGroupItem>(items);
                    group.Items = subgroups;
                }
                else
                {
                    List<TGroupItem?> deserializedItems = groupItems
                        .Select(x => x.Deserialize<TGroupItem>(DefaultJsonSerializerOptions))
                        .ToList();

                    group.Items = deserializedItems;
                }
            }
        }

        return groups;
    }
    
    public static List<AggregateFunctionsGroup> DeserializeGroups<TGroupItem, TProjection>(List<AggregateFunctionsGroup> groups, Func<TGroupItem, TProjection> projection)
    {
        if (groups != null)
        {
            for (int i = 0; i < groups.Count; i++)
            {
                AggregateFunctionsGroup group = groups[i];
                List<JsonElement> groupItems = group.Items.Cast<JsonElement>().ToList();

                if (group.HasSubgroups)
                {
                    List<AggregateFunctionsGroup?> deserializedItems = groupItems
                        .Select(x => x.Deserialize<AggregateFunctionsGroup>(DefaultJsonSerializerOptions))
                        .ToList();

                    List<AggregateFunctionsGroup> items = deserializedItems.Cast<AggregateFunctionsGroup>().ToList();
                    List<AggregateFunctionsGroup> subgroups = DeserializeGroups<TGroupItem>(items);
                    
                    group.Items = subgroups;
                }
                else
                {
                    List<TProjection> deserializedItems =
                        groupItems
                            .Select(x => x.Deserialize<TGroupItem>(DefaultJsonSerializerOptions))
                            .Select(projection)
                            .ToList();

                    group.Items = deserializedItems;
                }
            }
        }

        return groups;
    }
}