using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Theia.App.Client.Common.Services.Tab;

public class TabService(NavigationManager navigationManager, IJSRuntime jsRuntime) : ITabService
{
    public async Task SetActiveTabAsync(string tabName, bool addToHistory)
    {
        string normalizedTab = NormalizeTabName(tabName);
        string basePath = GetBasePath();
        string newUrl = $"{basePath}/{normalizedTab}";
        
        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public string GetActiveTabFromUrl()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);
        
        return segments.Length > 0 ? segments[^1] : string.Empty;
    }

    public string NormalizeTabName(string tabName) =>
        tabName.Replace(" ", "").Trim();
    
    private string GetBasePath()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);
        
        // Remove the last segment if it looks like a normalized tab name
        // This handles cases where we're already on a tab route
        if (segments.Length <= 1)
            return "/" + string.Join("/", segments);
        

        string lastSegment = segments[^1];
        // Check if the last segment matches any common tab naming patterns
        if (!IsLikelyTabSegment(lastSegment))
            return "/" + string.Join("/", segments);

        string[] baseSegments = segments[..^1];
        return "/" + string.Join("/", baseSegments);
    }
    
    private bool IsLikelyTabSegment(string segment)
    {
        // Common patterns for tab names (no spaces, CamelCase, etc.)
        return !segment.Contains('-') && 
               !segment.Contains('_') && 
               char.IsUpper(segment[0]) &&
               segment.Length > 2;
    }
}