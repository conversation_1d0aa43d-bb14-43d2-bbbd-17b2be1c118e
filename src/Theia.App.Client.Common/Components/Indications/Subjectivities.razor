<TelerikGrid
    @ref="gridRef"
    TItem="SubjectivityVM"
    Class="k-mb-2"
    @bind-PageSize="@pageSize"
    Sortable="true"
    ShowColumnMenu="true"
    Reorderable="true"
    EditMode="GridEditMode.Inline"
    OnRead="GetSubjectivitiesAsync"
    OnCreate="CreateSubjectivityAsync"
    OnDelete="DeleteSubjectivityAsync"
    ConfirmDelete="true">
    <GridSettings>
        <GridValidationSettings>
            <ValidatorTemplate>
                <FluentValidationValidator Validator="@validator"/>
            </ValidatorTemplate>
        </GridValidationSettings>
    </GridSettings>
    <GridToolBarTemplate>
        @if (!IsReadonly)
        {
            <GridCommandButton
                Command="Add"
                ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                Size="@ThemeConstants.Button.Size.Small"
                Icon="@FontIcon.Plus">
                @Resource.Add
            </GridCommandButton>
        }
    </GridToolBarTemplate>
    <GridColumns>
        <GridColumn Field="@nameof(SubjectivityVM.IsSatisfied)" Title="@Resource.Satisfied" Width="256px">
            <Template>
                @if (context is SubjectivityVM item)
                {
                    <TelerikCheckBox
                        Value="@item.IsSatisfied"
                        ValueChanged="@((bool value) => OnIsSatisfiedChanged(item, value))"
                        Enabled="@(!IsReadonly)"/>
                }
            </Template>
        </GridColumn>
        <GridColumn Field="@nameof(SubjectivityVM.Name)" Title="@Resource.Name"/>
        <GridCommandColumn Visible="@(!IsReadonly)" ShowColumnMenu="false" Width="128px">
            <TelerikStackLayout
                Orientation="StackLayoutOrientation.Horizontal"
                HorizontalAlign="StackLayoutHorizontalAlign.Right"
                Spacing="0.5rem">
                <GridCommandButton
                    Command="Delete"
                    Icon="@FontIcon.Trash"
                    Size="@ThemeConstants.Button.Size.Small"
                    ThemeColor="@ThemeConstants.Button.ThemeColor.Error"/>
                <GridCommandButton
                    Command="Save"
                    Icon="@FontIcon.Save"
                    Size="@ThemeConstants.Button.Size.Small"
                    ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                    ShowInEdit/>
                <GridCommandButton
                    Command="Cancel"
                    Icon="@FontIcon.Cancel"
                    Size="@ThemeConstants.Button.Size.Small"
                    ShowInEdit/>
            </TelerikStackLayout>
        </GridCommandColumn>
    </GridColumns>
</TelerikGrid>
