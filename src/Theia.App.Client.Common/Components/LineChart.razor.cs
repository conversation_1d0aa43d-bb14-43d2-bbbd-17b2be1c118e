using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components;
using Theia.App.Shared.Models;

namespace Theia.App.Client.Common.Components;

public partial class LineChart
{
    private TelerikChart? _chart;
    private int _chartValueAxisMaxValue;
    private bool _hasNoData;
    private bool _isLoading;

    [Parameter]
    public string? Title { get; set; }

    [Parameter]
    public IEnumerable<LineChartItem>? Data { get; set; }

    [Parameter]
    public Func<Task<LineChartModel?>>? GetFunction { get; set; }
    
    [Parameter]
    public Func<Task<string?>>? GetSubtitleValueFunction { get; set; }
    
    [Parameter]
    public string? Class { get; set; }

    private string? _subtitle;

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;
        StateHasChanged();

        try
        {
            if (GetFunction is not null)
            {
                LineChartModel? data = await GetFunction.Invoke();
                if (data?.Items?.Any() is true)
                {
                    Data = data.Items;
                    _chartValueAxisMaxValue = data.Items.Max(i => i.YValue) + 1;
                }
                else
                {
                    _hasNoData = true;
                }
            }

            if (GetSubtitleValueFunction is not null)
            {
                _subtitle = await GetSubtitleValueFunction.Invoke();
            }
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void OnSizeChanged()
    {
        _chart?.Refresh();
    }
}