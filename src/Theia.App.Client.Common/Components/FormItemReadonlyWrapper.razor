@using Theia.App.Shared.Extensions
@{
    if (IsR<PERSON>only)
    {
        <FormItem Class="form-field-margin" Field="@Field">
            <Template>
                @if (ValueType is not null
                     && !string.IsNullOrWhiteSpace(CurrencySymbol)
                     && decimal.TryParse(_value, out decimal amount))
                {
                    <ReadonlyInfo Label="@LabelText" Value="@amount.ShortenWithSuffixAndCurrencySymbols(CurrencySymbol)"/>
                }
                else
                {
                    <ReadonlyInfo Label="@LabelText" Value="@_value"/>
                }
            </Template>
        </FormItem>
    }
    else if (Template is not null)
    {
        
        <FormItem Field="@Field" LabelText="@LabelText" Class="@($"{Class} form-field-margin")">
            <Template>
                @Template
            </Template>
        </FormItem>
    }
    else 
    {
        <FormItem Field="@Field" LabelText="@LabelText" Class="@($"{Class} form-field-margin")"/>
    }
}

<style>
    .form-field-margin {
        margin-top: 4px !important;
    }
</style>