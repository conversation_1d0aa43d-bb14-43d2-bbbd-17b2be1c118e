using Microsoft.AspNetCore.Components;
using System.Text.Json;
using Theia.App.Shared.Models.Notifications;
using Theia.Http.Services;

namespace Theia.App.Client.Common.Components.Notifications.Events;

public partial class BrokerSentSubmissionToOrganisation : ComponentBase
{
    [Parameter]
    public required string AdditionalData { get; init; }
    
    [Parameter]
    public required Guid NotificationId { get; init; }
    
    [Parameter]
    public required Func<Guid, string, Task> HandleNotificationClick { get; init; }
    
    private BrokerSentOrganisationSubmissionModel Notification { get; set; } = null!;
    private string CallbackUrl => $"{CommonUrlConstants.ViewSubmission}/{(WebSafeGuid)Notification.SubmissionId}";

    protected override void OnParametersSet()
    {
        Notification = JsonSerializer.Deserialize<BrokerSentOrganisationSubmissionModel>(AdditionalData) ?? throw new InvalidOperationException();
        base.OnParametersSet();
    }
}