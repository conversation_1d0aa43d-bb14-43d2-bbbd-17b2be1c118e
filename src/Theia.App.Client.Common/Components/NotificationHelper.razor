@using Theia.App.Client.Common.Extensions
@using Theia.App.Shared
@using Theia.App.Client.Common.Models
@using OneOf

<TelerikNotification @ref="Notification" 
                     VerticalPosition="NotificationVerticalPosition.Bottom"
                     HorizontalPosition="NotificationHorizontalPosition.Right" 
                     AnimationType="AnimationType.SlideUp"
                     Class="notification k-mr-3">
    <Template>
        @(new MarkupString(context.Text))
    </Template>
</TelerikNotification>

<CascadingValue Value="this">
    @ChildContent
</CascadingValue>

@code {

    [Parameter]
    public RenderFragment ChildContent { get; set; }
    
    private TelerikNotification? Notification { get; set; }

    private static Action<SuccessResult> EmptyAction = _ => { };
    
    public void Handle(NoPayloadApiResponse result, string? messageToDisplay = null)
    {
        result.Switch(_ => Notification.ShowSuccess(), exception => ShowExceptionResult(exception, messageToDisplay));
    }

    public void HandleResultWithAction<T>(
        ApiResponse<T> result,
        Action<SuccessResult<T>>? successAction = null,
        Func<Task>? exceptionAction = null,
        bool showSuccess = false,
        string? messageToDisplay = null)
    {
        result.Switch(successResult =>
        {
            if (showSuccess)
            {
                Notification.ShowSuccess();
            }
            
            successAction?.Invoke(successResult);
        }, exception =>
        {
            ShowExceptionResult(exception, messageToDisplay);
            exceptionAction?.Invoke();
        });
    }
    
    public void HandleResultWithAction(OneOf<SuccessResult, ExceptionResult> result, Action<SuccessResult> action, string? messageToDisplay = null)
    {
        result.Switch(action, exception => ShowExceptionResult(exception, messageToDisplay));
    }

    public void HandleResultWithAction(NoPayloadApiResponse result, Action<SuccessResult> action, Action<ExceptionResult>? exceptionAction = null, string? messageToDisplay = null)
    {
        result.Switch(successResult =>
        {
            Notification?.ShowSuccess();
            action.Invoke(successResult);
        }, exception =>
        {
            ShowExceptionResult(exception, messageToDisplay);
            exceptionAction?.Invoke(exception);
        });
    }

    public void HandleResultWithoutAction<TResponse>(ApiResponse<TResponse> result, string? messageToDisplay = null, bool showSuccess = true)
    {
        Action<SuccessResult<TResponse>> successAction = showSuccess ? _ => Notification?.ShowSuccess() : EmptyAction;
        result.Switch(successAction, exception => ShowExceptionResult(exception, messageToDisplay));
    }

    public TFuncResult HandleResultWithFunc<TResponse, TFuncResult>(
        ApiResponse<TResponse> result,
        Func<TResponse, TFuncResult> successFunc,
        bool showSuccess = false,
        Func<TFuncResult>? exceptionFunc = null,
        bool showMessageFromResult = false,
        string? messageToDisplay = null) where TFuncResult : struct
    {
        return result.Match(successResult =>
        {
            string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
            
            if (showSuccess)
            {
                Notification?.ShowSuccess(message);
            }
            
            return successFunc.Invoke(successResult.Result);
        },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                return exceptionFunc?.Invoke() ?? default(TFuncResult);
            });
    }

    public Task HandleNoPayloadResponseWithFuncAsync(
        NoPayloadApiResponse result,
        Func<Task>? successFunc = null,
        Func<Task>? exceptionFunc = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        return result.Match(successResult =>
        {
            if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
            {
                string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                Notification?.ShowSuccess(message);
            }
            
            return successFunc?.Invoke() ?? Task.CompletedTask;
        },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                return exceptionFunc?.Invoke() ?? Task.CompletedTask;
            });
    }
    
    public void HandleNoPayloadResponseWithFunc(
        NoPayloadApiResponse result,
        Action? successFunc = null,
        Action? exceptionFunc = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        result.Switch(successResult =>
            {
                if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
                {
                    string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                    Notification?.ShowSuccess(message);
                }

                successFunc?.Invoke();
            },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                exceptionFunc?.Invoke();
            });
    }

    public Task HandleResultWithFuncAsync<TResponsePayload>(
        ApiResponse<TResponsePayload> result,
        Func<TResponsePayload, Task>? successFunc = null,
        Func<ExceptionResult, Task>? exceptionFunc = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        return result.Match(successResult =>
        {
            if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
            {
                string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                Notification?.ShowSuccess(message);
            }
            
            return successFunc?.Invoke(successResult.Result) ?? Task.CompletedTask;
        },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                return exceptionFunc?.Invoke(exceptionResult) ?? Task.CompletedTask;
            });
    }
    
    public Task HandleResultWithFuncAsync<TResponsePayload>(
        ApiResponse<TResponsePayload> result,
        CancellationTokenSource token,
        Func<TResponsePayload, CancellationTokenSource, Task>? successFunc = null,
        Func<ExceptionResult, Task>? exceptionFunc = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        return result.Match(successResult =>
            {
                if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
                {
                    string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                    Notification?.ShowSuccess(message);
                }
            
                return successFunc?.Invoke(successResult.Result, token) ?? Task.CompletedTask;
            },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                return exceptionFunc?.Invoke(exceptionResult) ?? Task.CompletedTask;
            });
    }

    public Task HandleResultWithFuncAsync<TResponsePayload>(
        ApiResponse<TResponsePayload> result,
        Func<Task>? successFunc = null,
        Func<ExceptionResult, Task>? exceptionFunc = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        return result.Match(successResult =>
        {
            if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
            {
                string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                Notification?.ShowSuccess(message);
            }
            
            return successFunc?.Invoke() ?? Task.CompletedTask;
        },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                return exceptionFunc?.Invoke(exceptionResult) ?? Task.CompletedTask;
            });
    }

    public Task HandleResultWithFuncAndActionAsync<TResponsePayload>(
        ApiResponse<TResponsePayload> result,
        Func<TResponsePayload, Task>? successFunc = null,
        Action<ExceptionResult>? exceptionAction = null,
        bool showSuccess = true,
        string? messageToDisplay = null,
        bool showMessageFromResult = false)
    {
        return result.Match(successResult =>
        {
            if (showSuccess || showMessageFromResult || !string.IsNullOrWhiteSpace(messageToDisplay))
            {
                string? message = showMessageFromResult ? successResult.Message : messageToDisplay;
                Notification?.ShowSuccess(message);
            }
            
            return successFunc?.Invoke(successResult.Result) ?? Task.CompletedTask;
        },
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult, messageToDisplay);
                exceptionAction?.Invoke(exceptionResult);
                return Task.CompletedTask;
            });
    }

    public TResponsePayload? Unwrap<TResponsePayload>(ApiResponse<TResponsePayload> result)
    {
        return result.Match<TResponsePayload?>(successResult => successResult.Result,
            exceptionResult =>
            {
                ShowExceptionResult(exceptionResult);
                return default;
            });
    }

    public void ShowSuccess(string? text = null)
    {
        string message = text ?? Resource.Succeeded;
        Notification?.Show(message, ThemeConstants.Notification.ThemeColor.Success);
    }

    public void Show(string text, string themeColor)
    {
        Notification?.Show(text, themeColor);
    }

    public void ShowError(string? text = null)
    {
        string message = text ?? Resource.Failed;
        
        Notification?.Show(new NotificationModel
        {
            Text = message,
            ThemeColor = ThemeConstants.Notification.ThemeColor.Error,
            CloseAfter = 5000
        });
    }

    public void ShowException()
    {
        Notification?.Show(new NotificationModel
        {
            Text = Resource.An_unexpected_exception_occured__Please_refresh_the_page_or_contact_support_if_the_problem_persists,
            ThemeColor = ThemeConstants.Notification.ThemeColor.Error,
            CloseAfter = 5000
        });
    }

    private void ShowExceptionResult(ExceptionResult exceptionResult)
    {
        string message = string.IsNullOrWhiteSpace(exceptionResult.Title) ? Resource.Unexpected_Error : exceptionResult.Title;
        message = AddValidationErrorsToMessage(message, exceptionResult);
        ShowError(message);
    }

    private void ShowExceptionResult(ExceptionResult exceptionResult, string? message)
    {
        message ??= string.IsNullOrWhiteSpace(exceptionResult.Title) ? Resource.Unexpected_Error : exceptionResult.Title;
        message = AddValidationErrorsToMessage(message, exceptionResult);
        ShowError(message);
    }

    private string AddValidationErrorsToMessage(string message, ExceptionResult exceptionResult)
    {
        if (exceptionResult.ValidationErrors?.Any() is true)
        {
            IEnumerable<string> errorReasons = exceptionResult.ValidationErrors.Select(x => x.Reason);
            string validationMessages = string.Join("<br/>", errorReasons);
            return $"{message}<br/>{validationMessages}";
        }

        return message;
    }
}

<style>
    .k-notification-group {
        max-height: fit-content !important;
    }
    
    .k-notification > .k-notification-content {
        font-size: 1rem !important;
    }

    .k-notification > .k-svg-icon {
        min-width: 1rem !important;
        margin-top:  2px !important;
    }
</style>