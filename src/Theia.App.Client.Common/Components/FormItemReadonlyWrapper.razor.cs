using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components.Form;

namespace Theia.App.Client.Common.Components;

public partial class FormItemReadonlyWrapper
{
    [CascadingParameter(Name = "FormContainer")]
    internal IFormContainer? FormContainer { get; set; }

    [Parameter]
    public string? Field { get; set; }

    [Parameter]
    public bool IsReadonly { get; set; }

    [Parameter]
    public string? LabelText { get; set; }

    [Parameter]
    public string? Class { get; set; }

    [Parameter]
    public RenderFragment? Template { get; set; }

    [Parameter]
    public string? ReadonlyValueOverride { get; set; }
    
    [Parameter]
    public Type? ValueType { get; set; }
    
    [Parameter]
    public string? CurrencySymbol { get; set; }

    private string? _value;

    protected override void OnInitialized()
    {
        if (!string.IsNullOrWhiteSpace(ReadonlyValueOverride))
        {
            _value = ReadonlyValueOverride;
        }
        else if (Field is not null && FormContainer is not null)
        {
            _value = FormContainer?.Model.GetType().GetProperty(Field)?.GetValue(FormContainer.Model)?.ToString();
        }
    }
}