using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Shared.Dtos;
using Theia.App.Shared.Models;
using Theia.FrontendResources;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Client.Common.Components.ControlFrameworks;

[PermissionAuthorize(PermissionTypes.GetDashboardData)]
public partial class ViewControlFrameworkCategories
{
    [Parameter] 
    public string UrlSafeIndicationId { get; set; } = null!;
    
    [Parameter] 
    public string UrlSafeSubmissionId { get; set; } = null!;

    [Parameter]
    public string UrlSafeControlFrameworkId { get; set; } = null!;

    [Inject] 
    private ICommonClient CommonClient { get; set; } = null!;
    
    [Inject]
    public IBreadcrumbService BreadcrumbService { get; init; } = null!;

    [CascadingParameter] 
    private NotificationHelper NotificationHelper { get; set; } = null!;

    private CancellationTokenSource cts = new();
    private ControlFrameworkMultiSelectModel[]? categoryData;
    private List<Guid> selectedDataKeys = new();
    private bool isLoading = true;
    private readonly SatisfactoryStateModel[] states = {
        new()
        {
            StateName = Resource.Satisfied_State_Name,
            StateType = ControlFrameworkSatisfactoryState.Satisfied
        },
        new()
        {
            StateName = Resource.Dissatisfied_State_Name,
            StateType = ControlFrameworkSatisfactoryState.Dissatisfied
        },
        new()
        {
            StateName = Resource.Unanswered_State_Name,
            StateType = ControlFrameworkSatisfactoryState.NotAnswered
        },
        new()
        {
            StateName = Resource.Informational_State_Name,
            StateType = ControlFrameworkSatisfactoryState.ZeroWeightingInformational
        }
    };

    private DataState? categoryDataStates;
    private DataState? filteredCategoryDataStates;
    private ControlFrameworkCategoriesModel[]? dropdownOptions;
    private bool isUpdating;
    private Guid currentlyViewingControlFrameworkCategoryId;
    private string? controlFrameworkName;
    private string? searchTerm;
    private bool isTopTenTextVisible = true;

    protected override async Task OnInitializedAsync()
    {
        SetupBreadcrumbs();
        await FetchDataAsync().ConfigureAwait(true);
        await base.OnInitializedAsync();
    }

    private async Task FetchDataAsync()
    {
        string idToSend = string.IsNullOrWhiteSpace(UrlSafeSubmissionId) ? UrlSafeIndicationId : UrlSafeSubmissionId; 
        RetrieveControlFrameworkCategoriesDto request = new(
            idToSend,
            UrlSafeControlFrameworkId);

        ApiResponse<ControlFrameworkCategoryDetails> response;
        if (string.IsNullOrWhiteSpace(UrlSafeSubmissionId))
        {
            response =
                await CommonClient.RetrieveControlFrameworkCategoryScoresForIndication(
                    request, cts.Token);
        }
        else
        {
            response =
                await CommonClient.RetrieveControlFrameworkCategoryScores(
                    request, cts.Token);
        }

        NotificationHelper.HandleResultWithAction(response, async success => await LoadData(success.Result));
        isLoading = false;
        StateHasChanged();
    }

    private async Task LoadData(ControlFrameworkCategoryDetails details)
    {
        controlFrameworkName = details.ControlFrameworkName;
        dropdownOptions = details.Categories.ToArray();
        await DropdownValueChanged(dropdownOptions[0].ControlFrameworkCategoryId);
        currentlyViewingControlFrameworkCategoryId = dropdownOptions[0].ControlFrameworkCategoryId;
        categoryData = details.Categories
            .Select(x => new ControlFrameworkMultiSelectModel
        {
            ControlFrameworkCategoryName = x.CategoryName,
            ControlFrameworkCategoryId = x.ControlFrameworkCategoryId,
            ControlFrameworkCategoryScore = x.CategoryScore
        }).ToArray();
        selectedDataKeys.AddRange(categoryData
            .OrderBy(x => x.ControlFrameworkCategoryScore)
            .Take(10)
            .Select(x => x.ControlFrameworkCategoryId));
        StateHasChanged();
    }

    private async Task DropdownValueChanged(Guid value)
    {
        isUpdating = true;
        StateHasChanged();

        Guid urlSafeSubmissionOrIndicationId = WebSafeGuid.Parse(UrlSafeSubmissionOrIndicationId, null);
        RetrieveControlFrameworkCategoryDashboardDetails request = 
            new(urlSafeSubmissionOrIndicationId, value);

        ApiResponse<DataState> response; 
        if (string.IsNullOrWhiteSpace(UrlSafeIndicationId))
        {
            response =
                await CommonClient.RetrieveControlFrameworkCategoryAnalysisDetail(request, cts.Token); 
        }
        else
        {
            response =
                await CommonClient.RetrieveControlFrameworkCategoryAnalysisDetailForIndication(request, cts.Token); 
        }

        NotificationHelper.HandleResultWithAction(response, success =>
        {
            UpdateTable(success.Result);
            currentlyViewingControlFrameworkCategoryId = value;
        });
    }

    private void SearchStates()
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
        {
            ResetFilteredDataStates();
            return;
        }

        DataSubState[] searchedSatisfactory = categoryDataStates?.Satisfactory?
            .Where(x => x.Name.ToLower().Contains(searchTerm.ToLower())).ToArray() ?? [];
        DataSubState[] searchedDissatisfied = categoryDataStates?.AreasOfImprovement?
            .Where(x => x.Name.ToLower().Contains(searchTerm.ToLower())).ToArray() ?? [];
        DataSubState[] searchedUnanswered = categoryDataStates?.Unanswered?
            .Where(x => x.Name.ToLower().Contains(searchTerm.ToLower())).ToArray() ?? [];

        filteredCategoryDataStates!.Satisfactory = searchedSatisfactory;
        filteredCategoryDataStates!.AreasOfImprovement = searchedDissatisfied;
        filteredCategoryDataStates!.Unanswered = searchedUnanswered;
        StateHasChanged();
    }

    private void UpdateTable(DataState data)
    {
        categoryDataStates = new()
        {
            Satisfactory = data.Satisfactory?.DistinctBy(x => x.Name).ToArray() ?? [],
            AreasOfImprovement = data.AreasOfImprovement?.DistinctBy(x => x.Name).ToArray() ?? [],
            Unanswered = data.Unanswered?.DistinctBy(x => x.Name).ToArray() ?? [],
            Informational = data.Informational?.DistinctBy(x => x.Name).ToArray() ?? []
        };

        ResetFilteredDataStates();
        isUpdating = false;
        StateHasChanged();
    }

    private void ResetFilteredDataStates()
    {
        filteredCategoryDataStates = new()
        {
            Satisfactory = categoryDataStates?.Satisfactory,
            Unanswered = categoryDataStates?.Unanswered,
            AreasOfImprovement = categoryDataStates?.AreasOfImprovement,
            Informational = categoryDataStates?.Informational
        };
    }

    private void HideTextOnElementUpdate()
    {
        if (isTopTenTextVisible)
        {
            isTopTenTextVisible = false;
            StateHasChanged();
        }
    }

    private void SetupBreadcrumbs()
    {
        BreadcrumbService.AddBreadcrumb(
            new(
                Resource.View_Control_Framework_Domain,
                $"{CommonUrlConstants.ViewControlFrameworkCategories}/{UrlSafeSubmissionOrIndicationId}/{UrlSafeControlFrameworkId}"));
    }
    
    private string UrlSafeSubmissionOrIndicationId => string.IsNullOrWhiteSpace(UrlSafeSubmissionId) ? UrlSafeIndicationId : UrlSafeSubmissionId;
}