@using Theia.App.Shared.Models
@using Theia.FrontendResources

<MultiSizeMediaQuery OnChange="@OnSizeChanged"/>

@if (_isLoading)
{
    <TelerikSkeleton Height="100%" Width="100%" ShapeType="SkeletonShapeType.Rectangle"/>
}
else
{
    <TelerikCard Class="@Class">
        <CardHeader>
            <CardTitle>@Title</CardTitle>
            @if (!string.IsNullOrWhiteSpace(_subtitle))
            {
                <CardSubTitle Class="subtitle">@_subtitle</CardSubTitle>
            }
        </CardHeader>
        <CardBody>
            @if (Data is null || !Data.Any())
            {
                <TelerikStackLayout
                    VerticalAlign="StackLayoutVerticalAlign.Center"
                    HorizontalAlign="StackLayoutHorizontalAlign.Center"
                    Height="70%" Width="100%">
                    <NoData/>
                </TelerikStackLayout>
            }
            else
            {
                <TelerikChart @ref="@_chart" RenderAs="RenderingMode.Canvas" Class="line-chart">
                    <ChartSeriesItems>
                        <ChartSeries
                            Type="ChartSeriesType.Line"
                            Data="@Data"
                            Field="@nameof(LineChartItem.YValue)"
                            CategoryField="@nameof(LineChartItem.XValue)">
                        </ChartSeries>
                    </ChartSeriesItems>
                    <ChartLegend Position="ChartLegendPosition.Bottom"/>
                    <ChartCategoryAxes>
                        <ChartCategoryAxis>
                            <ChartCategoryAxisLabels Position="@ChartAxisLabelsPosition.Start"/>
                        </ChartCategoryAxis>
                    </ChartCategoryAxes>
                    <ChartValueAxes>
                        <ChartValueAxis Min="0" Max="@_chartValueAxisMaxValue"/>
                    </ChartValueAxes>
                </TelerikChart>
            }
        </CardBody>
    </TelerikCard>
}

<style>
    .line-chart {
        border: none;
    }
    
    .full-height {
        height: 100%;
    }

    .subtitle {
        color: #0891b2;
        font-weight: 600;
        text-transform: uppercase;
        font-family: inherit;
    }
</style>