using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Extensions;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Models.ChangeEmail;
using Theia.App.Client.Common.Models.ConfirmEmailChange;

namespace Theia.App.Client.Common.Components.Account;

public partial class ConfirmEmailChange : IDisposable
{ 
    [SupplyParameterFromQuery(Name = "userId")]
    public string? UserId { get; set; }
    
    [SupplyParameterFromQuery(Name = "email")]
    public string? Email { get; set; }
    
    [SupplyParameterFromQuery(Name = "code")]
    public string? Code { get; set; }

    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private IManageClient ManageClient { get; set; }
    
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private ConfirmEmailChangeCommand ConfirmEmailChangeCommand { get; set; } = new();
    private readonly CancellationTokenSource _cts = new();

    protected override async Task OnInitializedAsync()
    {
        ConfirmEmailChangeCommand = new ConfirmEmailChangeCommand {Code = Code, UserId = UserId, Email = Email};

        Models.ApiResponse<ChangeEmailResponse> response =
            await ManageClient
                .ConfirmEmailChangeAsync(ConfirmEmailChangeCommand, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, _ =>
        {
            NavigationManager.NavigateTo("account/manage/emailChangeConfirmed");
        }, showSuccess: true);
    }

    public void Dispose()
    {
        _cts?.Cancel();
        _cts?.Dispose();
    }
}