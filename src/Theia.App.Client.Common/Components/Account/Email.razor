@if (!string.IsNullOrEmpty(_email))
{
    <TelerikCard>
        <CardHeader>
            <CardTitle>
                @Resource.Change_Email
            </CardTitle>
        </CardHeader>
        <CardBody Class="font-size-16">
            <TelerikForm Model="ChangeEmailCommand" OnValidSubmit="ChangeEmailAsync">
                <FormValidation>
                    <FluentValidationValidator/>
                </FormValidation>
                <FormItems>
                    <FormItem>
                        <Template>
                            <label for="email" class="k-label k-form-label">@Resource.Email</label>
                            <TelerikTextBox Id="email" @bind-Value="@_email" Enabled="false"></TelerikTextBox>
                        </Template>
                    </FormItem>
                    <FormItem Field="@nameof(ChangeEmailCommand.NewEmail)">
                        <Template>
                            <label for="newMail" class="k-label k-form-label">@Resource.New_Email</label>
                            <TelerikTextBox Id="newMail" @bind-Value="@ChangeEmailCommand.NewEmail" InputMode="email"></TelerikTextBox>
                            <TelerikValidationMessage For="@(() => ChangeEmailCommand.NewEmail)"></TelerikValidationMessage>
                        </Template>
                    </FormItem>
                </FormItems>
                <FormButtons>
                    <TelerikButton Class="my-button"
                                   Icon="@FontIcon.Save"
                                   Size="@(ThemeConstants.Button.Size.Large)"
                                   ButtonType="Telerik.Blazor.ButtonType.Submit"
                                   ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">
                        @Resource.Save
                    </TelerikButton>
                </FormButtons>
            </TelerikForm>
        </CardBody>
    </TelerikCard>
}