using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Interfaces;
using Theia.App.Client.Common.Models.UpdateUserProfile;
using Theia.App.Client.Common.Queries.GetUser;

namespace Theia.App.Client.Common.Components.Account;

public partial class Profile : IDisposable
{
    [Inject]
    private IManageClient ManageClient { get; set; }
    
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private CurrentUserForEdit UserForEdit { get; set; }

    private readonly CancellationTokenSource _cts = new();

    protected override async Task OnInitializedAsync()
    {
        Models.ApiResponse<CurrentUserForEdit> response =
            await ManageClient
                .GetUserAsync(_cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            UserForEdit = successResult.Result;
        });
    }

    private async Task UpdateUserProfileAsync()
    {
        UpdateUserProfileCommand updateCurrentUserProfileCommand = new()
        {
            Forename = UserForEdit.Forename, Surname = UserForEdit.Surname, JobTitle = UserForEdit.JobTitle, PhoneNumber = UserForEdit.PhoneNumber
        };

        Models.ApiResponse<string> response = 
            await ManageClient
                .UpdateUserProfileAsync(updateCurrentUserProfileCommand, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, _ => {}, showSuccess: true);
    }

    public void Dispose()
    {
        _cts?.Cancel();
        _cts?.Dispose();
    }
}