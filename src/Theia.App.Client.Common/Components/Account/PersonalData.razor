@using Theia.FrontendResources

<TelerikCard>
    <CardHeader>
        <CardTitle>
            @Resource.Personal_Data
        </CardTitle>
    </CardHeader>
    <CardBody>
        <p>@Resource.Your_account_contains_personal_data_that_you_have_given_us</p>
        <p>@Resource.This_page_allows_you_to_download_or_delete_that_data</p>
    </CardBody>
    <CardActions>
        <TelerikButton ButtonType="Telerik.Blazor.ButtonType.Button"
                       Icon="@FontIcon.Download"
                       OnClick="@DownloadPersonalDataAsync"
                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">
            @Resource.Download
        </TelerikButton>
        <TelerikButton ButtonType="Telerik.Blazor.ButtonType.Button"
                       Icon="@FontIcon.Trash"
                       OnClick="@(() => PersonalDataDeleteDialogVisible = true)"
                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Warning)">
            @Resource.Delete_Personal_Data
        </TelerikButton>
    </CardActions>
</TelerikCard>

<TelerikDialog @bind-Visible="@PersonalDataDeleteDialogVisible" CloseOnOverlayClick="true" Title=@Resource.Delete_Personal_Data>
    <DialogContent>
        <div class="z-alert z-alert-error k-mb-4">
            <TelerikFontIcon Icon="@FontIcon.ExclamationCircle"></TelerikFontIcon> @Resource.Deleting_this_data_will_permanently_remove_your_account_and_this_cannot_be_recovered
        </div>
        <TelerikForm id="delete-personal-data-form" Model="@DeletePersonalDataCommand" OnValidSubmit="DeleteUserPersonalDataAsync">
            <FormValidation>
                <FluentValidationValidator/>
            </FormValidation>
            <FormItems>
                <FormItem Field="@nameof(DeletePersonalDataCommand.Password)">
                    <Template>
                        <label for="password" class="k-label k-form-label">@Resource.Current_password</label>
                        <TelerikTextBox Id="password" @bind-Value="@DeletePersonalDataCommand.Password" Password="true"></TelerikTextBox>
                        <TelerikValidationMessage For="@(() => DeletePersonalDataCommand.Password)"></TelerikValidationMessage>
                    </Template>
                </FormItem>
            </FormItems>
            <FormButtons>
            </FormButtons>
        </TelerikForm>
    </DialogContent>
    <DialogButtons>
        <TelerikButton Icon="FontIcon.Cancel" OnClick="@(() => { PersonalDataDeleteDialogVisible = false; })">@Resource.Cancel</TelerikButton>
        <TelerikButton
            Form="delete-personal-data-form"
            Icon="@FontIcon.Trash"
            ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)"
            ButtonType="ButtonType.Submit">
            @Resource.Delete_my_Account
        </TelerikButton>
    </DialogButtons>
</TelerikDialog>