using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components;
using Theia.App.Client.Common.Consumers;
using Theia.App.Shared;
using Theia.App.Shared.Dtos;
using Theia.Infrastructure.Common.Defaults;

namespace Theia.App.Client.Common.Components.Organisations;

public partial class OrganisationApplicationForms : IDisposable
{
    private readonly CancellationTokenSource _cts = new();

    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
    
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; set; } = null!;

    [Inject] 
    private NavigationManager NavigationManager { get; set; } = null!;
    
    [Inject]
    private IApplicationFormsClient ApplicationFormsClient { get; set; } = null!;
    
    private bool _showSelected = false;
    private TelerikGrid<VersionedApplicationFormVm>? _gridRef;
    private int _pageSize = DefaultSettings.Pager.DefaultPageSize;

    private void ViewSurvey(VersionedApplicationVersionVm form)
    {
        NavigationManager.NavigateTo($"/{CommonUrlConstants.ViewApplicationForm}/{form.Id}");
    }

    private class VersionedApplicationFormVm
    {
        public string ApplicationFormCode { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public VersionedApplicationVersionVm[] Versions { get; set; }
    }

    private class VersionedApplicationVersionVm
    {
        public string Id { get; set; }
        public bool IsSelected { get; set; }
        public bool WasSelected { get; set; }
        public string Version { get; set; }
        public string Date { get; set; }
    }
    
    private async Task FetchDataAsync(GridReadEventArgs args)
    {
        Models.ApiResponse<DataEnvelope<GetAllApplicationFormsItem>> result =
            await ApplicationFormsClient
                .GetVersionedApplicationFormsAsync(args.Request, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(result, success =>
        {
            args.Data = success.Result.Data
                .Select(item => new VersionedApplicationFormVm
            {
                ApplicationFormCode = item.Code,
                Name = item.Name,
                Description = item.Description,
                Versions = item.Versions.Select(version => new VersionedApplicationVersionVm
                {
                    Id = version.Id,
                    Version = version.Version,
                    Date = version.Date.Date.ToShortDateString()
                }).ToArray()
            });

            args.Total = success.Result.Total;
        });
    }
}