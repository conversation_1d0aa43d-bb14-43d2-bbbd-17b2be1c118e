using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;
using Theia.App.Client.Common.Services.Tab;

namespace Theia.App.Client.Common.Components.Tab;

public abstract class TabComponentBase : ComponentBase, IDisposable
{
    [CascadingParameter]
    protected NotificationHelper NotificationHelper { get; init; } = null!; 
    
    [Inject]
    protected ITabService TabNavigationService { get; set; } = null!;
    [Inject] 
    protected NavigationManager NavigationManager { get; set; } = null!;

    protected int ActiveTabIndex { get; private set; }
    protected List<string> TabNames { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        NavigationManager.LocationChanged += OnLocationChangedInitializeTabsAsync;
        await InitializeTabsAsync();
    }

    private async Task InitializeTabsAsync()
    {
        string urlTab = TabNavigationService.GetActiveTabFromUrl();
        
        if (!string.IsNullOrEmpty(urlTab) && TabNames.Contains(urlTab))
        {
            List<string> normalizedTabNames = TabNames.Select(TabNavigationService.NormalizeTabName).ToList();
            int tabIndex = normalizedTabNames.IndexOf(urlTab);
            
            if (tabIndex >= 0)
                ActiveTabIndex = tabIndex;
        } else if (TabNames is { Count: > 0 })
        {
            // Set the initial tab
            await TabNavigationService.SetActiveTabAsync(TabNames[0], false);
        }

        await InvokeAsync(StateHasChanged);
    }

    protected async Task OnTabChangedAsync(int newIndex)
    {
        ActiveTabIndex = newIndex;

        if (newIndex < TabNames.Count)
        {
            await TabNavigationService.SetActiveTabAsync(TabNames[newIndex], true);
        }
    }

    private async void OnLocationChangedInitializeTabsAsync(object? sender, LocationChangedEventArgs e)
    {
        try
        {
            await InitializeTabsAsync();
        }
        catch (Exception _)
        {
            NotificationHelper.ShowError("An error with tabs has occurred");
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChangedInitializeTabsAsync;
    }
}