@using Theia.FrontendResources

<TelerikDialog Width="400px" Visible="ShowRecoveryCodesDialogVisible" VisibleChanged="ShowRecoveryCodesDialogVisibleChanged" Title=@Resource.Recovery_Codes>
    <DialogContent>
        <div class="k-mb-4 z-alert z-alert-info">
            <TelerikFontIcon Icon="@FontIcon.InfoCircle"/> @Resource.Store_these_codes_in_a_safe_place
        </div>
        
        <TelerikTextArea AutoSize="true" Enabled="false" @bind-Value="@Codes"/>
        
        <TelerikButton Icon="@FontIcon.Copy" OnClick="@CopyCodesAsync" Class="copy-button" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)">
            @CopyText
        </TelerikButton>
        
        <div class="k-mb-4 z-alert z-alert-warning">
            <TelerikFontIcon Icon="@FontIcon.ExclamationCircle"/> @Resource.If_you_lose_your_device_and_dont_have_the_recovery_codes_you_will_lose_access_to_your_account
        </div>
    </DialogContent>
    <DialogButtons>
        <TelerikButton ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
                       Icon="@FontIcon.Check"
                       OnClick="@OnGotItClickedAsync">@Resource.Got_it</TelerikButton>
    </DialogButtons>
</TelerikDialog>

<style>
    .copy-button {
        margin-top: 12px !important;
    }
</style>