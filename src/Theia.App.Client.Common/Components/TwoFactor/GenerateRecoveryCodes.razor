@using Theia.App.Client.Common
@using Theia.FrontendResources


<TelerikDialog Width="450px" Visible="GenerateRecoveryCodesDialogVisible" CloseOnOverlayClick="true" VisibleChanged="GenerateRecoveryCodesDialogVisibleChanged" Title=@Resource.Reset_2FA_Recovery_Codes>
    <DialogContent>
        <div class="k-mb-4 z-alert z-alert-warning">
            <TelerikFontIcon Icon="@FontIcon.ExclamationCircle"></TelerikFontIcon> @Resource.Store_these_codes_in_a_safe_place
        </div>
        <div class="k-mb-4 z-alert z-alert-error">
            <TelerikFontIcon Icon="@FontIcon.ExclamationCircle"></TelerikFontIcon> @Resource.If_you_lose_your_device_and_dont_have_the_recovery_codes_you_will_lose_access_to_your_account
        </div>
        <p>
            @Resource.Generating_new_recovery_codes_does_not_change_the_keys_used_in_authenticator_apps
        </p>
        <p>
            @Resource.If_you_wish_to_change_the_key_used_in_an_authenticator_app_you_should @Resource.Reset_Authenticator_App
        </p>
        @if (IsTwoFactorEnabled == false)
        {
            <div class="k-mb-4 z-alert z-alert-warning">
                <TelerikFontIcon Icon="@FontIcon.ExclamationCircle"></TelerikFontIcon>@StatusMessage
            </div>
        }
    </DialogContent>
    <DialogButtons>
        <TelerikButton
            Icon="@FontIcon.Cancel"
            OnClick="@(() => { GenerateRecoveryCodesDialogVisible = false; })"
            ButtonType="ButtonType.Button">
            @Resource.Cancel
        </TelerikButton>
        <TelerikButton 
            Icon="@FontIcon.Check"
            ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
            ButtonType="ButtonType.Submit"
            OnClick="Generate2FaRecoveryCodesAsync">
            @Resource.Generate
        </TelerikButton>
    </DialogButtons>
</TelerikDialog>

@if (GetRecCodType == "Update")
{
    <ShowRecoveryCodes Codes="@Codes" ShowRecoveryCodesDialogVisible="@ShoRecCodDialogVisible" ShowRecoveryCodesDialogVisibleChanged="OnRecoveryCodesVisibilityChanged"/>
}