using FluentValidation;
using Theia.FrontendResources;

namespace Theia.App.Client.Common.Components.OrganisationProfile.Vendor;

public class NewInternalSubmissionViewModelValidator : AbstractValidator<NewInternalSubmissionViewModel>
{
    public NewInternalSubmissionViewModelValidator()
    {
        RuleFor(x => x.SubmissionName)
            .NotEmpty()
            .NotNull();

        RuleFor(x => x.DueBy)
            .NotNull();

        
    }
}