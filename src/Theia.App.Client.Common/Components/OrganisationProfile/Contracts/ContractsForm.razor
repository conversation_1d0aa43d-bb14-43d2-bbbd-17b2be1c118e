<TelerikCard>
    <CardHeader>
        <CardTitle>@Resource.Contracts</CardTitle>
    </CardHeader>
    <CardBody>
        <TelerikForm Model="@_vm" OnValidSubmit="AddContractsInfoAsync" Columns="3" ColumnSpacing="1em">
            <FormItems>
                <FormItemReadonlyWrapper IsReadonly="@IsReadonly" Field="@(nameof(Vm.ApproximateContractsNumber))" LabelText="@Resource.Approximate_Number_of_Contracts" Class="required-field"/>
                <FormItemReadonlyWrapper IsReadonly="@IsReadonly" Field="@(nameof(Vm.AverageContractSize))" LabelText="@($"{Resource.Average_Contract_Size} ({_vm.CurrencyName})")" Class="required-field"/>
                <FormItemReadonlyWrapper IsReadonly="@IsReadonly" Field="@(nameof(Vm.AverageContractDuration))" LabelText="@Resource.Average_Contract_Duration_Years" Class="required-field"/>
            </FormItems>
            <FormValidation>
                <FluentValidationValidator Validator="@_validator"/>
            </FormValidation>
            <FormButtons>
                @if (!IsReadonly)
                {
                    <TelerikButton Icon="@FontIcon.Save"
                                   ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                                   ButtonType="@ButtonType.Submit">
                        @Resource.Save
                    </TelerikButton>
                }
            </FormButtons>
        </TelerikForm>
    </CardBody>
</TelerikCard>