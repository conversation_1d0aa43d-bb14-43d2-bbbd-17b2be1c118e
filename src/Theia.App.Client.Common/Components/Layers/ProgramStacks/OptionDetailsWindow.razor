<TelerikWindow
        @bind-Visible="@isVisible"
        Size="@ThemeConstants.Window.Size.Small"
        Draggable="false"
        Resizable="false"
        Modal="true"
        CloseOnOverlayClick="true">
        <WindowActions>
            <WindowAction Name="Close"/>
        </WindowActions>
        <WindowTitle>@Resource.Option</WindowTitle>
        <WindowContent>
            <TelerikStackLayout
                Orientation="StackLayoutOrientation.Vertical"
                Spacing="1em">
                @if (vm is not null)
                {
                    <ReadonlyInfo Label="@Resource.Indication_Date" Value="@vm.IndicationDate"/>
                    <ReadonlyInfo Label="@Resource.Insurer" Value="@vm.Insurer"/>
                    <ReadonlyInfo Label="@Resource.Target_Premium" Value="@vm.TargetPremium"/>
                    <ReadonlyInfo Label="@Resource.Policy_Aggregate_Limit_Of_Liability"
                                  Value="@vm.LimitOfLiability"/>
                    <ReadonlyInfo Label="@Resource.Premium" Value="@vm.Premium"/>
                    <ReadonlyInfo Label="@Resource.Line" Value="@vm.Line"/>
                    <TelerikStackLayout Orientation="StackLayoutOrientation.Horizontal" Spacing="0.5em"
                                        HorizontalAlign="StackLayoutHorizontalAlign.Right">
                        <TelerikButton
                            OnClick="@(() => isVisible = false)"
                            Class="app-width-fit-content"
                            Icon="@FontIcon.Cancel"
                            Size="@ThemeConstants.Button.Size.Small">
                            @Resource.Close
                        </TelerikButton>
                        <TelerikButton
                            OnClick="@(() => ViewOptionPage(vm))"
                            Class="app-width-fit-content"
                            Icon="@FontIcon.FolderOpen"
                            ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"
                            Size="@ThemeConstants.Button.Size.Small">
                            @Resource.View_Option
                        </TelerikButton>
                    </TelerikStackLayout>
                }
            </TelerikStackLayout>
        </WindowContent>
    </TelerikWindow>
