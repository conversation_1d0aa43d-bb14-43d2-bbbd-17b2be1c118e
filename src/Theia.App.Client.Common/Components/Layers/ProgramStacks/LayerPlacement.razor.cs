using Microsoft.AspNetCore.Components;
using System.Globalization;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services;
using Theia.App.Shared.Extensions;
using Theia.App.Shared.Layers;
using Theia.App.Shared.Submissions.ProgramStack.GetOptionDetails;
using Theia.App.Shared.Submissions.ProgramStack.GetSubmissionProgramStack;
using Theia.FrontendResources;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Defaults;

namespace Theia.App.Client.Common.Components.Layers.ProgramStacks;

public partial class LayerPlacement : ComponentBase, IDisposable
{
    [Parameter, EditorRequired]
    public required GetPlacementResponseLayer[] Layers { get; init; }
    
    [Parameter, EditorRequired]
    public required string? OrganisationId { get; set; }
    
    [Parameter]
    public string? Title { get; set; }
    
    [Parameter]
    public bool EnableOptionNavigation { get; set; }

    [Parameter]
    public bool ShowLayerBasicData { get; init; }
    
    [Parameter]
    public bool ShowPlacementSummary { get; init; }
    
    [Inject] 
    private ISubmissionsClient SubmissionsClient { get; init; } = null!;
    
    [Inject]
    private NavigationManager NavigationManager { get; init; } = null!;
    
    [CascadingParameter] 
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private readonly CancellationTokenSource cts = new();
    private VM? vm;
    private GetPlacementResponseLayer[]? localLayers;
    private bool layersChanged = true;
    private OptionDetailsWindow? optionDetailsWindow;

    private bool isProgramStackVisible;
    private bool isProgramStackVisibleCopy;
    private Guid? programStackSubmissionId;
    private string? retailLayerName;

    private class VM
    {
        public required LayerVM[] Layers { get; init; }
    }

    protected override void OnParametersSet()
    {
        if (!ReferenceEquals(localLayers, Layers))
        {
            localLayers = Layers;
            layersChanged = true;
            vm = new VM { Layers = Layers.Select(MapToLayerVm).ToArray() };
        }
        
        base.OnParametersSet();
    }

    protected override bool ShouldRender()
    {
        bool isProgramStackVisibleChanged = isProgramStackVisible != isProgramStackVisibleCopy;
        bool shouldRender = vm is null || layersChanged || isProgramStackVisibleChanged;
        layersChanged = false;
        isProgramStackVisibleCopy = isProgramStackVisible;
        
        return shouldRender;
    }
    
    private void ShowProgramStack(Guid submissionId, string layerName)
    {
        programStackSubmissionId = submissionId;
        retailLayerName = layerName;
        isProgramStackVisible = true;
        
        StateHasChanged();
    }
    
    private void HideProgramStack()
    {
        programStackSubmissionId = null;
        retailLayerName = null;
        isProgramStackVisible = false;
    }

    private static LayerVM MapToLayerVm(GetPlacementResponseLayer layer)
    {
        IndicationVM[] indications =
            layer.Indications
                .Where(x => !x.IsWholesaleBrokerIndication)
                .Select((indication, index) =>
                {
                    decimal currentCumulativeLimit =
                        layer.Indications[..index]
                            .Select(i => i.SelectedOption)
                            .Sum(i => i.Limit.GetValueOrDefault());
                    return MapToIndicationVM(indication, currentCumulativeLimit, layer.Limit);
                })
                .ToArray();
        
        WholesaleSubmissionVM[] wholesaleSubmissions = 
            layer.WholesaleSubmissions
                .Select(x => new WholesaleSubmissionVM
                {
                    Layers = x.Layers.Select(l => new WholesaleLayerVM
                    {
                        Limit = l.Limit,
                        Indications = 
                            l.Indications
                                .Select(i => MapToIndicationVM(i, 0, 100))
                                .ToArray()
                    }).ToArray()
                }).ToArray();

        decimal sum = indications.Sum(x => x.Total) + layer.Unassigned;

        if (sum > 0)
        {
            foreach (IndicationVM indication in indications)
            {
                indication.PercentageOfTotalIndicationsLine = indication.Total / sum;
            }
        }

        return new LayerVM
        {
            Name = LayerNamingService.Generate(layer.Index,
                layer.Currency,
                layer.Limit,
                layer.Excess),
            Limit = layer.Limit,
            LimitDisplay = layer.Limit.ShortenWithSuffixAndCurrencySymbols(layer.Currency),
            IsFinished = layer.IsFinished,
            Unassigned = layer.Unassigned,
            Indications = indications,
            PlacementPercentage = layer.PlacementPercentage,
            Currency = layer.Currency,
            TotalPlaced = layer.TotalPlaced,
            WholesaleSubmissions = wholesaleSubmissions
        };
    }

    private static IndicationVM MapToIndicationVM(GetPlacementResponseIndication indication, decimal currentCumulativeLimit, decimal? parentLimit)
    {
        decimal optionLine = indication.SelectedOption.Line ?? 0;
        decimal limit = indication.SelectedOption.Limit.GetValueOrDefault();
        decimal lineTotal = currentCumulativeLimit + limit;
        
        return new IndicationVM
        {
            Insurer = indication.Insurer,
            Line = optionLine,
            LineDisplay = optionLine.ShortenWithSuffixAndCurrencySymbols(),
            CumulativeLimit = lineTotal,
            CumulativeLimitDisplay = lineTotal.ShortenWithSuffixAndCurrencySymbols(),
            Limit = limit,
            LimitDisplay = limit.ShortenWithSuffixAndCurrencySymbols(),
            OptionId = indication.SelectedOption.Id,
            SubmissionId = indication.SubmissionId,
            PercentageOfTotalIndicationsLine = parentLimit is > 0 ? limit / parentLimit : null,
            TotalLine = indication.LineTotal,
            Unassigned = indication.Unassigned,
            FollowMarkets =
                indication.SelectedOption.FollowMarkets
                    .Select(x => new IndicationFollowMarket
                    {
                        Id = x.Id, 
                        Line = x.Line, 
                        LineDisplay = x.Line.GetValueOrDefault().ShortenWithSuffixAndCurrencySymbols(), 
                        InsurerName = x.InsurerName
                    })
                    .ToArray(),
            IsWholesaleBrokerIndication = indication.IsWholesaleBrokerIndication
        };
    }

    private async Task OnOptionSelected(IndicationVM indication, string layerName)
    {
        if (!EnableOptionNavigation || vm is null) return;
        
        if (indication.IsWholesaleBrokerIndication)
        {
            Guid? submissionId = indication.SubmissionId;

            if (submissionId.HasValue)
            {
                ShowProgramStack(submissionId.Value, layerName);
            }
            else
            {
                NotificationHelper.ShowError(Resource.Unable_to_find_wholesale_submission_for_this_option);
            }
            
            return;
        }
        
        ApiResponse<IndicationOptionDetailsResponse> response =
            await SubmissionsClient
                .GetIndicationOptionDetailsAsync(indication.OptionId, cts.Token)
                .ConfigureAwait(true);

        NotificationHelper.HandleResultWithAction(response, success =>
        {
            OptionDetailsWindow.Vm option = new()
            {
                OptionId = success.Result.OptionId,
                IndicationDate = success.Result.IndicationDate.ToString(DefaultSettings.DateFormatConstants.DateFormat),
                Insurer = success.Result.Insurer,
                TargetPremium = success.Result.TargetPremium.ConvertToRelevantCurrency(success.Result.Currency),
                LimitOfLiability = success.Result.LimitOfLiability.ConvertToRelevantCurrency(success.Result.Currency),
                Premium = success.Result.Premium.ConvertToRelevantCurrency(success.Result.Currency),
                Line = success.Result.Line.ConvertToRelevantCurrency(success.Result.Currency),
                IndicationRequestId = success.Result.IndicationRequestId,
                IndicationId = success.Result.IndicationId
            };
            
            optionDetailsWindow?.Show(option);
        });
    }
    
    private class LayerVM
    {
        public required string Name { get; init; }
        public required decimal Limit { get; init; }
        public required string LimitDisplay { get; init; }
        public required decimal Unassigned { get; init; }
        public required IndicationVM[] Indications { get; init; }
        public required WholesaleSubmissionVM[] WholesaleSubmissions { get; init; }
        public required decimal PlacementPercentage { get; set; }
        public required string Currency { get; set; }
        public required decimal TotalPlaced { get; set; }

        public bool IsFinished { get; init; }
        private bool IsPartPlacement => Indications.Any(x => x.IsWholesaleBrokerIndication);
        public string Class => IsFinished ? "layer-finished" : (IsPartPlacement ? "layer-part" : string.Empty);
        public bool HasUnassignedValue => Unassigned > 0;
        public string UnassignedDisplay => Unassigned.ShortenWithSuffixAndCurrencySymbols();
    }

    private class WholesaleSubmissionVM
    {
        public required WholesaleLayerVM[] Layers { get; init; }
        public decimal TotalLimit => Layers.Sum(x => x.Limit);
        public string TotalLimitDisplay => TotalLimit.ShortenWithSuffixAndCurrencySymbols();
    }

    private class WholesaleLayerVM
    {
        public required IndicationVM[] Indications { get; init; }
        public decimal TotalLimit => Indications.Sum(x => x.Limit);
        public required decimal Limit { get; init; }
        public string LimitDisplay => Limit.ShortenWithSuffixAndCurrencySymbols();
    }

    private class IndicationVM
    {
        public required Guid OptionId { get; init; }
        public required string Insurer { get; init; }
        public required decimal Line { get; init; }
        public required string LineDisplay { get; init; }
        public required decimal CumulativeLimit { get; init; }
        public required string CumulativeLimitDisplay { get; init; }
        public required decimal Limit { get; init; }
        public required string LimitDisplay { get; init; }
        public required bool IsWholesaleBrokerIndication { get; init; }
        public required decimal TotalLine { get; init; }
        public required decimal Unassigned { get; init; }
        public Guid? SubmissionId { get; init; }
        public decimal? PercentageOfTotalIndicationsLine { get; set; }
        public IndicationFollowMarket[] FollowMarkets { get; init; }
        
        public bool HasUnassignedAmount => Unassigned > 0;
        public decimal Total => TotalLine + (HasUnassignedAmount ? Unassigned : 0);
        public string UnassignedDisplay => Unassigned.ShortenWithSuffixAndCurrencySymbols();
        private decimal LinePercentageOfUnassigned => TotalLine / (TotalLine + Unassigned);
        public string LinePercentageOfUnassignedWidth => HasUnassignedAmount ? LinePercentageOfUnassigned.ToString("0.##%", CultureInfo.InvariantCulture) : "100%";
        public string PercentageOfTotalIndicationsLineDisplay => PercentageOfTotalIndicationsLine.GetValueOrDefault(1).ToString("0.##%", CultureInfo.InvariantCulture);
        public string Tooltip => IsWholesaleBrokerIndication ? Resource.View_wholesale_broker_section : Resource.View_option_details;
    }

    private class IndicationFollowMarket
    {
        public required Guid Id { get; init; }
        public required decimal? Line { get; init; }
        public required string LineDisplay { get; init; }
        public required string InsurerName { get; init; } 
    }

    public void Dispose()
    {
        cts.Cancel();
        cts.Dispose();
    }
}