using Microsoft.AspNetCore.Components;
using Telerik.Blazor;
using Telerik.FontIcons;
using Theia.App.Client.Common.Enums;

namespace Theia.App.Client.Common.Components;

public partial class LoaderButton
{
    [Parameter]
    public string? Text { get; set; }
    
    [Parameter]
    public string? Class { get; set; }
    
    [Parameter]
    public LoaderButtonType LoaderButtonType { get; set; }
    
    [Parameter]
    public EventCallback MethodToInvoke { get; set; }

    [Parameter]
    public string ThemeColour { get; set; } = ThemeConstants.Button.ThemeColor.Primary;

    [Parameter]
    public bool IsLoading { get; set; }
    
    [Parameter]
    public FontIcon? Icon { get; set; }

    [Parameter]
    public string Size { get; set; } = ThemeConstants.Button.Size.Large;

    private async Task InvokeEventDelegateAsync()
    {
        if (MethodToInvoke.HasDelegate)
        {
            IsLoading = true;
            StateHasChanged();

            await MethodToInvoke.InvokeAsync();
            IsLoading = false;
            StateHasChanged();
        }
    }
}