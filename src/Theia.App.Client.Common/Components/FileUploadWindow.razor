<TelerikButton
    Icon="@FontIcon.Upload"
    ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
    OnClick="Show">
    @Resource.Upload
</TelerikButton>


<TelerikWindow
    Visible="@_visible"
    Size="@ThemeConstants.Window.Size.Large"
    Draggable="false"
    Resizable="false"
    Modal="true"
    VisibleChanged="OnFileWindowVisibilityChangedAsync">
    <WindowActions>
        <WindowAction Name="Close"/>
    </WindowActions>
    <WindowTitle>@Resource.Upload</WindowTitle>
    <WindowContent>
        <TelerikDropZone Id="file-dropzone" NoteText="@NoteText"/>
        <TelerikUpload
            SaveField="File"
            SaveUrl="@_saveUrl"
            OnUpload="OnFileUpload"
            DropZoneId="file-dropzone"
            MaxFileSize="@MaxFileSize"
            Accept="@_accept"
            AllowedExtensions="@AllowedExtensions">
        </TelerikUpload>
    </WindowContent>
</TelerikWindow>