using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Telerik.Blazor;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Services;
using Theia.App.Shared.DocGen;
using Theia.App.Shared.Enums;
using Theia.App.Shared.Models;
using Theia.App.Shared.Submissions.Files;
using Theia.Domain.Common.Models.Submission;
using Theia.FrontendResources;

namespace Theia.App.Client.Common.Components.Submissions;

public partial class SubmissionFiles : IDisposable
{
    [CascadingParameter]
    public DialogFactory Dialogs { get; init; } = null!;
    
    [Inject]
    private ISubmissionFilesClient SubmissionFilesClient { get; init; } = null!;

    [Inject]
    private IJSRuntime Js { get; init; } = null!;
    
    [Inject]
    private IDocGenClient DocGenClient { get; init; } = null!;
    
    [Inject]
    private DocGenHttpClient DocGenHttpClient { get; init; } = null!;

    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; set; } = null!;

    [Parameter]
    public string SubmissionId { get; set; }
    
    [Parameter]
    public string? Class { get; set; }
    
    [Parameter]
    public string? Width { get; set; }

    [Parameter]
    public string? CardTitle { get; set; }

    [Parameter]
    public FileModificationStates IsAllowedToModifyFiles { get; set; }
    
    [Parameter]
    public bool CanSelectFiles { get; set; }
    
    [Parameter]
    public EventCallback<FileComponentFileModel> CallbackEvent { get; set; }
    
    [Parameter]
    public bool LoadFromBrokerSubmissionId { get; set; }
    
    [Parameter]
    public string? UrlSafeBrokerSubmissionId { get; set; }
    
    [Parameter]
    public bool IsGeneratingBrokerSubmission { get; set; }
    
    [Parameter]
    public string? UrlSafeOrganisationId { get; set; }
    
    private readonly List<string> _allowedExtensions = [".pdf"];
    private readonly CancellationTokenSource _cts = new();
    private FileUploadWindow? _fileUploadWindow;
    private Dictionary<string, object>? _fileUploadRequestData;
    private Vm _vm = new()
    {
        Files = [new()
        {
            Id = Guid.Empty,
            Name = Resource.Organisation_Profile,
            IsFileFromCurrentTenant = false,
            IsOrgProfileDownloadButton = true
        }]
    };

    protected override void OnParametersSet()
    {
        _fileUploadRequestData ??= new Dictionary<string, object>
        {
            {nameof(AddSubmissionFileRequest.SubmissionId), SubmissionId}
        };

        base.OnParametersSet();
    }

    protected override async Task OnInitializedAsync()
    {
        await GetFilesAsync().ConfigureAwait(true);
    }

    private async Task GetFilesAsync()
    {
        if (LoadFromBrokerSubmissionId)
        {
            Models.ApiResponse<GetSubmissionFilesResponse> response =
                await SubmissionFilesClient.GetBrokerSubmissionFilesAsync(UrlSafeBrokerSubmissionId!, _cts.Token);

            NotificationHelper.HandleResultWithAction(response, success => LoadData(success.Result));
        }
        else
        {
            Models.ApiResponse<GetSubmissionFilesResponse> response =
                await SubmissionFilesClient.GetSubmissionFilesAsync(SubmissionId, _cts.Token).ConfigureAwait(true);

            NotificationHelper.HandleResultWithAction(response, success => LoadData(success.Result));
        }
    }

    private void LoadData(GetSubmissionFilesResponse data)
    {
        FileComponentFileModel[] files =
            data.Files
                .Select(f => new FileComponentFileModel
                {
                    Id = f.Id,
                    Name = f.Name,
                    IsFileFromCurrentTenant = f.IsFileFromCurrentTenant
                })
                .ToArray();

        _vm.Files = [_vm.Files[0], ..files];
    }

    private async Task DownloadFileAsync(FileComponentFileModel file)
    {
        Stream downloadStream = await SubmissionFilesClient.DownloadFileAsync(file.Id, _cts.Token);
        using DotNetStreamReference streamReference = new(downloadStream);

        await Js.InvokeVoidAsync("downloadFileFromStream", file.Name, streamReference);
    }

    private async Task DeleteFileAsync(Guid fileId)
    {
        bool isConfirmed = await Dialogs.ConfirmAsync(
            Resource.Are_you_sure_you_want_to_delete_this_file,
            Resource.Confirm);

        if (!isConfirmed)
        {
            return;
        }

        Models.NoPayloadApiResponse response = await SubmissionFilesClient.DeleteFileAsync(fileId, _cts.Token);
        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetFilesAsync);
    }
    
    private async Task GetDownloadOrgSummaryDataAsync()
    {
        Models.ApiResponse<GetDownloadOrgSummaryDataRequestResponse> response =
            await DocGenClient.GetDownloadOrgSummaryData(UrlSafeOrganisationId!, _cts.Token).ConfigureAwait(true);

        await NotificationHelper.HandleResultWithFuncAsync(response, response =>
        {
            DataReference[] mappedResponse = Map(response);
            return DownloadOrgSummaryFileAsync(mappedResponse);
        }).ConfigureAwait(true);
    }
    
    private async Task DownloadOrgSummaryFileAsync(DataReference[] request)
    {
        Stream orgSummaryDocument = await DocGenHttpClient.GetOrgSummaryDocument(request, _cts.Token).ConfigureAwait(false);
        using DotNetStreamReference streamReference = new(orgSummaryDocument);
        await Js.InvokeVoidAsync("downloadFileFromStream", "Summary.pdf", streamReference).ConfigureAwait(false);
    }

    private async Task UpdateAndInvoke(FileComponentFileModel file, ChangeEventArgs changeArgs)
    {
        file.IsSelected = (bool)(changeArgs.Value ?? false);
        await CallbackEvent.InvokeAsync(file);
    }

    private class Vm
    {
        public FileComponentFileModel[] Files = [];
    }
    
    private DataReference[] Map(GetDownloadOrgSummaryDataRequestResponse model)
    {
        return
            model
                .GetType()
                .GetProperties()
                .Select(prop => new DataReference(prop.Name, prop.GetValue(model, null)))
                .ToArray();
    }

    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
}