using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Theia.App.Client.Common.Consumers;
using Theia.App.Shared.Submissions;
using Theia.Domain.Common.Enums;

namespace Theia.App.Client.Common.Components.Submissions;

public partial class SubmissionsListComponent : IDisposable
{
    [Parameter]
    public string BrokerSubmissionId { get; set; }
    
    [Inject]
    private ISubmissionQuestionsClient SubmissionQuestionsClient { get; init; } = null!;

    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; set; } = null!;

    [CascadingParameter]
    private AuthenticationState AuthenticationState { get; set; } = null!;

    private readonly CancellationTokenSource _cts = new();
    private bool _isAddingQuestion;
    private string _question = string.Empty;
    private bool _isQuestionEditing;
    private bool _isLoading = true;
    
    private Vm _vm = new();

    protected override async Task OnInitializedAsync()
    {
        await GetMessagesAsync().ConfigureAwait(true);
    }

    private async Task GetMessagesAsync()
    {
        Models.ApiResponse<GetSubmissionsQuestionsResponse> response =
            await SubmissionQuestionsClient
                .GetQuestionsAsync(new GetSubmissionsQuestionsRequest(BrokerSubmissionId), _cts.Token)
                .ConfigureAwait(false);

        NotificationHelper.HandleResultWithAction(response,
            result =>
            {
                _vm = new Vm
                {
                    Questions =
                        result.Result.Questions.Select(q =>
                            new VmQuestion
                            {
                                Content = q.Content,
                                Id = q.Id,
                                Status = MapQuestionStatus(q.Status),
                                AskedAt = q.AskedAt,
                                AskedBy = $"{q.AskedByName} {q.AskedBySurname}",
                                EditedAt = q.EditedAt,
                                Messages = q.Answers.Select(a => new VmMessage
                                {
                                    Content = a.Content,
                                    Id = a.Id,
                                    AnsweredAt = a.AnsweredAt,
                                    AnsweredBy = $"{a.AnsweredByName} {a.AnsweredBySurname}",
                                    EditedAt = q.EditedAt,
                                    IsEditable = a.IsEditable
                                }).ToArray()
                            }).ToArray()
                };
            });

        _isLoading = false;
        StateHasChanged();
    }
    
    private QuestionStatus MapQuestionStatus(SubmissionsQuestionStatus status)
    {
        return status switch
        {
            SubmissionsQuestionStatus.Approved => QuestionStatus.Approved,
            SubmissionsQuestionStatus.AwaitingApproval => QuestionStatus.AwaitingApproval,
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null)
        };
    }
    
    private async Task AddQuestionAsync()
    {
        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .AddQuestionAsync(new AddSubmissionQuestionRequest(BrokerSubmissionId, _question), _cts.Token)
                .ConfigureAwait(true);

        await NotificationHelper
            .HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync)
            .ConfigureAwait(true);

        _isAddingQuestion = false;
        _question = string.Empty;
    }

    private void ShowAddQuestionForm()
    {
        _isAddingQuestion = true;
    }

    private void HideAddQuestionForm()
    {
        _isAddingQuestion = false;
    }

    private async Task AddMessage(VmQuestion question)
    {
        AddSubmissionQuestionAnswerRequest request = new(question.Id, question.NewQuestion);
        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .AddAnswerAsync(request, _cts.Token)
                .ConfigureAwait(true);

        await NotificationHelper
            .HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync)
            .ConfigureAwait(true);

        question.NewQuestion = string.Empty;
    }

    private async Task DeleteAnswerAsync(Guid answerId)
    {
        DeleteSubmissionQuestionAnswerRequest request = new(answerId);

        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .DeleteAnswerAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync).ConfigureAwait(true);
    }

    private async Task EditMessageAsync(VmMessage message)
    {
        EditSubmissionQuestionAnswerRequest request = new(message.Id, message.NewContent);

        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .EditAnswerAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync);
    }

    private async Task EditQuestionAsync(VmQuestion question)
    {
        EditSubmissionQuestionRequest request = new(question.Id, question.NewContent);

        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .EditQuestionAsync(request, _cts.Token)
                .ConfigureAwait(true);

        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync);
    }

    private async Task DeleteQuestionAsync(VmQuestion question)
    {
        Models.NoPayloadApiResponse response =
            await SubmissionQuestionsClient
                .DeleteQuestionAsync(question.Id, _cts.Token)
                .ConfigureAwait(true);

        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync);
    }
    
    private async Task ApproveQuestionAsync(VmQuestion question)
    {
        ApproveSubmissionQuestionRequest request = new(question.Id);
        Models.NoPayloadApiResponse response = 
            await SubmissionQuestionsClient
                .ApproveQuestionAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        await NotificationHelper.HandleNoPayloadResponseWithFuncAsync(response, GetMessagesAsync).ConfigureAwait(true);
    }
    
    private class Vm
    {
        public VmQuestion[] Questions = [];
    }

    private record VmQuestion
    {
        public required string Id { get; init; }
        public required DateTime AskedAt { get; init; }
        public required string AskedBy { get; init; }
        public required string Content { get; init; }
        public required QuestionStatus Status { get; init; }
        public DateTime? EditedAt { get; init; }
        public VmMessage[] Messages = [];
        public string NewQuestion { get; set; } = string.Empty;

        public bool IsBeingEdited; 
        public string NewContent = string.Empty;
        
        public bool IsEdited => EditedAt.HasValue;
        public bool IsEditable => Status == QuestionStatus.AwaitingApproval;
        public bool MessagesVisible => Status == QuestionStatus.Approved;

        public void StartEditing()
        {
            IsBeingEdited = true;
            NewContent = Content;
        }

        public void StopEditing()
        {
            IsBeingEdited = false;
            NewContent = string.Empty;
        }
    }

    private class VmMessage
    {
        public required Guid Id { get; init; }
        public required DateTime AnsweredAt { get; init; }
        public required string AnsweredBy { get; init; }
        public required string Content { get; init; }
        public required bool IsEditable { get; init; }
        public string NewContent { get; set; } = string.Empty;
        public DateTime? EditedAt { get; init; }
        public bool IsBeingEdited { get; set; }
        public bool CanEdit => !IsBeingEdited;

        public void StartEditing()
        {
            IsBeingEdited = true;
            NewContent = Content;
        }

        public void StopEditing()
        {
            IsBeingEdited = false;
            NewContent = string.Empty;
        }
    }

    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
}