namespace Theia.App.Client.Common.Themes;

public static class Themes
{
    public static ThemeSetting[] AvailableThemes = {OceanB<PERSON>, MainDark, Default, Bootstrap, Material, Fluent};

    public static readonly ThemeSetting OceanBlue = new("Ocean Blue", "default-ocean-blue", true, "default");
    public static readonly ThemeSetting MainDark = new("Main Dark", "default-main-dark", true, "default");
    public static readonly ThemeSetting Default = new("Default", "default", false, "default");
    public static readonly ThemeSetting Bootstrap = new("Bootstrap", "bootstrap", false, "bootstrap");
    public static readonly ThemeSetting Material = new("Material", "material", false, "material");
    public static readonly ThemeSetting Fluent = new("Fluent", "fluent", false, "fluent");
}