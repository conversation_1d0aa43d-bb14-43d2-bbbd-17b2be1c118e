<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <LangVersion>12</LangVersion>
        <UserSecretsId>904d817f-2807-43dd-b17f-2b4ba1b8acd3</UserSecretsId>
    </PropertyGroup>


    <ItemGroup>
        <SupportedPlatform Include="browser"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Blazored.FluentValidation" Version="2.1.0" />
        <PackageReference Include="Blazored.LocalStorage" Version="4.4.0" />
        <PackageReference Include="Fluxor.Blazor.Web" Version="6.5.2" />
        <PackageReference Include="Humanizer" Version="2.14.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.0.11" />
        <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
        <PackageReference Include="OneOf" Version="3.0.263" />
        <PackageReference Include="OneOf.SourceGenerator" Version="3.0.263" />
        <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0"/>
        <PackageReference Include="System.Private.Uri" Version="4.3.2" />
        <PackageReference Include="telerik.ui.for.blazor" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <AdditionalFiles Include="Components\Indications\Components\OrganisationData.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Contracts\Contracts.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Contracts\ContractsForm.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Contracts\TopContracts.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Financials\CountryRevenue.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Financials\Financials.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Financials\IndustryRevenue.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\ItOt\DataCentres.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\ItOt\Infrastructure.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\ItOt\ItOt.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Shared\RevenueInfo.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Vendor\AddVendorsWizard.razor" />
        <AdditionalFiles Include="Components\OrganisationProfile\Vendor\Vendors.razor" />
        <AdditionalFiles Include="Themes\ThemeChooser.razor"/>
        <AdditionalFiles Include="TopMenu\DrawTemplate.razor"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Theia.App.Shared.Login\Theia.App.Shared.Login.csproj" />
        <ProjectReference Include="..\Theia.FrontendResources\Theia.FrontendResources.csproj"/>
        <ProjectReference Include="..\Theia.App.Shared\Theia.App.Shared.csproj"/>
        <ProjectReference Include="..\Theia.App.Shared.Admin\Theia.App.Shared.Admin.csproj"/>
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Components\OrganisationProfile\Vendor\AddVendorsWizard.razor" />
      <UpToDateCheckInput Remove="Components\OrganisationProfile\Vendor\Vendors.razor" />
      <UpToDateCheckInput Remove="Components\Supplier\SupplierProductsStep.razor" />
    </ItemGroup>




</Project>
