using Theia.App.Shared.Models;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared.QuotaShares;
using Theia.App.Shared.QuotaShares.Followers;
using Theia.App.Shared.Submissions.ProgramStack.GetSubmissionProgramStack;

namespace Theia.App.Client.Common.Consumers;

public class QuotaSharesClient(HttpService httpService)
{
    public Task<ApiResponse<GetQuotaShareResponse>> GetQuotaShareAsync(Guid indicationId, CancellationToken ct)
        => httpService.GetAsync<GetQuotaShareResponse>($"quota-shares/{indicationId}", ct);

    public Task<ApiResponse<GetFollowMarketsResponse>> GetFollowMarketsAsync(Guid indicationId, CancellationToken ct)
        => httpService.GetAsync<GetFollowMarketsResponse>($"quota-shares/{indicationId}/follow-markets", ct);

    public Task<ApiResponse<AddQuotaShareFollowersResponse>> AddMultipleUnderwritersToQuotaShareAsync(AddQuotaShareFollowersRequest followersRequest, CancellationToken ct)
        => httpService.PostAsync<AddQuotaShareFollowersRequest, AddQuotaShareFollowersResponse>("quota-shares", followersRequest, ct);

    public Task<ApiResponse<BrokerSubmissionTenantModel[]>> RetrieveUnderwritersAsync(GetQuotaShareUnderwritersRequest request, CancellationToken ct)
        => httpService.GetAsync<BrokerSubmissionTenantModel[]>($"quota-shares/{request.LeadOptionId}/retrieve-underwriters", ct);

    public Task<NoPayloadApiResponse> DeleteQuotaShareFollowerAsync(DeleteQuotaShareFollowerRequest request, CancellationToken ct)
        => httpService.DeleteAsync($"quota-shares/followers/{request.QuotaShareFollowerId}", ct);

    public Task<NoPayloadApiResponse> UpdateQuotaShareFollowerStatusAsync(UpdateQuotaShareFollowerStatusRequest request, CancellationToken ct)
        => httpService.PutAsync("quota-shares/followers/status", request, ct);

    public Task<ApiResponse<AddQuotaShareFollowerDetailsResponse>> GetQuotaShareFollowerDetailsAsync(Guid quotaShareFollowerId, CancellationToken ct)
        => httpService.GetAsync<AddQuotaShareFollowerDetailsResponse>($"quota-shares/followers/{quotaShareFollowerId}/details", ct);

    public Task<NoPayloadApiResponse> FollowQuotaShareFollowerAsync(FollowQuotaShareFollowerRequest request, CancellationToken ct)
        => httpService.PutAsync("quota-shares/followers/follow", request, ct);

    public Task<NoPayloadApiResponse> UnfollowQuotaShareFollowerAsync(UnfollowQuotaShareFollowerRequest request, CancellationToken ct)
        => httpService.PutAsync("quota-shares/followers/unfollow", request, ct);
}
