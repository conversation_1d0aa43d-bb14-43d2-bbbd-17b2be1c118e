using Microsoft.JSInterop;

namespace Theia.App.Client.Common.Custom;

public static class JavaScriptCallbacks
{
    public delegate void StorageChangedHandler(string? key, string? oldValue, string? newValue);
    public static event StorageChangedHandler? OnStorageChanged;
    public static event Func<string?, string?, string?, Task>? OnStorageChangedAsync;

    [JSInvokable]
    public static void LocalStorageChanged(string? key, string? oldValue, string? newValue)
    {
        OnStorageChanged?.Invoke(key, oldValue, newValue);
        OnStorageChangedAsync?.Invoke(key, oldValue, newValue);
    }
}