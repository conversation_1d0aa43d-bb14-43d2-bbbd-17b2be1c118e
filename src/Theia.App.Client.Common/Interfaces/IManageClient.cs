using Theia.App.Client.Common.Models.ChangeEmail;
using Theia.App.Client.Common.Models.ChangePassword;
using Theia.App.Client.Common.Models.ConfirmEmailChange;
using Theia.App.Client.Common.Models.DeletePersonalData;
using Theia.App.Client.Common.Models.UpdateUserProfile;
using Theia.App.Client.Common.Queries.DownloadPersonalData;
using Theia.App.Client.Common.Queries.GetUser;

namespace Theia.App.Client.Common.Interfaces;

public interface IManageClient
{
    Task<Models.ApiResponse<ChangeEmailResponse>> ChangeEmailAsync(ChangeEmailCommand request, CancellationToken ct);
    Task<Models.ApiResponse<ChangePasswordResponse>> ChangePasswordAsync(ChangePasswordCommand request, CancellationToken ct);
    Task<Models.ApiResponse<ChangeEmailResponse>> ConfirmEmailChangeAsync(ConfirmEmailChangeCommand request, CancellationToken ct);
    Task<Models.ApiResponse<string>> DeletePersonalData(DeletePersonalDataCommand request, CancellationToken ct);
    Task<Models.ApiResponse<DownloadPersonalDataResponse>> DownloadPersonalData(CancellationToken ct);
    Task<Models.ApiResponse<CurrentUserForEdit>> GetUserAsync(CancellationToken ct);
    Task<Models.ApiResponse<string>> UpdateUserProfileAsync(UpdateUserProfileCommand request, CancellationToken ct);
}