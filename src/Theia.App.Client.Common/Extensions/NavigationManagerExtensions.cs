using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Primitives;

namespace Theia.App.Client.Common.Extensions;

public static class NavigationManagerExtensions
{
    public static bool TryGetQueryString<T>(this NavigationManager navManager, string key, out T value)
    {
        Uri uri = navManager.ToAbsoluteUri(navManager.Uri);

        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue(key, out StringValues valueFromQueryString))
        {
            if (typeof(T) == typeof(int) && int.TryParse(valueFromQueryString, out int valueAsInt))
            {
                value = (T)(object)valueAsInt;
                return true;
            }

            if (typeof(T) == typeof(string))
            {
                value = (T)(object)valueFromQueryString.ToString();
                return true;
            }

            if (typeof(T) == typeof(decimal) && decimal.TryParse(valueFromQueryString, out decimal valueAsDecimal))
            {
                value = (T)(object)valueAsDecimal;
                return true;
            }
        }

        value = default;
        return false;
    }

    public static string GetSubDomain(this NavigationManager navManager)
    {
        char dot = '.';

        string navManagerBaseUri = navManager.BaseUri;

        int dotCount = navManagerBaseUri.Count(c => c == dot);

        switch (dotCount)
        {
            case 0 when navManagerBaseUri.Contains("localhost"):
            case 1 when !navManagerBaseUri.Contains("localhost"):
            case 2 when navManagerBaseUri.Contains("www"):
                return null;
            default:
                {
                    string subDomain = navManagerBaseUri.Split('.')[0].Split("//")[1];
                    return subDomain;
                }
        }
    }

    public static bool IsHost(this NavigationManager navManager)
    {
        char dot = '.';

        string navManagerBaseUri = navManager.BaseUri;

        int dotCount = navManagerBaseUri.Count(c => c == dot);

        return dotCount switch
        {
            0 when navManagerBaseUri.Contains("localhost") => true,
            1 when !navManagerBaseUri.Contains("localhost") => true,
            2 when navManagerBaseUri.Contains("www") => true,
            _ => false
        };
    }
}