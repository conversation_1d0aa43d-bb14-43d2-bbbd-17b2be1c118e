using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.Infrastructure.Common.DTOs;

namespace Theia.App.Client.Organisation.Consumers;

public interface IOrganisationUsersClient
{
    Task<NoPayloadApiResponse> SubmitBrokerRequestAsync(RequestBrokerRecord request, CancellationToken ct);
}

public class OrganisationUsersClient : IOrganisationUsersClient
{
    private readonly HttpService _httpService;

    public OrganisationUsersClient(HttpService httpService)
    {
        _httpService = httpService;
    }

    public Task<NoPayloadApiResponse> SubmitBrokerRequestAsync(RequestBrokerRecord request, CancellationToken ct)
        => _httpService.PostAsync("organisationUsers/request-new-broker", request, ct);
}