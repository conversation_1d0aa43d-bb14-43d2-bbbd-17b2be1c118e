using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Dtos;
using Theia.App.Shared.Organisation.Submissions;
using Theia.App.Shared.Organisation.Suppliers;

namespace Theia.App.Client.Organisation.Consumers;

public class SubmissionClient(HttpService httpService)
{
    public Task<NoPayloadApiResponse> AddSuppliersAsync(AddSuppliersRequest request, CancellationToken ct)
        => httpService.PostAsync("submissions/suppliers", request, ct);

    public Task<ApiResponse<DataEnvelope<GetSubmissionSuppliersResponseItem>>> GetSuppliersAsync(GetSubmissionSuppliersRequest request, CancellationToken ct)
        => httpService.PostAsync<GetSubmissionSuppliersRequest, DataEnvelope<GetSubmissionSuppliersResponseItem>>("submission/get-suppliers", request, ct);

    public Task<NoPayloadApiResponse> RemoveSupplierFromSubmissionAsync(RemoveSupplierRequest request, CancellationToken ct)
        => httpService.DeleteAsync($"submissions/suppliers?{nameof(request.SubmissionId)}={request.SubmissionId}&{nameof(request.SupplierId)}={request.SupplierId}", ct);
}