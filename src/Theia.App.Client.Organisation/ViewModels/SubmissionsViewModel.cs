using Telerik.Blazor;
using Theia.App.Shared.Models;
using Theia.App.Shared.Organisation.Models;
using Theia.App.Shared.Vendors;
using Theia.Domain.Common.Enums;
using Theia.FrontendResources;
using Theia.Infrastructure.Common.Enums;

namespace Theia.App.Client.Organisation.ViewModels;

public class SubmissionsViewModel
{
    public required Guid SubmissionId { get; init; }
    public string SubmissionName { get; init; } = string.Empty;
    public bool IsVoided { get; set; }
    public bool IsSubmitted { get; set; }
    public string? RequestedByName { get; set; }
    public int PercentageComplete { get; init; }
    public DateTimeOffset RequestedOn { get; init; }
    public bool IsInternal { get; init; }
    public JobStatus? JobStatus { get; init; }
    public required SubmissionStatus SubmissionStatus { get; init; }
    public string SubmissionStatusColour => SubmissionStatus switch
    {
        SubmissionStatus.Error => ThemeConstants.Chip.ThemeColor.Error,
        SubmissionStatus.Submitted => ThemeConstants.Chip.ThemeColor.Success,
        SubmissionStatus.Unsubmitted => ThemeConstants.Chip.ThemeColor.Warning,
        SubmissionStatus.Voided => ThemeConstants.Chip.ThemeColor.Base,
        _ => ThemeConstants.Chip.ThemeColor.Base,
    };

    public string SubmissionStatusDescription => SubmissionStatus switch
    {
        SubmissionStatus.Error => Resource.Error, 
        SubmissionStatus.Submitted => Resource.Submitted,
        SubmissionStatus.Unsubmitted => Resource.Unsubmitted,
        SubmissionStatus.Voided => Resource.Voided,
        _ => Resource.NotApplicable,
    };
    
    public bool HasUnansweredQuestions { get; init; }
}