@page "/application/answer-application-form/{FormId}/{SubmissionId}";
@page "/application/answer-application-form/{SupplierApplicationFormVersionId}";

<PageHeader Header="@(IsFormEditable ? Resource.Answer_Application_Form : Resource.View_Application_Form)" />
@if (IsFormEditable)
{
    <TelerikButton
            Class="k-mb-3"
            OnClick="@InvokeSave"
            ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
            Size="@(ThemeConstants.Button.Size.Medium)"
            Icon="FontIcon.Save">
        @Resource.Save_progress
    </TelerikButton>
}
else
{
    <TelerikButton
            Class="k-mb-3"
            OnClick="@InvokeSaveSurveyAsPdf"
            ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
            Size="@(ThemeConstants.Button.Size.Medium)"
            Icon="FontIcon.Download">
        @Resource.Download_as_PDF
    </TelerikButton>
}

<div>
    <survey id="surveyAnswer" params="survey: model"></survey>
</div>

<TelerikDialog Visible="@IsYesNoDialog"
               Title="@Resource.Complete_Application_Form"
               Width="450px"
               Height="275px"
               ShowCloseButton="false">
    <DialogContent>
        <p>@Resource.Application_form_submission</p>
    </DialogContent>
    <DialogButtons>
        <TelerikButton
            Icon="@FontIcon.Cancel"
            Size="@(ThemeConstants.Button.Size.Large)"
            Rounded="@(ThemeConstants.Button.Rounded.Large)"
            ButtonType="ButtonType.Submit"
            OnClick="@OnNoClicked">
            @Resource.Cancel
        </TelerikButton>
        <TelerikButton
            Icon="@FontIcon.Check"
            Size="@(ThemeConstants.Button.Size.Large)"
            Rounded="@(ThemeConstants.Button.Rounded.Large)"
            ButtonType="ButtonType.Submit"
            ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
            OnClick="@OnYesClicked">
            @Resource.Complete
        </TelerikButton>
    </DialogButtons>
</TelerikDialog>

<style>
    .center-content {
        display: flex;
        justify-content: center;
        align-items: center;
        height: auto;
    }
</style>

@code {
    
}