using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Theia.App.Client.Common;
using Theia.App.Client.Organisation.Consumers;
using Theia.App.Shared.Models;
using Theia.FrontendResources;
using Theia.Http.Services;

namespace Theia.App.Client.Organisation.Pages.Suppliers;

public partial class SupplierFormsAndFiles : IDisposable
{
    [Parameter]
    public string? SubmissionId { get; init; }
    
    [Parameter]
    public bool ShowProductsTab { get; init; } = true;
    
    [Parameter]
    [EditorRequired]
    public SupplierFormsAndFilesVm? Vm { get; set; }
    
    [Inject]
    private NavigationManager NavigationManager { get; init; } = null!;
    
    [Inject]
    private IJSRuntime Js { get; init; } = null!;
    
    [Inject]
    private SuppliersClient SuppliersClient { get; init; } = null!;

    private CancellationTokenSource _cts = new();
    
    public class SupplierFormsAndFilesVm
    {
        public required SupplierFormsAndFilesVmSnapshot[] Snapshots { get; init; }
        public required SupplierFormsAndFilesVmFile[] Files { get; init; }
        public required SupplierFormsAndFilesProduct[] Products { get; init; }
    }

    public class SupplierFormsAndFilesVmSnapshot
    {
        public required string ApplicationFormName { get; init; }
        public required Guid? SnapshotId { get; init; }
        public string OpenFormBtnTitle => SnapshotId.HasValue ? Resource.Tooltip_open_application_form : Resource.Awaiting_completion_by_supplier;
        public string OpenAnalysisBtnTitle => SnapshotId.HasValue ? Resource.Tooltip_open_analysis : Resource.Awaiting_completion_by_supplier;
    }
    
    public class SupplierFormsAndFilesVmFile
    {
        public required Guid FileId { get; init; }
        public required Guid? UploadedFileId { get; init; }
        public required string RequestFileName { get; init; }
        public required string OriginalFilename { get; init; }
        public required string? DownloadFilename { get; init; }
        public bool IsNotApplicable { get; init; }
        public required bool SubmissionRequestCompleted { get; init; }

        public bool DownloadBtnEnabled => SubmissionRequestCompleted && !IsNotApplicable;
        public string DownloadBtnTitle =>
            (SubmissionRequestCompleted, IsNotApplicable) switch
            {
                (true, false) => Resource.Download,
                (true, true) => Resource.Supplier_did_not_provide_file,
                (false, _) => Resource.Awaiting_completion_by_supplier
            };
    }

    public class SupplierFormsAndFilesProduct
    {
        public required Guid Id { get; init; }
        public required string Name { get; init; }
        public required string? Version { get; init; }
    }
    
    private void OpenApplicationFormToView(WebSafeGuid? supplierApplicationFormVersionId)
    {
        if (!supplierApplicationFormVersionId.HasValue) return;
        NavigationManager.NavigateTo(string.IsNullOrWhiteSpace(SubmissionId)
            ? $"{CommonUrlConstants.AnswerApplicationForm}/{supplierApplicationFormVersionId.Value}"
            : $"{CommonUrlConstants.ViewSupplierDashboardApplicationForm}/{SubmissionId}/{supplierApplicationFormVersionId}");
    }

    private void OpenApplicationFormAnalysis(Guid? supplierApplicationFormVersionId)
    {
        if (!supplierApplicationFormVersionId.HasValue) return;
        NavigationManager.NavigateTo($"{CommonUrlConstants.SupplierDashboard}/{(WebSafeGuid)supplierApplicationFormVersionId.Value}");
    }
    
    private async Task DownloadFile(Guid? fileToDownloadId, string? fileName)
    {
        if (!fileToDownloadId.HasValue) return;
        
        Stream downloadStream = await SuppliersClient
            .DownloadFileAsync(fileToDownloadId.Value, _cts.Token)
            .ConfigureAwait(false);
        using DotNetStreamReference streamReference = new(downloadStream);
        await Js.InvokeVoidAsync("downloadFileFromStream", fileName, streamReference).ConfigureAwait(false);
    }

    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
}
