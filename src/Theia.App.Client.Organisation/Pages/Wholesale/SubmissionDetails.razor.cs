using Microsoft.AspNetCore.Components;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Services;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.FrontendResources;

namespace Theia.App.Client.Organisation.Pages.Wholesale;

[Route(CommonUrlConstants.Submission)]
public partial class SubmissionDetails
{
    [Parameter]
    public string? SubmissionId { get; set; }
    
    [Parameter]
    public string? OrganisationId { get; set; }

    [Inject]
    private IBreadcrumbService BreadcrumbService { get; init; } = null!;

    protected override async Task OnInitializedAsync()
    {
        await SetupBreadcrumbsAsync().ConfigureAwait(true);
        await base.OnInitializedAsync().ConfigureAwait(true);
    }

    private async Task SetupBreadcrumbsAsync()
    {
        if (!string.IsNullOrWhiteSpace(SubmissionId))
        {
            await BreadcrumbService.AddBreadcrumb(new(
                Resource.Full_Submission,
                CommonUrlBuilder.Submission(OrganisationId!, SubmissionId)))
                .ConfigureAwait(false);
        }
    }
}