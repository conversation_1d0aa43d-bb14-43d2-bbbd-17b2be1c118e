using Theia.Identity.Interface.Shared;

namespace Theia.Identity.Interface.UpdateUser;

public record UpdateUserRequest
{
    public required string UserId { get; init; }
    public required string Forename { get; init; }
    public required string Surname { get; init; }
    public required bool DisableUser { get; init; }
    public required string SelectedRoleId { get; init; }
    public required OrganisationTypes OrganisationTypes { get; init; }
}