using Theia.Identity.Interface.CreateRole;
using Theia.Identity.Interface.CreateUser;
using Theia.Identity.Interface.Roles;
using Theia.Identity.Interface.UpdateUser;

namespace Theia.Identity.Interface;

public interface IAuthIdentityApi
{
    Task<CreateUserResponse> CreateUserAsync(CreateUserRequest request, CancellationToken ct);
    Task<bool> UpdateUserAsync(UpdateUserRequest request, CancellationToken ct);
    Task SendChangePasswordInvitationEmail(string email, CancellationToken ct);
    Task UpdateUsersNameAsync(UpdateUsersNameRequest request, CancellationToken ct);
    Task UpdateUsersEmailAsync(UpdateUsersEmailRequest request, CancellationToken ct);
    Task ConfirmUsersEmail(ConfirmUsersEmailRequest request, CancellationToken ct);
    Task SendChangePasswordEmailAsync(ChangeUsersPasswordRequest request, CancellationToken ct);
    Task RemoveUsersMfaAsync(string userId, CancellationToken ct);
    Task ChangeRolesAsync(string userId, string[] rolesToRemoveId, string[] newRolesId, CancellationToken ct);
    Task BlockUserAsync(string userId, CancellationToken ct);
    Task ResendEmailConfirmation(string userId, CancellationToken ct);
    Task<GetAllRolesResponse> GetAllRolesAsync(CancellationToken ct);
    Task<IEnumerable<IdentityPermission>> GetAllApiPermissionsAsync(CancellationToken ct);
    Task ReplaceApiPermissionsAsync(IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct);
    Task<CreateRoleResponse> CreateRoleAsync(IdentityRole role, CancellationToken ct);
    Task<ICollection<IdentityPermission>> GetAllRolesPermissionsAsync(string roleAuthId, CancellationToken ct);
    Task AssignRolePermissionsAsync(string roleAuthId, IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct);
    Task RemovePermissionsFromRoleAsync(string roleId, IReadOnlyCollection<IdentityPermission> permissionName, CancellationToken ct);
    Task UpdateRoleNameAsync(string authId, string roleName, CancellationToken ct);
}