using Theia.Identity.Interface.Shared;

namespace Theia.Identity.Interface.CreateUser;

public record CreateUserRequest
{
    public required UserInfo User { get; init; }
    public required string RoleAuthId { get; init; }
    public required OrganisationTypes OrganisationTypes { get; init; }
    public required string? TenantHostname { get; init; }
    
    public record UserInfo
    {
        public string? FirstName { get; init; }
        public string? LastName { get; init; }
        public required string Email { get; init; }
        public string? PhoneNumber { get; init; }
    }
}
