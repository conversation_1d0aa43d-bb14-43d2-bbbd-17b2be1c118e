<!DOCTYPE html>
<html>

<head prefix="og: http://ogp.me/ns#">
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <title>Admins | TheiaLens</title>
    <meta name="description" content="TheiaLens | Analytics Platform">
    <base href="/"/>
    <link href="https://fonts.googleapis.com/css2?family=Play:wght@400;700&family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" rel="stylesheet"/>
    <link href="_content/Telerik.UI.for.Blazor/css/kendo-theme-material/all.css" id="theme" rel="stylesheet"/>
    <link rel="stylesheet" href="https://unpkg.com/@progress/kendo-theme-utils@10.2.0/dist/all.css" />
    <link href="_content/Telerik.UI.for.Blazor/css/kendo-font-icons/font-icons.css" rel="stylesheet" />
    <link href="_content/Theia.App.Client.Common/css/app.css" rel="stylesheet"/>
    <link href="_content/Theia.App.Client.Common/css/main.css" rel="stylesheet"/>
    <link href="manifest.json" rel="manifest"/>
    <script src="_content/Telerik.UI.for.Blazor/js/telerik-blazor.js" defer></script>

    <script type="text/javascript" src="js/surveyjs/knockout/build/output/knockout-latest.js"></script>
    <link href="js/surveyjs/survey-core/defaultV2.min.css" type="text/css" rel="stylesheet">
    <script src="js/surveyjs/survey-core/survey.core.min.js"></script>
    <script src="js/surveyjs/survey-knockout-ui/survey-knockout-ui.min.js"></script>
    <link href="js/surveyjs/survey-creator-core/survey-creator-core.min.css" type="text/css" rel="stylesheet">
    <script src="js/surveyjs/survey-creator-core/survey-creator-core.min.js"></script>
    <script src="js/surveyjs/survey-creator-knockout/survey-creator-knockout.min.js"></script>
    <script type="text/javascript" src="js/surveyjs/index.js"></script>
</head>
<body>
<div id="app">
    <div class="k-stack-layout telerik-blazor k-vstack k-align-items-center k-justify-content-center" dir="ltr" style="height: 100%; width: 100%">
        <img style="height: 50vh;" src="_content/Theia.App.Client.Common/images/theia-logo-cyan.svg" rel="preload">
        <h1 class="logo-message">Loading...</h1>
    </div>
</div>

<script src="_content/Microsoft.AspNetCore.Components.WebAssembly.Authentication/AuthenticationService.js"></script>
<script src="_framework/blazor.webassembly.js" autostart="false"></script>
<script>
    window.interopFunctions = {
        scrollToHeader: function () {
            const target = document.getElementById("scroll-to-target");
            if (target) {
                target.scrollIntoView()
            }
        }
    }

    document.addEventListener("DOMContentLoaded", function () {
        if (window.location.hostname.includes("localhost")) {
            Blazor.start({
                environment: "Local"
            });
        } else if (window.location.hostname.includes("theialens.xyz")) {
            Blazor.start({
                environment: "Development"
            });
        } else if (window.location.hostname.includes("theialensuat.com")) {
            Blazor.start({
                environment: "UAT"
            })
        } else {
            Blazor.start();
        }
    });
</script>
<script>
    window.addEventListener('storage', function (e) {
        if (e.storageArea === localStorage) {
            DotNet.invokeMethodAsync('Theia.App.Client.Common', 'LocalStorageChanged', e.key, e.oldValue, e.newValue);
        }
    });
</script>
<script src="https://code.iconify.design/iconify-icon/1.0.2/iconify-icon.min.js"></script>
<script src="https://code.iconify.design/2/2.0.3/iconify.min.js"></script>
<script src="https://use.fontawesome.com/bae3fdbbaf.js"></script>
<script src="_content/Theia.App.Client.Common/js/Utilities.js"></script>
<script src="js/qrcode.js"></script>
</body>
</html>