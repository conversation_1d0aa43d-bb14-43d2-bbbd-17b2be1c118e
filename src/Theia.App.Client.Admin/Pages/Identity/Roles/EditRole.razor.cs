using Theia.App.Client.Admin.Features.Identity.Permissions.Queries.GetPermissions;
using Theia.App.Client.Admin.Features.Identity.Roles.Commands.UpdateRole;
using Theia.App.Client.Admin.Features.Identity.Roles.Queries.GetRoleForEdit;
using Theia.App.Client.Admin.Interfaces.Consumers;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.Infrastructure.Common.Permissions;
using BreadcrumbItem = Theia.App.Client.Common.Services.Breadcrumbs.BreadcrumbItem;

namespace Theia.App.Client.Admin.Pages.Identity.Roles;

[PermissionAuthorize(PermissionTypes.UpdateRole)]
public partial class EditRole : IDisposable
{
    [Parameter]
    public Guid RoleId { get; set; }

    public IEnumerable<object> CheckedPermissions { get; set; } = new List<object>();

    [Inject]
    private IBreadcrumbService BreadcrumbService { get; set; }

    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private IRolesClient RolesClient { get; set; }

    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private EditContextServerSideValidator EditContextServerSideValidator { get; set; }
    private HashSet<PermissionItem> PermissionItems { get; set; } = new();
    private RoleForEdit RoleForEditVm { get; set; } = new();
    private UpdateRoleCommand UpdateRoleCommand { get; set; }
    private bool EditRoleDialogVisible { get; set; } = false;
    private readonly CancellationTokenSource _cts = new();

    protected override async Task OnInitializedAsync()
    {
        SetupBreadcrumbs();
        await GetRoleAsync().ConfigureAwait(true);
        await InitializeTreeAsync().ConfigureAwait(true);
    }

    private async Task GetRoleAsync()
    {
        ApiResponse<RoleForEdit> response = 
            await RolesClient
                .GetRoleAsync(new GetRoleForEditQuery {Id = RoleId}, _cts.Token)
                .ConfigureAwait(false);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            RoleForEditVm = successResult?.Result;
        });
    }

    private async Task InitializeTreeAsync()
    {
        PermissionItems = new HashSet<PermissionItem>();

        ApiResponse<RolePermissionsResponse> response =
            await RolesClient
                .GetRolePermissionsAsync(new GetRolePermissionsForEditQuery {PermissionId = null, RoleId = RoleId}, _cts.Token)
                .ConfigureAwait(false);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            PermissionItems = successResult?.Result.RequestedPermissions.ToHashSet();
            HashSet<PermissionItem> selectedPermissionsResponse = successResult?.Result.SelectedPermissions.ToHashSet();
            List<PermissionItem> selectedPermissions = 
                PermissionItems.Where(a => selectedPermissionsResponse.Any(b => a.Id == b.Id)).ToList();
            CheckedPermissions = new List<object>(selectedPermissions.ToList());
        });
    }

    private async Task SubmitFormAsync()
    {
        EditRoleDialogVisible = false;

        UpdateRoleCommand = new UpdateRoleCommand
        {
            Id = RoleForEditVm.Id,
            Name = RoleForEditVm.Name,
            IsDefault = RoleForEditVm.IsDefault,
            SelectedPermissionIds = CheckedPermissions.Cast<PermissionItem>().Select(p => p.Id).ToList()
        };
        
        ApiResponse<string> response =
            await RolesClient
                .UpdateRoleAsync(UpdateRoleCommand, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            NavigationManager.NavigateTo("identity/roles");
        }, showSuccess: true);
    }

    private void SetupBreadcrumbs()
    {
        BreadcrumbService.AddBreadcrumb(
            new(
                Resource.Edit_Role, "#"));
    }

    public void Dispose()
    {
        _cts?.Cancel();
        _cts?.Dispose();
    }
}