@page "/identity/roles/EditRole/{roleId:guid}"
@using Theia.App.Client.Common.Consumers

<PageHeader Header="@($"{Resource.Edit_Role} - {RoleForEditVm.Name}")" />
<TelerikForm Id="editRoleForm" Model="RoleForEditVm" OnValidSubmit="SubmitFormAsync">
    <FormValidation>
        <EditContextServerSideValidator @ref="EditContextServerSideValidator"/>
        <FluentValidationValidator/>
    </FormValidation>
    <FormItems>
        <TelerikTabStrip Class="max-height-tts">
            <TabStripTab>
                <HeaderTemplate>
                    <div class="z-md-icon">
                        <iconify-icon icon="la:people-carry" width="18" height="18"></iconify-icon>
                    </div>
                    <strong class="z-tabstriptab">@Resource.Role</strong>
                </HeaderTemplate>
                <Content>
                    <TelerikCard>
                        <CardHeader>
                            <CardTitle>
                                @Resource.Edit_Role
                            </CardTitle>
                        </CardHeader>
                        <CardBody Class="font-size-16">
                            <FormItem Field="@nameof(RoleForEditVm.Name)">
                                <Template>
                                    <label for="name" class="k-label k-form-label">@Resource.Role_Name</label>
                                    <TelerikTextBox Id="name" @bind-Value="@RoleForEditVm.Name"></TelerikTextBox>
                                    <TelerikValidationMessage For="@(() => RoleForEditVm.Name)"></TelerikValidationMessage>
                                </Template>
                            </FormItem>
                            <FormItem Field="@nameof(RoleForEditVm.IsDefault)">
                                <Template>
                                    <div class="z-switch-div">
                                        <TelerikSwitch Id="isDefault" Size="@ThemeConstants.Switch.Size.Small" @bind-Value="@RoleForEditVm.IsDefault" OffLabel="No" OnLabel="Yes"></TelerikSwitch>
                                        <label for="isDefault" class="k-label k-form-label z-switch-label">@Resource.Is_Default</label>
                                        <TelerikValidationMessage For="@(() => RoleForEditVm.IsDefault)"></TelerikValidationMessage>
                                    </div>
                                </Template>
                            </FormItem>
                        </CardBody>
                    </TelerikCard>
                </Content>
            </TabStripTab>
            <TabStripTab>
                <HeaderTemplate>
                    <div class="z-md-icon">
                        <iconify-icon icon="fluent-mdl2:permissions" width="18" height="18"></iconify-icon>
                    </div>
                    <strong class="z-tabstriptab">@Resource.Permissions</strong>
                </HeaderTemplate>
                <Content>
                    <TelerikCard>
                        <CardHeader>
                            <CardTitle>
                                @Resource.Assign_Permissions_for: @RoleForEditVm.Name
                            </CardTitle>
                        </CardHeader>
                        <CardBody Class="font-size-16">
                            <FormItem>
                                <Template>
                                    <TelerikTreeView Data="@PermissionItems"
                                                     @bind-CheckedItems="@CheckedPermissions"
                                                     CheckBoxMode="@TreeViewCheckBoxMode.Multiple"
                                                     CheckParents="false"
                                                     CheckChildren="true"
                                                     CheckOnClick="true">
                                        <TreeViewBindings>
                                            <TreeViewBinding Context="loadingOneShotContext"
                                                             ItemsField="Permissions"
                                                             TextField="Name"
                                                             IdField="Id"
                                                             HasChildrenField="HasChildren"
                                                             ParentIdField="ParentId">
                                            </TreeViewBinding>
                                        </TreeViewBindings>
                                    </TelerikTreeView>

                                </Template>
                            </FormItem>
                        </CardBody>
                    </TelerikCard>
                </Content>
            </TabStripTab>

        </TelerikTabStrip>
    </FormItems>
    <FormButtons>
        <TelerikButton Class="my-button"
                       Icon="@FontIcon.Save"
                       Size="@(ThemeConstants.Button.Size.Large)"
                       ButtonType="Telerik.Blazor.ButtonType.Button"
                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)"
                       OnClick="@(() => EditRoleDialogVisible = true)">
            @Resource.Save
        </TelerikButton>
    </FormButtons>
</TelerikForm>

<TelerikDialog @bind-Visible="@EditRoleDialogVisible" CloseOnOverlayClick="true" Title=@Resource.Save>
    <DialogContent>
        <div>
            @Resource.Are_you_sure_you_want_to_save_role
        </div>
    </DialogContent>
    <DialogButtons>
        <TelerikButton Icon="@FontIcon.Cancel" OnClick="@(() => { EditRoleDialogVisible = false; })">@Resource.Cancel</TelerikButton>
        <TelerikButton Form="editRoleForm" ThemeColor="@(ThemeConstants.Button.ThemeColor.Primary)" ButtonType="Telerik.Blazor.ButtonType.Submit">@Resource.Yes</TelerikButton>
    </DialogButtons>
</TelerikDialog>

<style>
    .k-icon {
        width: 18px;
        height: 18px;
        font-size: 18px;
    }

    .z-switch-label {
        margin-top: 2px !important;
        margin-left: 8px !important;
    }

    .z-switch-div {
        display: inline-flex;
        margin-right: 16px;
    }

    .max-height-tts {
        max-height: 740px;
    }

    .k-treeview {
        font-size: 15px !important;
    }
</style>