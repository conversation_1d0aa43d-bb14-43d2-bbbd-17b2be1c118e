using Theia.App.Client.Admin.Consumers;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Consumers;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Consts;
using Theia.App.Shared.Admin.PolicyForms.ReadForms;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Client.Admin.Pages.PolicyForms;

[PermissionAuthorize(PermissionTypes.ReadPolicyForms)]
[Route($"{AdminAppUrlConstants.PolicyForms}")]
public partial class PolicyForms : IDisposable
{
    [Inject]
    private PolicyFormsCommonClient PolicyFormsCommonClient { get; init; } = null!;
    
    [Inject]
    private PolicyFormsAdminClient PolicyFormsAdminClient { get; init; } = null!;

    [Inject]
    private IBreadcrumbService BreadcrumbService { get; init; } = null!;
    
    [Inject]
    private IJSRuntime Js { get; init; } = null!;
    
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;
    
    private class PolicyVm
    {
        public required Guid Id { get; init; }
        public required string Name { get; set; }
        public required bool IsActive { get; set; }
        public required DateTimeOffset CreatedOn { get; init; }
        public required string FileName { get; init; }
    }

    private int pageSize = DefaultSettings.Pager.DefaultPageSize;
    private TelerikGrid<PolicyVm>? gridRef;
    private readonly CancellationTokenSource cts = new();
    private AddPolicyFormModal? addPolicyFormModal;

    public void Dispose()
    {
        cts.Cancel();
        cts.Dispose();
    }

    protected override Task OnInitializedAsync()
    {
        SetupBreadcrumb();
        return base.OnInitializedAsync();
    }

    private async Task FetchAsync(GridReadEventArgs args)
    {
        ApiResponse<DataEnvelope<AdminReadPolicyFormsResponse>> response = 
            await PolicyFormsAdminClient
                .GetPolicyFormsAsync(args.Request, cts.Token)
                .ConfigureAwait(true);

        NotificationHelper.HandleResultWithAction(response, success =>
        {
            args.Data =
                success.Result.Data
                    .Select(x => new PolicyVm
                    {
                        Id = x.Id, Name = x.Name, IsActive = x.IsActive, CreatedOn = x.CreatedOn,
                        FileName = x.FileName
                    }).ToList();
            args.Total = success.Result.Total;
        });
    }

    private async Task EditAsync(Guid policyId)
    {
        await addPolicyFormModal!.Show(policyId).ConfigureAwait(true);
    }

    private async Task DeleteAsync(GridCommandEventArgs args)
    {
        if (args.Item is PolicyVm vm)
        {
            NoPayloadApiResponse response =
                await PolicyFormsAdminClient.DeletePolicyFormAsync(vm.Id, cts.Token).ConfigureAwait(false);
            
            NotificationHelper.HandleNoPayloadResponseWithFunc(response, gridRef!.Rebind);
        }
    }

    private void OpenModal()
    {
        addPolicyFormModal?.Show();
    }

    private async Task DownloadFileAsync(GridCommandEventArgs args)
    {
        if (args.Item is PolicyVm vm)
        {
            Stream downloadStream = await PolicyFormsCommonClient.DownloadFileAsync(vm.Id, cts.Token).ConfigureAwait(false);
            using DotNetStreamReference streamReference = new(downloadStream);

            await Js.InvokeVoidAsync("downloadFileFromStream", vm.FileName, streamReference).ConfigureAwait(false);  
        }
    }

    private void SetupBreadcrumb()
    {
        BreadcrumbService.AddBreadcrumb(
            new(Resource.Policy_forms, AdminAppUrlConstants.PolicyForms, true));
    }
}