@using Theia.App.Shared.Admin.Organisations.GetAdminOrganisations
@using Theia.Infrastructure.Common.Defaults
<PageHeader Header="@Resource.Bulk_Upload_CSV" />

<TelerikCard>
    <CardHeader>
        <CardTitle>@Resource.Bulk_Upload_CSV</CardTitle>
    </CardHeader>
    <CardBody>
        <TelerikDropDownList
            Class="k-mb-3"
            TItem="GetAdminOrganisationsBasicResponse"
            TValue="Guid"
            Data="@organisations"
            TextField="@nameof(GetAdminOrganisationsBasicResponse.OrganisationName)"
            ValueField="@nameof(GetAdminOrganisationsBasicResponse.OrganisationId)"
            @bind-Value="@selectedOrganisationId" />

        <TelerikUpload SaveUrl="@($"{saveUrl}/{selectedOrganisationId}")"
                       OnUpload="OnFileUpload"
                       AllowedExtensions="@DefaultSettings.Files.ExcelFileExtensions.ToList()"
                       MaxFileSize="@MaxFileSize"
                       SaveField="file"
                       OnSuccess="OnUploadSuccess"
                       OnError="OnUploadError" />
    </CardBody>
</TelerikCard>