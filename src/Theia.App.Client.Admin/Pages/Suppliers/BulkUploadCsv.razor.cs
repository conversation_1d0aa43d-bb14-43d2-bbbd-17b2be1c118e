using Microsoft.AspNetCore.Components.WebAssembly.Authentication;
using Theia.App.Client.Admin.Consumers;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Shared.Admin.Consts;
using Theia.App.Shared.Admin.Organisations.GetAdminOrganisations;

namespace Theia.App.Client.Admin.Pages.Suppliers;

[Route($"/{AdminAppUrlConstants.BulkUploadCsv}")]
public partial class BulkUploadCsv : ComponentBase, IDisposable
{
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;
    
    [Inject]
    private SuppliersClient SuppliersClient { get; init; } = null!;
    
    [Inject]
    private IAccessTokenProvider AccessTokenProvider { get; init; } = null!;
    
    [Inject]
    private IBreadcrumbService BreadcrumbService { get; init; } = null!;
    
    [Inject]
    private ISubDomainProvider SubDomainProvider { get; init; } = null!;
    
    [Inject]
    private IConfiguration Configuration { get; init; } = null!;
    
    [Inject]
    private IOrganisationsClient OrganisationsClient { get; init; } = null!;
    
    private string saveUrl;
    private CancellationTokenSource cts = new();
    private GetAdminOrganisationsBasicResponse[] organisations;
    private Guid selectedOrganisationId;
    
    protected override async Task OnInitializedAsync()
    {
        saveUrl = $"{Configuration.GetSection("BaseApiUrl").Value}admin/import/suppliers";
        await SetupBreadcrumbsAsync();
        await FetchOrganisationsAsync();
        await base.OnInitializedAsync();
    }

    private async Task FetchOrganisationsAsync()
    {
        ApiResponse<GetAdminOrganisationsBasicResponse[]> response = 
            await OrganisationsClient
                .GetAdminOrganisationsBasicAsync(cts.Token)
                .ConfigureAwait(false);
        
        NotificationHelper.HandleResultWithAction(response, success => organisations = success.Result);
    }
    
    private int MaxFileSize => 10 * 1024 * 1024;

    private void OnUploadSuccess() => NotificationHelper.ShowSuccess();

    private void OnUploadError(UploadErrorEventArgs args)
    {
        RegexExcludedErrorResponse errorResponse =
            JsonSerializer.Deserialize<RegexExcludedErrorResponse>(args.Request.ResponseText) ??
            new() { title = Resource.Unexpected_Error };
        
        NotificationHelper.ShowError(errorResponse.title);
    }
    
    private async Task OnFileUpload(UploadEventArgs args)
    {
        if (selectedOrganisationId == Guid.Empty)
        {
            NotificationHelper.ShowError(Resource.Please_select_an_organisation);
            args.IsCancelled = true;
            return;
        }
        
        AccessTokenResult tokenResult = await AccessTokenProvider.RequestAccessToken();
        if (tokenResult.TryGetToken(out AccessToken? token))
        {
            args.RequestHeaders.Add("Authorization", $"Bearer {token.Value}");
            args.RequestHeaders.Add("X-Tenant", SubDomainProvider.GetSubDomain());
        }
    }
    
    private async Task SetupBreadcrumbsAsync()
    {
        await BreadcrumbService.AddBreadcrumb(
            new(
                Resource.Bulk_Upload_CSV, AdminAppUrlConstants.BulkUploadCsv, true));
    }

    public void Dispose()
    {
        cts.Cancel();
        cts.Dispose();
    }
}