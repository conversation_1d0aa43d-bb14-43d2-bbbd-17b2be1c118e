@using Theia.Infrastructure.Common.Defaults

<PageHeader Header="@Resource.Organisations"/>

<TelerikGrid
    @ref="_gridRef"
    TItem="OrganisationVm"
    Size="@ThemeConstants.Grid.Size.Small"
    Sortable
    ShowColumnMenu
    Reorderable
    ConfirmDelete
    Pageable
    @bind-PageSize="_pageSize"
    EditMode="GridEditMode.Inline"
    OnRead="FetchDataAsync"
    OnUpdate="@EditOrganisationAsync"
    OnDelete="@DeleteOrganisationAsync"
    EnableLoaderContainer="DefaultSettings.Grid.IsLoaderVisible">
<GridToolBarTemplate>
    <AppGridSearchBox/>
</GridToolBarTemplate>
<GridSettings>
    <GridValidationSettings>
        <ValidatorTemplate>
            <FluentValidationValidator Validator="@_organisationVmValidator"/>
        </ValidatorTemplate>
    </GridValidationSettings>
    <GridPagerSettings PageSizes="@DefaultSettings.Pager.PageSizes" ButtonCount="@DefaultSettings.Pager.ButtonCount"/>
</GridSettings>
<GridColumns>
    <GridColumn Field="@nameof(OrganisationVm.Name)" Title="@Resource.Name"/>
    <GridColumn Field="@nameof(OrganisationVm.ContactName)" Title="@Resource.Contact_Name"/>
    <GridColumn Field="@nameof(OrganisationVm.ContactEmail)" Title="@Resource.Email"/>
    <GridColumn Field="@nameof(OrganisationVm.ContactPhoneNumber)" Title="@Resource.Phone_Number"/>
    <GridColumn Field="@nameof(OrganisationVm.Subdomain)" Title="@Resource.Subdomain">
        <Template>
            <TelerikTooltip TargetSelector=".app-link" />
            @if (context is OrganisationVm vm)
            {
                <span title="@Resource.Access_Org" class="app-link">
                    <ExternalLink Name="@vm.Subdomain" Url="@vm.Url" />
                </span>
            }
        </Template>
    </GridColumn>
    <GridCommandColumn ShowColumnMenu="false">
        <GridCommandButton Command="Edit" Size="@ThemeConstants.Button.Size.Small" Icon="@FontIcon.Pencil"
                           ThemeColor="@ThemeConstants.Button.ThemeColor.Primary">
            @Resource.Edit
        </GridCommandButton>
        <GridCommandButton Command="Delete" Size="@ThemeConstants.Button.Size.Small"
                           Icon="@FontIcon.Trash" ThemeColor="@ThemeConstants.Button.ThemeColor.Error">
            @Resource.Delete
        </GridCommandButton>
        <GridCommandButton Command="Save" Size="@ThemeConstants.Button.Size.Small" Icon="@FontIcon.Save"
                           ShowInEdit="true" ThemeColor="@ThemeConstants.Button.ThemeColor.Primary">
            @Resource.Save
        </GridCommandButton>
        <GridCommandButton Command="Cancel" Size="@ThemeConstants.Button.Size.Small" Icon="@FontIcon.Cancel"
                           ShowInEdit="true">
            @Resource.Cancel
        </GridCommandButton>
    </GridCommandColumn>
</GridColumns>
<NoDataTemplate>
    <GridNoDataSkeleton Loaded="@_loaded"/>
</NoDataTemplate>
</TelerikGrid>

<style>
    .k-wizard .k-wizard-content { 
        padding: 3em 7em 1em 5em;
    }
    
    #additional-info {
        min-height: 8rem;
    }
</style>