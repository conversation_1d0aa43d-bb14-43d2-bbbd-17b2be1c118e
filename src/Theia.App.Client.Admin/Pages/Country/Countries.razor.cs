using Telerik.DataSource;
using Theia.App.Client.Admin.Enums;
using Theia.App.Client.Admin.Features.Countries.Commands.CreateCountry;
using Theia.App.Client.Admin.Features.Countries.Commands.UpdateCountry;
using Theia.App.Client.Admin.Interfaces.Consumers;
using Theia.App.Client.Admin.Models;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Extensions;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Countries.GetCountries;
using Theia.App.Shared.Admin.Regions.GetRegions;
using Theia.Domain.Common.Enums;
using Theia.Http.Services;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Permissions;
using BreadcrumbItem = Theia.App.Client.Common.Services.Breadcrumbs.BreadcrumbItem;

namespace Theia.App.Client.Admin.Pages.Country;

[PermissionAuthorize(PermissionTypes.GetCountries)]
public partial class Countries : IDisposable
{
    [CascadingParameter]
    public Task<AuthenticationState> AuthenticationState { get; set; }

    [CascadingParameter]
    public DialogFactory Dialogs { get; set; }
    
    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    public List<RegionItem> RegionsData { get; set; }
    public bool Loaded { get; set; }

    [Inject]
    private IBreadcrumbService BreadcrumbService { get; set; }

    [Inject]
    private ICountriesClient CountriesClient { get; set; }

    [Inject]
    private NavigationManager NavigationManager { get; set; }

    [Inject]
    private IRegionsClient RegionsClient { get; set; }
    private int _pageSize = DefaultSettings.Pager.DefaultPageSize;
    private CancellationTokenSource _cts = new();
    private TelerikGrid<CountryItem> _gridRef;

    private List<DropDownListItem> ExposureLevelData { get; set; } = new()
    {
        new() {Text = Resource.Very_Low, Value = (int)ExposureLevel.VeryLow},
        new() {Text = Resource.Low, Value = (int)ExposureLevel.Low},
        new() {Text = Resource.Medium, Value = (int)ExposureLevel.Medium},
        new() {Text = Resource.High, Value = (int)ExposureLevel.High},
        new() {Text = Resource.Very_High, Value = (int)ExposureLevel.VeryHigh}
    };


    protected override void OnInitialized()
    {
        SetupBreadcrumbs();
    }

    private async Task LoadDataAsync(GridReadEventArgs args)
    {
        GetCountriesRequest request = new(args.Request);

        ApiResponse<DataEnvelope<CountryItem>> response =
            await CountriesClient
                .GetCountriesAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            args.Data = successResult.Result.Data;
            args.Total = successResult.Result.Total;
        });

        await LoadRegionsAsync().ConfigureAwait(true);
    }

    private async Task LoadRegionsAsync()
    {
        DataSourceRequest dataSourceRequest = new()
        {
            Page = 0
        };
        
        GetRegionsQuery request = new(dataSourceRequest);
        
        ApiResponse<DataEnvelope<RegionItem>> response = 
            await RegionsClient
                .GetRegionsAsync(request, _cts.Token)
                .ConfigureAwait(false);
        
        NotificationHelper.HandleResultWithAction(response, successResult =>
        {
            RegionsData = successResult.Result.Data.Where(r => r.IsActive).ToList();
        });
    }

    private async Task CreateItemAsync(GridCommandEventArgs args)
    {
        if (args.Item is not CountryItem countryItem) return;

        CreateCountryRequest request = new() 
        {
            Name = countryItem.Name,
            ExposureLevel = countryItem.ExposureLevel,
            IsActive = countryItem.IsActive,
            RegionId = countryItem.RegionId
        };

        ApiResponse<CreateCountryResponse> response = 
            await CountriesClient
                .CreateCountryAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleResultWithAction(response, _ => _gridRef.Rebind());
    }

    private async Task UpdateItemAsync(GridCommandEventArgs args)
    {
        bool confirmed = await Dialogs.ConfirmAsync(Resource.Are_you_sure_you_want_to_save_country, Resource.Update).ConfigureAwait(true);

        if (confirmed)
        {
            if (args.Item is not CountryItem countryItem)
            {
                return;
            }

            UpdateCountryCommand request = new()
            {
                Id = countryItem.Id,
                Name = countryItem.Name,
                ExposureLevel = countryItem.ExposureLevel,
                IsActive = countryItem.IsActive,
                RegionId = countryItem.RegionId
            };

            ApiResponse<string> httpResponse =
                await CountriesClient
                    .UpdateCountryAsync(request, _cts.Token)
                    .ConfigureAwait(true);

            NotificationHelper.HandleResultWithAction(httpResponse, _ => _gridRef.Rebind());
        }
    }

    private void SetupBreadcrumbs()
    {
        BreadcrumbService.AddBreadcrumb(
            new(
                Resource.Countries, "settings/countries", true));
    }

    public void Dispose()
    {
        _cts?.Cancel();
        _cts?.Dispose();
    }
}