using Theia.App.Client.Admin.Consumers;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Attributes;
using Theia.App.Client.Common.Components;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Breadcrumbs;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Consts;
using Theia.App.Shared.Admin.Organisations;
using Theia.App.Shared.Admin.Organisations.AddOrganisation;
using Theia.App.Shared.Admin.Organisations.EditOrganisation;
using Theia.App.Shared.OrganisationRequests.GetOrganisationRequests;
using Theia.Domain.Common.Enums;
using Theia.Infrastructure.Common.Defaults;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Client.Admin.Pages.OrganisationRequests;

[Route($"/{AdminAppUrlConstants.OrganisationRequests}")]
[PermissionAuthorize(PermissionTypes.GetOrganisationRequests)]
public partial class OrganisationRequests : IDisposable
{
    [Inject]
    private IBreadcrumbService BreadcrumbService { get; init; } = null!;

    [Inject]
    private IOrganisationRequestsClient OrganisationRequestsClient { get; init; } = null!;

    [Inject]
    private IOrganisationsClient OrganisationsClient { get; init; } = null!;

    [CascadingParameter]
    private NotificationHelper NotificationHelper { get; init; } = null!;

    private AddOrganisationVm _addOrganisationVm = null!;
    private AddOrganisationVmValidator _addOrganisationVmValidator = new();
    private bool _addWindowVisible;
    private bool? _isClientInformationValid;
    private bool _loaded = true;
    private readonly CancellationTokenSource _cts = new();
    private TelerikGrid<OrganisationRequestVm>? _gridRef;
    private int _pageSize = DefaultSettings.Pager.DefaultPageSize;

    protected override void OnInitialized()
    {
        SetupBreadcrumbs();
    }

    private async Task FetchDataAsync(GridReadEventArgs args)
    {
        ApiResponse<DataEnvelope<GetBrokingHouseOrganisationRequestsResponseItem>> result =
            await OrganisationRequestsClient
                .GetOrganisationRequestsAsync(args.Request, _cts.Token)
                .ConfigureAwait(true);

        NotificationHelper.HandleResultWithAction(result, successResult =>
        {
            args.Data =
                successResult.Result.Data
                    .Select(i => new OrganisationRequestVm
                    {
                        ContactEmail = i.ContactEmail,
                        ContactName = i.ContactName,
                        Name = i.OrganisationName,
                        ContactPhoneNumber = i.ContactPhoneNumber,
                        Id = i.Id,
                        Status = i.Status,
                        AdminEmail = i.AdminEmail,
                        AdminFullName = i.AdminFullName
                    });
            args.Total = successResult.Result.Total;
        });
    }

    private void RefreshAfterAddingOrganisation()
    {
        _addWindowVisible = false;
        _gridRef!.Rebind();
    }

    private async Task DeleteOrganisationRequestAsync(GridCommandEventArgs obj)
    {
        if (obj.Item is OrganisationRequestVm organisation)
        {
            NoPayloadApiResponse result = await 
                OrganisationRequestsClient
                    .DeleteOrganisationRequestAsync(organisation.Id, _cts.Token)
                    .ConfigureAwait(true);
            
            NotificationHelper.HandleNoPayloadResponseWithFunc(result, _gridRef!.Rebind);
        }
    }

    private class OrganisationRequestVm
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ContactName { get; set; } = string.Empty;
        public string ContactEmail { get; set; } = string.Empty;
        public string ContactPhoneNumber { get; set; } = string.Empty;
        public OrganisationRequestStatus Status { get; set; }
        public string AdminFullName { get; set; } = string.Empty;
        public string AdminEmail { get; set; } = string.Empty;
    }

    private class AddOrganisationVm
    {
        public Guid Id { get; set; }
        public string OrganisationName { get; set; }
        public string ContactName { get; set; }
        public string ContactEmail { get; set; }
        public string ContactPhoneNumber { get; set; }
        public string Subdomain { get; set; }
    }

    private class AddOrganisationVmValidator : AbstractValidator<AddOrganisationVm>
    {
        public AddOrganisationVmValidator()
        {
            RuleFor(vm => vm.OrganisationName)
                .NotEmpty()
                .MaximumLength(OrganisationEntityDefaults.NameMaxLength)
                .WithName(Resource.Organisation_name);

            RuleFor(vm => vm.ContactName)
                .NotEmpty()
                .MaximumLength(OrganisationEntityDefaults.ContactNameMaxLength)
                .WithName(Resource.Contact_Name);

            RuleFor(vm => vm.ContactEmail)
                .NotEmpty()
                .EmailAddress()
                .MaximumLength(OrganisationEntityDefaults.EmailMaxLength)
                .WithName(Resource.Contact_Email);

            RuleFor(vm => vm.ContactPhoneNumber)
                .NotEmpty()
                .MaximumLength(OrganisationEntityDefaults.PhoneMaxLength)
                .WithName(Resource.Contact_Phone_Number);

            RuleFor(vm => vm.Subdomain)
                .NotEmpty()
                .MaximumLength(OrganisationEntityDefaults.SubdomainMaxLength)
                .WithMessage(Resource.Subdomain);
        }
    }

    private async Task ChangeStatusToUnprocessedAsync(GridCommandEventArgs args)
    {
        if (args.Item is OrganisationRequestVm organisationRequestVm)
        {
            NoPayloadApiResponse response =
                await OrganisationsClient
                    .SetOrganisationToUnprocessed(new EditOrganisationUnprocessedRequest(organisationRequestVm.Id), _cts.Token)
                    .ConfigureAwait(true);
            
            NotificationHelper.HandleResultWithAction(response, _ =>
            {
                organisationRequestVm.Status = OrganisationRequestStatus.Unprocessed;
            });
        }
    }
    
    private async Task AddOrganisationAsync(GridCommandEventArgs args)
    {
        if (args.Item is OrganisationRequestVm vm)
        {
            ApiResponse<GetOrganisationRequestResponse> response =
                await OrganisationRequestsClient
                    .GetOrganisationRequestAsync(vm.Id, _cts.Token)
                    .ConfigureAwait(true);

            NotificationHelper.HandleResultWithAction(response, success =>
            {
                _addOrganisationVm = new AddOrganisationVm
                {
                    ContactEmail = success.Result.ContactEmail,
                    OrganisationName = success.Result.OrganisationName,
                    ContactPhoneNumber = success.Result.ContactPhoneNumber,
                    ContactName = success.Result.ContactName,
                    Id = success.Result.Id
                };

                _addWindowVisible = true;
            });
        }
    }

    private async Task OnAddOrganisationValidSubmitAsync()
    {
        AddOrganisationRequest request = new(
            _addOrganisationVm.OrganisationName,
            _addOrganisationVm.ContactName,
            _addOrganisationVm.ContactEmail,
            _addOrganisationVm.ContactPhoneNumber,
            _addOrganisationVm.Subdomain,
            _addOrganisationVm.Id);

        NoPayloadApiResponse response = 
            await OrganisationsClient
                .AddOrganisationAsync(request, _cts.Token)
                .ConfigureAwait(true);
        
        NotificationHelper.HandleNoPayloadResponseWithFunc(response, RefreshAfterAddingOrganisation);
    }

    private void SetupBreadcrumbs()
    {
        BreadcrumbService.AddBreadcrumb(
            new(
                Resource.Organisation_Requests, AdminAppUrlConstants.OrganisationRequests, true));
    }

    public void Dispose()
    {
        _cts.Cancel();
        _cts.Dispose();
    }
}