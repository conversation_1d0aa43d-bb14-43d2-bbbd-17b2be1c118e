using Telerik.DataSource;
using Theia.App.Client.Admin.Features.Industries.Commands.CreateIndustry;
using Theia.App.Client.Admin.Features.Industries.Commands.UpdateIndustry;
using Theia.App.Client.Admin.Features.Industries.Queries.GetIndustries;
using Theia.App.Client.Admin.Features.Industries.Queries.GetIndustryForEdit;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared.Industries.GetIndustries;

namespace Theia.App.Client.Admin.Interfaces.Consumers;

public interface IIndustriesClient
{
    Task<ApiResponse<IndustryForEdit>> GetIndustryAsync(GetIndustryForEditQuery request, CancellationToken ct);
    Task<ApiResponse<GetIndustriesResponse>> GetIndustriesAsync(DataSourceRequest request, CancellationToken ct);
    Task<ApiResponse<CreateIndustryResponse>> CreateIndustryAsync(CreateIndustryCommand request, CancellationToken ct);
    Task<ApiResponse<string>> UpdateIndustryAsync(UpdateIndustryCommand request, CancellationToken ct);
}