using Theia.App.Client.Admin.Features.Identity.Permissions.Queries.GetPermissions;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;

namespace Theia.App.Client.Admin.Interfaces.Consumers;

public interface IPermissionsClient
{
    Task<ApiResponse<PermissionsResponse>> GetPermissionsAsync(GetPermissionsQuery request, CancellationToken ct);
}