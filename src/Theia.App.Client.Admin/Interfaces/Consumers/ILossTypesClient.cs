using Theia.App.Client.Admin.Features.LossTypes.Commands.CreateLossType;
using Theia.App.Client.Admin.Features.LossTypes.Commands.UpdateLossType;
using Theia.App.Client.Admin.Features.LossTypes.Queries.GetLossTypeForEdit;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Shared;
using Theia.App.Shared.Admin.LossTypes.GetLossTypes;

namespace Theia.App.Client.Admin.Interfaces.Consumers;

public interface ILossTypesClient
{
    Task<ApiResponse<LossTypeForEdit>> GetLossTypeAsync(GetLossTypeForEditQuery request, CancellationToken ct);
    Task<ApiResponse<DataEnvelope<LossTypeItem>>> GetLossTypesAsync(GetLossTypesQuery request, CancellationToken ct);
    Task<ApiResponse<CreateLossTypeResponse>> CreateLossTypeAsync(CreateLossTypeCommand request, CancellationToken ct);
    Task<ApiResponse<string>> UpdateLossTypeAsync(UpdateLossTypeCommand request, CancellationToken ct);
}