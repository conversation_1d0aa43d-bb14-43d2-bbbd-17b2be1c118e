using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Insurers.AddInsurer;
using Theia.App.Shared.Admin.Insurers.GetInsurers;
using Theia.App.Shared.Admin.Insurers.UpdateInsurer;

namespace Theia.App.Client.Admin.Consumers;

public interface IInsurersClient
{
    public Task<NoPayloadApiResponse> CreateInsurerAsync(AddInsurerRequest request, CancellationToken ct);
    public Task<ApiResponse<GetInsurersResponse>> GetInsurersAsync(DataSourceRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> UpdateInsurerAsync(UpdateInsurerRequest request, CancellationToken ct);
}

public class InsurersClient : IInsurersClient
{
    private readonly HttpService _httpService;

    public InsurersClient(HttpService httpService)
    {
        _httpService = httpService;
    }

    public Task<NoPayloadApiResponse> CreateInsurerAsync(AddInsurerRequest request, CancellationToken ct)
        => _httpService.PostAsync("admin/insurers", request, ct);

    public Task<ApiResponse<GetInsurersResponse>> GetInsurersAsync(DataSourceRequest request, CancellationToken ct)
        => _httpService.PostAsync<DataSourceRequest, GetInsurersResponse>("admin/insurers/get", request, ct);

    public Task<NoPayloadApiResponse> UpdateInsurerAsync(UpdateInsurerRequest request,
        CancellationToken ct) => _httpService.PutAsync("admin/insurers", request, ct);
}