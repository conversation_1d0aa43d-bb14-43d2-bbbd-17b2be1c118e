using Theia.App.Client.Admin.Features.SecurityControlFramework.ControlFrameworkCategories.Commands.CreateControlFrameworkCategory;
using Theia.App.Client.Admin.Features.SecurityControlFramework.ControlFrameworkCategories.Queries.GetControlFrameworkCategories;
using Theia.App.Client.Admin.Features.SecurityControlFramework.ControlFrameworkCategories.Queries.GetControlFrameworkCategoryForEdit;
using Theia.App.Client.Admin.Interfaces.Consumers;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared.Admin.ControlFrameworksCategories;

namespace Theia.App.Client.Admin.Consumers;

public class ControlFrameworkCategoriesClient : IControlFrameworkCategoriesClient
{
    private readonly HttpService _httpService;

    public ControlFrameworkCategoriesClient(HttpService httpService)
    {
        _httpService = httpService;
    }

    public Task<ApiResponse<CreateControlFrameworkCategoryResponse>> CreateControlFrameworkCategoryAsync(CreateControlFrameworkCategoryCommand request, CancellationToken ct)
         => _httpService.PostAsync<CreateControlFrameworkCategoryCommand, CreateControlFrameworkCategoryResponse>("controlFrameworkCategories/CreateControlFrameworkCategory", request, ct);

    public Task<ApiResponse<ControlFrameworkCategoryForEdit>> GetControlFrameworkCategoryAsync(GetControlFrameworkCategoryForEditQuery request, CancellationToken ct)
        => _httpService.PostAsync<GetControlFrameworkCategoryForEditQuery, ControlFrameworkCategoryForEdit>("controlFrameworkCategories/GetControlFrameworkCategory", request, ct);

    public Task<ApiResponse<ControlFrameworkCategoriesResponse>> GetControlFrameworkCategoriesAsync(GetControlFrameworkCategoriesQuery request, CancellationToken ct)
        => _httpService.PostAsync<GetControlFrameworkCategoriesQuery, ControlFrameworkCategoriesResponse>("controlFrameworkCategories/GetControlFrameworkCategories", request, ct);

    public Task<ApiResponse<string>> UpdateControlFrameworkCategoryAsync(UpdateControlFrameworkCategoryCommand request, CancellationToken ct)
        => _httpService.PutAsync<UpdateControlFrameworkCategoryCommand, string>("controlFrameworkCategories/UpdateControlFrameworkCategory", request, ct);
}