using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Suppliers;

namespace Theia.App.Client.Admin.Consumers;

public class SuppliersClient(
        HttpService httpService)
{
    public Task<ApiResponse<DataEnvelope<SuppliersDto>>> GetSuppliersAsync(DataSourceRequest request, CancellationToken cts)
        => httpService.PostAsync<DataSourceRequest, DataEnvelope<SuppliersDto>>("admin/suppliers", request, cts);
    
    public Task<NoPayloadApiResponse> ResendEmailConfirmationAsync(Guid supplierId, CancellationToken cts)
        => httpService.PostAsync("admin/supplier/resend-confirmation", supplierId, cts); 
}