using OneOf;
using OneOf.Types;
using Telerik.DataSource;
using Theia.App.Client.Common;
using Theia.App.Client.Common.Models;
using Theia.App.Client.Common.Services.Http;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Organisations;
using Theia.App.Shared.Admin.Organisations.AddOrganisation;
using Theia.App.Shared.Admin.Organisations.EditOrganisation;
using Theia.App.Shared.Admin.Organisations.GetAdminOrganisations;
using Theia.App.Shared.Admin.Organisations.GetAvailableBrokingHouses;
using Theia.Http.Services;

namespace Theia.App.Client.Admin.Consumers;

public interface IOrganisationsClient
{
    Task<ApiResponse<GetAdminOrganisationsResponse>> GetAdminOrganisationsAsync(DataSourceRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> AddOrganisationAsync(AddOrganisationRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> DeleteOrganisationAsync(WebSafeGuid id, CancellationToken ct) ;
    Task<NoPayloadApiResponse> EditOrganisationAsync(EditOrganisationRequest request, CancellationToken ct);
    Task<NoPayloadApiResponse> SetOrganisationToUnprocessed(EditOrganisationUnprocessedRequest request,
        CancellationToken ct);
    Task<ApiResponse<GetAvailableBrokingHousesResponse>> GetAvailableBrokingHousesAsync(GetAvailableBrokingHousesRequest request, CancellationToken ct);
    Task<ApiResponse<GetAdminOrganisationsBasicResponse[]>> GetAdminOrganisationsBasicAsync(CancellationToken ct);
}

public class OrganisationsClient : IOrganisationsClient
{
    private readonly HttpService _httpService;

    public OrganisationsClient(HttpService httpService)
    {
        _httpService = httpService;
    }

    public Task<ApiResponse<GetAdminOrganisationsResponse>> GetAdminOrganisationsAsync(DataSourceRequest request, CancellationToken ct)
        => _httpService.PostAsync<DataSourceRequest, GetAdminOrganisationsResponse>("organisations/admin/get", request, ct);

    public Task<NoPayloadApiResponse> AddOrganisationAsync(AddOrganisationRequest request, CancellationToken ct)
        => _httpService.PostAsync("organisations", request, ct);
    
    public Task<NoPayloadApiResponse> DeleteOrganisationAsync(WebSafeGuid id, CancellationToken ct) 
        => _httpService.DeleteAsync($"admin/organisations/{id}", ct);

    public Task<NoPayloadApiResponse> EditOrganisationAsync(EditOrganisationRequest request, CancellationToken ct)
        => _httpService.PutAsync("admin/organisations", request, ct);

    public Task<NoPayloadApiResponse> SetOrganisationToUnprocessed(EditOrganisationUnprocessedRequest request, CancellationToken ct)
        => _httpService.PutAsync("organisations/unprocessed", request, ct);

    public Task<ApiResponse<GetAvailableBrokingHousesResponse>> GetAvailableBrokingHousesAsync(GetAvailableBrokingHousesRequest request, CancellationToken ct)
    {
        string url = $"organisations/availableBrokingHouses?{nameof(request.OrganisationId)}={request.OrganisationId}";
        return _httpService.GetAsync<GetAvailableBrokingHousesResponse>(url, ct);
    }

    public Task<ApiResponse<GetAdminOrganisationsBasicResponse[]>> GetAdminOrganisationsBasicAsync(CancellationToken ct)
        => _httpService.GetAsync<GetAdminOrganisationsBasicResponse[]>("admin/organisations/basic", ct);
}