namespace Theia.App.Client.Admin.Features.SecurityControlFramework.ControlFrameworkCategoryClauses.Commands.CreateControlFrameworkCategoryClause;

public class CreateControlFrameworkCategoryClauseCommandValidator : AbstractValidator<CreateControlFrameworkCategoryClauseCommand>
{
    public CreateControlFrameworkCategoryClauseCommandValidator()
    {
        RuleFor(r => r.Name).Cascade(CascadeMode.Stop)
            .NotEmpty()
            .WithMessage(Resource.Control_Framework_Category_Clause_name_is_required);
        RuleFor(r => r.ControlFrameworkCategoryId).Cascade(CascadeMode.Stop)
            .NotEmpty()
            .WithMessage(Resource.Control_Framework_Category_name_is_required);
    }
}