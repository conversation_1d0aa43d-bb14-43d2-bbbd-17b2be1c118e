using Theia.Http.Services;

namespace Theia.App.Client.Admin.Features.Identity.Users.Commands.UpdateUser;

public class UpdateUserCommand
{
    public string Id { get; set; }
    public string Email { get; set; }
    public string Password { get; set; }
    public string ConfirmPassword { get; set; }
    public string AvatarUri { get; set; }
    public bool IsAvatarAdded { get; set; }
    public string Name { get; set; }
    public string Surname { get; set; }
    public string JobTitle { get; set; }
    public string PhoneNumber { get; set; }
    public bool SetRandomPassword { get; set; }
    public bool MustSendActivationEmail { get; set; }
    public bool IsSuspended { get; set; }

    public IList<Guid> AssignedRoleIds { get; init; }
    public IList<Guid> AttachmentIds { get; set; }
}