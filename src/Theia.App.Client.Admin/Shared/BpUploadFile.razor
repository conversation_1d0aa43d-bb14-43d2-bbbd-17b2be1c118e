@if (!IsHidden)
{
    <TelerikGridLayout RowSpacing="6px" ColumnSpacing="10px" Class="grid-layout">
        <GridLayoutItems>
            <GridLayoutItem>
                <TelerikProgressBar Value="@ProgressValue" Max="1" Class="my-loading">
                    <ProgressBarLabel Visible="false"/>
                </TelerikProgressBar>
                <div class="z-inline">
                    <div class="k-button k-button-solid k-rounded-md k-button-md k-button-solid-secondary file-input-zone k-mr-1">
                        <InputFile OnChange="UploadFiles"/>
                        <TelerikFontIcon Icon="@FontIcon.Upload" ThemeColor="@ThemeConstants.Button.ThemeColor.Primary"></TelerikFontIcon>
                        @ButtonName
                    </div>
                    @if (AllowCancel)
                    {
                        <TelerikButton ButtonType="Telerik.Blazor.ButtonType.Button"
                                       Icon="@FontIcon.Cancel"
                                       OnClick="@CancelFileUpload"
                                       Class="k-mr-1">
                            @Resource.Cancel_Upload
                        </TelerikButton>
                    }
                    @if (AllowRemove)
                    {
                        <TelerikButton ButtonType="Telerik.Blazor.ButtonType.Button"
                                       Icon="@FontIcon.Trash"
                                       OnClick="@HideFileUploadComponent"
                                       Enabled="@(!AllowCancel)"
                                       Class="k-mr-1"
                                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)">
                            @Resource.Remove
                        </TelerikButton>
                    }
                    @if (AllowClear)
                    {
                        <TelerikButton ButtonType="Telerik.Blazor.ButtonType.Button"
                                       Icon="@FontIcon.File"
                                       OnClick="@ClearFile"
                                       ThemeColor="@(ThemeConstants.Button.ThemeColor.Error)">
                            @Resource.Clear
                        </TelerikButton>
                    }
                </div>
                @if (!IsFileInfoBoxHidden)
                {
                    <div class="z-list z-list-padding">
                        <div tabindex="0" class="z-list-item z-list-item-gutters">
                            <div class="z-list-item-icon">
                                <TelerikFontIcon Icon="@FontIcon.PaperclipAlt"/>
                            </div>
                            <div class="z-list-item-text ">
                                <p>
                                    <code>@FileUploadMetaData.Name</code>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="z-list z-list-padding">
                        <div tabindex="0" class="z-list-item z-list-item-gutters">
                            <div class="z-list-item-icon">
                                <TelerikFontIcon Icon="@FontIcon.FileImage"/>
                            </div>
                            <div class="z-list-item-text ">
                                <p>
                                    <code>@FileUploadMetaData.Type</code>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="z-list z-list-padding">
                        <div tabindex="0" class="z-list-item z-list-item-gutters">
                            <div class="z-list-item-icon">
                                <TelerikFontIcon Icon="@FontIcon.Cloud"/>
                            </div>
                            <div class="z-list-item-text ">
                                <p>
                                    <code>@FileUploadMetaData.Size</code>
                                </p>
                            </div>
                        </div>
                    </div>
                }

            </GridLayoutItem>
        </GridLayoutItems>
    </TelerikGridLayout>
}