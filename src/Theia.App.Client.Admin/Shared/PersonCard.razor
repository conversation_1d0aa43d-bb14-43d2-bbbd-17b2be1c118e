<MudCard Elevation="0" Square="true" Class="@Class" Style="@Style">
    <MudCardHeader>
        <CardHeaderAvatar>
            <MudAvatar Image="@AvatarUri"/>
        </CardHeaderAvatar>
        <CardHeaderContent>
            @if (!string.IsNullOrWhiteSpace(FullName))
            {
                <MudText Typo="Typo.body2">@FullName</MudText>
            }
            <MudText Typo="Typo.caption">@JobTitle</MudText>
        </CardHeaderContent>
    </MudCardHeader>
</MudCard>

@code {

    [Parameter]
    public string AvatarUri { get; set; }

    [Parameter]
    public string FullName { get; set; }

    [Parameter]
    public string JobTitle { get; set; }

    [Parameter]
    public string Class { get; set; }

    [Parameter]
    public string Style { get; set; }

}