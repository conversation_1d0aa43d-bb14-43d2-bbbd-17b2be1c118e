using AutoWrapper.Wrappers;
using EntityFrameworkCore.Projectables;
using Theia.Application.Services.Indications;
using Theia.Domain.Entities.Indications;

namespace Theia.Application.Services.QuotaShares;

public class QuotaSharePermissionService(
    IApplicationDbContext dbContext,
    IUserService userService,
    IRoleService roleService,
    ITenantResolverService tenantResolverService,
    IndicationsPermissionService indicationsPermissionService)
{
    public async Task<bool> CanCurrentUserRespondToQuotaShareAsync(Guid quotaShareId, CancellationToken cancellationToken = default)
    {
        if (tenantResolverService.GetTenantType() != TenantType.Insurer)
        {
            return false;
        }

        if (roleService is {IsUserInUnderwriterAdminRole: false, IsUserInUnderwriterRole: false})
        {
            return false;
        }

        Guid insurerTenantId = tenantResolverService.EnsureTenantId();
        string currentUserId = userService.EnsureAuthId();

        return await dbContext.QuotaShareFollowers
            .Where(x =>
                x.QuotaShareId == quotaShareId
                && IsQuotaShareFollowerAccessibleByCurrentUser(x, currentUserId, insurerTenantId, roleService.IsUserInUnderwriterAdminRole)
                && x.CanBeAcceptedOrDeclined)
            .AnyAsync(cancellationToken)
            .ConfigureAwait(false);
    }

    public async Task EnsureQuoteShareFollowerAccessAsync(Guid quotaShareId, CancellationToken ct)
    {
        bool canRespond = await CanCurrentUserRespondToQuotaShareAsync(quotaShareId, ct);
        if (canRespond)
        {
            return;
        }
        
        Guid? leadOptionId = 
            await dbContext.QuotaShares
                .Where(x => x.Id == quotaShareId)
                .Select(x => (Guid?)x.LeadOptionId)
                .FirstOrDefaultAsync(ct)
                .ConfigureAwait(false);

        if (leadOptionId is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        await EnsureWriteAccessAsync(leadOptionId.Value, ct);
    }

    public async Task EnsureWriteAccessAsync(Guid leadOptionId, CancellationToken ct)
    {
        Guid? indicationId = 
            await dbContext.Options
                .Where(x => x.Id == leadOptionId)
                .Select(x => (Guid?)x.IndicationId)
                .SingleOrDefaultAsync(ct)
                .ConfigureAwait(false);
        
        if (indicationId is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        await indicationsPermissionService.CheckWriteAccessOrThrowAsync(indicationId.Value, ct).ConfigureAwait(false);
    }
    
    [Projectable]
    public static bool IsQuotaShareFollowerAccessibleByCurrentUser(QuotaShareFollower follower, string userId, Guid insurerId, bool isUserInUnderwriterAdminRole)
        => isUserInUnderwriterAdminRole
            ? follower.InsurerId == insurerId 
            : follower.Underwriters!.Any(u => u.UnderwriterId == userId);
}
