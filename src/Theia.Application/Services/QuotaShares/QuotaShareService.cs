using AutoWrapper.Wrappers;

namespace Theia.Application.Services.QuotaShares;

public class QuotaShareService(IApplicationDbContext dbContext)
{
    public async Task<decimal> CalculateMaximumAvailableLineSize(Guid quotaShareId, CancellationToken ct)
    {
        var model =
            await dbContext.QuotaShares
                .Where(x => x.Id == quotaShareId)
                .Select(x => new
                {
                    LeadInsurerLine = x.LeadOption!.InsurerLine,
                    FollowersLineSum = 
                        x.Followers!
                            .Where(f => f.Status == QuotaShareFollowerStatus.Following)
                            .Sum(f => f.FinalLineSize),
                    MaximumLineSize = x.LeadOption.LimitOfLiability,
                    LayerLimit = x.LeadOption.Indication.IndicationRequest.Layer.Limit
                })
                .SingleOrDefaultAsync(ct);

        if (model is null)
        {
            throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
        }
        
        decimal quotaShareLineSum = model.LeadInsurerLine + model.FollowersLineSum.GetValueOrDefault();
        decimal amountAvailableInLayer = model.LayerLimit - quotaShareLineSum;
        
        decimal maximumLineSize = Math.Min(model.MaximumLineSize, amountAvailableInLayer);
        
        if (maximumLineSize < 0) 
            maximumLineSize = 0;

        return maximumLineSize;
    }
}