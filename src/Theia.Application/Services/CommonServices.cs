using AutoWrapper.Wrappers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using Theia.App.Shared.Models;
using Theia.Application.Interfaces;
using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Reporting;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Enums;
using Theia.Infrastructure.Common.Extensions;
using Theia.Infrastructure.Common.Models;

namespace Theia.Application.Services;

public class CommonServices(
        IApplicationDbContext dbContext,
        ITenantResolverService tenantResolverService,
        ILogger<CommonServices> logger,
        SubmissionsService submissionsService)
    : ICommonServices
{
    public string RetrieveAuthIdFromHttpContext(HttpContext httpContext)
    {
        string? authId = httpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;
        return string.IsNullOrEmpty(authId) ? string.Empty : authId;
    }

    public async Task<string> RetrieveAuthUserIdFromHttpContextAsync(
        HttpContext context)
    {
        string authId = RetrieveAuthIdFromHttpContext(context);
        ApplicationUser? user = await dbContext.Users
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == authId).ConfigureAwait(false);

        if (user is null) throw new ApiProblemDetailsException(Resource.value_cannot_be_null, HttpStatusCode.BadRequest.ToInt());

        return user.Id;
    }

    public async Task<ControlFrameworkModel[]> RetrieveBasicDashboardDataModelFromSubmissionAsync(Guid? submissionId, CancellationToken ct)
    {
        SubmissionType submissionType = await submissionsService.IsSubmissionWholesaleOrOrganisationAsync(submissionId.Value, ct);
        if (submissionType == SubmissionType.WholesaleBrokerSubmission)
        {
            Guid indicationRequestId =
                await dbContext.WholesaleSubmissions
                    .Where(x => x.Id == submissionId)
                    .Select(x => x.IndicationRequestId)
                    .SingleAsync(ct);
            return await RetrieveBasicIndicationDataFromIndicationAsync(indicationRequestId, ct);
        }
        
        ControlFrameworkModel[] models =
            await dbContext
                .ControlFrameworks
                .Where(cf => cf.Type == ControlFrameworkType.Organisation || cf.Type == ControlFrameworkType.Both)
                .Select(cf => new ControlFrameworkModel
                {
                    ControlFrameworkId = cf.Id,
                    ControlFrameworkName = cf.Name,
                    ControlFrameworkScore = cf.AnalysedControlFrameworks.FirstOrDefault(acf => acf.SubmissionId == submissionId).Score,
                    IsTheiaLensFramework = cf.Code == ControlFrameworkCodes.THEIA
                })
                .ToArrayAsync(ct)
                .ConfigureAwait(true);

        return models.Reverse().ToArray();
    }

    public async Task<ControlFrameworkModel[]> RetrieveBasicIndicationDataFromIndicationAsync(Guid? indicationRequestId, CancellationToken ct)
    {
        ControlFrameworkModel[] models =
            await dbContext
                .ControlFrameworks
                .Where(cf => cf.Type == ControlFrameworkType.Organisation || cf.Type == ControlFrameworkType.Both)
                .Select(cf => new ControlFrameworkModel
                {
                    ControlFrameworkId = cf.Id,
                    ControlFrameworkName = cf.Name,
                    ControlFrameworkScore = cf.AnalysedControlFrameworks.FirstOrDefault(acf => acf.IndicationRequestId == indicationRequestId).Score,
                    IsTheiaLensFramework = cf.Code == ControlFrameworkCodes.THEIA
                })
                .ToArrayAsync(ct)
                .ConfigureAwait(true);

        return models.Reverse().ToArray();
    }

    public async Task<Guid> GetUsersCurrentOrganisationAsync()
    {
        Guid? tenantId = tenantResolverService.GetTenantId();
        if (tenantId is null)
        {
            return Guid.Empty;
        }

        Guid? organisationId = (await dbContext.Organisations
            .SingleOrDefaultAsync(x => x.TenantId == tenantId).ConfigureAwait(false))?.Id;
        if (organisationId is null)
        {
            return Guid.Empty;
        }

        return (Guid)organisationId;
    }

    public async Task<Guid> GetUserIdFromUsernameAsync(string userName)
    {
        ApplicationUser? user = await dbContext.Users.SingleOrDefaultAsync(x => 
                x.Email == userName)
            .ConfigureAwait(false);

        if (user is null)
        {
            return Guid.Empty;
        }

        return Guid.Parse(user.Id);
    }

    public async Task<Guid> GetOrganisationIdSubmissionIsAssignedFor(Guid submissionId)
    {
        Submission? submission = await dbContext.Submissions
            .SingleOrDefaultAsync(x => x.Id == submissionId)
            .ConfigureAwait(false);

        if (submission is null)
        {
            return Guid.Empty;
        }

        return submission.RequestedForOrganisationId;
    }

    public async Task<SubmissionAllApplicationFormDataModel[]> GetAllApplicationFormsAndPotentialAnswersAsync(
        Guid submissionId,
        CancellationToken cancellationToken)
    {
        return await (from saf in dbContext.SubmissionApplicationForms
                join sub in dbContext.Submissions on saf.SubmissionId equals sub.Id
                join afv in dbContext.ApplicationFormVersions on saf.ApplicationFormVersionId equals afv.Id
                join af in dbContext.ApplicationForms on afv.ApplicationFormId equals af.Id
                where saf.SubmissionId == submissionId
                select new SubmissionAllApplicationFormDataModel
                {
                    SubmissionApplicationForm = saf,
                    ApplicationFormVersion = afv,
                    ApplicationForms = af
                })
            .ToArrayAsync(cancellationToken)
            .ConfigureAwait(false);
    }

    public async Task<Submission> GetSubmissionAsync(Guid submissionId)
    {
        Guid organisationId = await GetUsersCurrentOrganisationAsync().ConfigureAwait(false);
        Guid? tenantId = tenantResolverService.GetTenantId();

        if (tenantId is null)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, HttpStatusCode.BadRequest.ToInt());
        }

        Submission? submission = await dbContext.Submissions
            .SingleOrDefaultAsync(x => x.Id == submissionId
                                       && (x.RequestedForOrganisationId == organisationId
                                           || x.TenantRequestedById == tenantId))
            .ConfigureAwait(false);

        if (submission is null)
        {
            throw new ApiProblemDetailsException(Resource.Submission_cannot_be_null,
                HttpStatusCode.NotFound.ToInt());
        }

        return submission;
    }

    public async Task<bool> IsSubmissionReadyToBeSubmittedAsync(Guid submissionId)
    {
        Guid organisationId = await GetUsersCurrentOrganisationAsync().ConfigureAwait(false);
        Guid? tenantId = tenantResolverService.GetTenantId();

        if (tenantId is null)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, HttpStatusCode.BadRequest.ToInt());
        }

        SubmissionApplicationForm[] allForms = await (from sub in dbContext.Submissions
                join saf in dbContext.SubmissionApplicationForms on sub.Id equals saf.SubmissionId
                where sub.Id == submissionId
                      && (sub.RequestedForOrganisationId == organisationId
                          || sub.TenantRequestedById == tenantId)
                select saf)
            .ToArrayAsync().ConfigureAwait(false);

        return allForms.All(x => x.Status is ApplicationFormStatus.Completed or ApplicationFormStatus.Removed);
    }

    public Dictionary<string, object> MapFromTheiaJson(string surveyAnswers)
    {
        Dictionary<string, object> toReturn = new();
        Dictionary<string, ApplicationFormAnswers>? theiaJsonAnswers = JsonConvert
            .DeserializeObject<Dictionary<string, ApplicationFormAnswers>>(surveyAnswers);

        if (theiaJsonAnswers is null)
        {
            if (string.IsNullOrWhiteSpace(surveyAnswers))
            {
                return new();
            }

            throw new ApiProblemDetailsException(Resource.Theia_json_cannot_be_null,
                HttpStatusCode.UnprocessableContent.ToInt());
        }

        Dictionary<string, ApplicationFormAnswers> allAnswers = theiaJsonAnswers
            .ToDictionary(x => x.Key, x => x.Value);

        Dictionary<string, ApplicationFormAnswers> fieldsWithComments = allAnswers
            .Where(x => x.Key.Contains("Comment") && !string.IsNullOrWhiteSpace(x.Value.comment))
            .ToDictionary(x => x.Key, x => x.Value);

        foreach (KeyValuePair<string, ApplicationFormAnswers> answer in allAnswers.Where(x => !x.Key.Contains("Comment")))
        {
            toReturn.Add(answer.Key, answer.Value.answer ?? string.Empty);
        }

        foreach (KeyValuePair<string, ApplicationFormAnswers> answerWithComment in fieldsWithComments)
        {
            toReturn.Add(answerWithComment.Key, answerWithComment.Value.comment ?? string.Empty);
        }

        return toReturn;
    }

    public AnswerType WorkoutAnswerType(object answerValue)
    {
        if (answerValue is JArray)
        {
            return AnswerType.MultiValue;
        }

        return AnswerType.SingleValue;
    }

    public bool IsAnswerCorrect(object answer, string correctAnswer, AnswerType answerType)
    {
        if (answerType == AnswerType.MultiValue)
        {
            return false;
        }

        bool success = bool.TryParse(answer.ToString(), out bool boolAnswer);
        if (success)
        {
            return boolAnswer.ToString().ToLower() == correctAnswer;
        }

        return answer.ToString() == correctAnswer;
    }

    public async Task<DataState?> GenerateAnswerStatesAsync(SubmissionApplicationForm? form, CancellationToken ct)
    {
        DataState toReturn = new();
        
        if (form is null)
        {
            return default;
        }

        AnalysedControlFrameworkCategoryClause[] clauses = await dbContext.AnalysedControlFrameworkCategories
            .Join(dbContext.AnalysedControlFrameworkCategoryClauses,
                acfc => acfc.Id,
                acfcc => acfcc.AnalysedControlFrameworkCategoryId,
                (acfc, acfcc)
                    => new { Acfc = acfc, Acfcc = acfcc })
            .Where(x
                    => x.Acfcc.SubmissionApplicationFormId == form.Id)
            .AsNoTracking()
            .Select(x => x.Acfcc)
            .ToArrayAsync(ct)
            .ConfigureAwait(false);

        toReturn.Satisfactory = toReturn.Satisfactory is null or { Length: 0 }
            ? clauses
                .SelectMany(x => x.SatisfiedByQuestions)
                .DistinctBy(x => x.QuestionName)
                .Select(x => new DataSubState
                {
                    Name = x.QuestionName,
                    Comment = x.QuestionComment,
                    QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                        ? SwapStringValueIfBool(x.Answer)
                        : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                            .Select(y => y.ToString())),
                    AnswerType = x.AnswerType,
                    Text = x.QuestionText
                }).ToArray()
            : toReturn.Satisfactory.Concat(
                clauses
                    .SelectMany(x => x.SatisfiedByQuestions)
                    .DistinctBy(x => x.QuestionName)
                    .Select(x => new DataSubState
                    {
                        Name = x.QuestionName,
                        Comment = x.QuestionComment,
                        QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                            ? SwapStringValueIfBool(x.Answer)
                            : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                                .Select(y => y.ToString())),
                        AnswerType = x.AnswerType,
                        Text = x.QuestionText
                    })
                    .Where(x => toReturn.Satisfactory.All(y =>
                        y.Name != x.Name))
            ).ToArray();

        toReturn.AreasOfImprovement = toReturn.AreasOfImprovement is null or { Length: 0 } 
            ? clauses
                .SelectMany(x => x.DissatisfiedByQuestions)
                .DistinctBy(x => x.QuestionName)
                .Select(x => new DataSubState
                {
                    Name = x.QuestionName,
                    Comment = x.QuestionComment,
                    QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                        ? SwapStringValueIfBool(x.Answer)
                        : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                            .Select(y => y.ToString())),
                    AnswerType = x.AnswerType,
                    Text = x.QuestionText
                }).ToArray()
            : toReturn.AreasOfImprovement.Concat(
                clauses
                    .SelectMany(x => x.DissatisfiedByQuestions)
                    .DistinctBy(x => x.QuestionName)
                    .Select(x => new DataSubState
                    {
                        Name = x.QuestionName,
                        Comment = x.QuestionComment,
                        QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                            ? SwapStringValueIfBool(x.Answer)
                            : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                                .Select(y => y.ToString())),
                        AnswerType = x.AnswerType,
                        Text = x.QuestionText
                    }).Where(x => toReturn.AreasOfImprovement.All(y =>
                        y.Name != x.Name))
            ).ToArray();

        toReturn.Unanswered = toReturn.Unanswered is null or { Length: 0 }
            ? clauses
                .SelectMany(x => x.UnansweredByQuestions)
                .DistinctBy(x => x.QuestionName)
                .Select(x => new DataSubState
                {
                    Name = x.QuestionName,
                    Comment = x.QuestionComment,
                    QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                        ? SwapStringValueIfBool(x.Answer)
                        : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                            .Select(y => y.ToString())),
                    AnswerType = x.AnswerType,
                    Text = x.QuestionText
                }).ToArray()
            : toReturn.Unanswered.Concat(
                clauses
                    .SelectMany(x => x.UnansweredByQuestions)
                    .DistinctBy(x => x.QuestionName)
                    .Select(x => new DataSubState
                    {
                        Name = x.QuestionName,
                        Comment = x.QuestionComment,
                        QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                            ? SwapStringValueIfBool(x.Answer)
                            : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                                .Select(y => y.ToString())),
                        AnswerType = x.AnswerType,
                        Text = x.QuestionText
                    }).Where(x => toReturn.Unanswered.All(y =>
                        y.Name != x.Name))
            ).ToArray();

        toReturn.Informational = toReturn.Informational is null or { Length: 0 }
            ? clauses
                .SelectMany(x => x.InformationalByQuestions)
                .DistinctBy(x => x.QuestionName)
                .Select(x => new DataSubState
                {
                    Name = x.QuestionName,
                    Comment = x.QuestionComment,
                    QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                        ? SwapStringValueIfBool(x.Answer)
                        : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                            .Select(y => y.ToString())),
                    AnswerType = x.AnswerType,
                    Text = x.QuestionText
                }).ToArray()
            : toReturn.Informational.Concat(
                clauses
                    .SelectMany(x => x.InformationalByQuestions)
                    .DistinctBy(x => x.QuestionName)
                    .Select(x => new DataSubState
                    {
                        Name = x.QuestionName,
                        Comment = x.QuestionComment,
                        QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                            ? SwapStringValueIfBool(x.Answer)
                            : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                                .Select(y => y.ToString())),
                        AnswerType = x.AnswerType,
                        Text = x.QuestionText
                    }).Where(x => toReturn.Informational.All(y =>
                        y.Name != x.Name))
            ).ToArray();

        return toReturn;
    }

    public async Task<DataState?> GenerateAnswerStatesAsync(Guid snapshotId, CancellationToken ct)
    {
        DataState toReturn = new();
        
        AnalysedControlFrameworkCategoryClause[] clauses = 
            await dbContext.AnalysedControlFrameworkCategoryClauses
            .AsNoTracking()
            .Where(cat => 
                cat.AnalysedControlFrameworkCategory.AnalysedControlFramework.TheiaAnalysisJobId != null
                && cat.AnalysedControlFrameworkCategory.AnalysedControlFramework.SupplierApplicationFormVersionSnapshotId == snapshotId
                && cat.AnalysedControlFrameworkCategory.AnalysedControlFramework.SupplierApplicationFormVersionSnapshot.TheiaAnalysisJob.Status == JobStatus.Complete
                && cat.SupplierApplicationFormVersionSnapshotId == snapshotId)
            .ToArrayAsync(ct)
            .ConfigureAwait(false);

        toReturn.Satisfactory = toReturn.Satisfactory is null or {Length: 0}
            ? MapClausesToSubState(clauses, x => x.SatisfiedByQuestions)
            : toReturn.Satisfactory.Concat(
                    MapClausesToSubState(clauses,
                        x => x.SatisfiedByQuestions,
                        x => toReturn.Satisfactory.All(y => y.Name != x.Name)))
                .ToArray();

        toReturn.AreasOfImprovement = toReturn.AreasOfImprovement is null or {Length: 0}
            ? MapClausesToSubState(clauses, x => x.DissatisfiedByQuestions)
            : toReturn.Satisfactory.Concat(
                    MapClausesToSubState(clauses,
                        x => x.DissatisfiedByQuestions,
                        x => toReturn.AreasOfImprovement.All(y => y.Name != x.Name)))
                .ToArray();

        toReturn.Unanswered = toReturn.Unanswered is null or {Length: 0}
            ? MapClausesToSubState(clauses, x => x.UnansweredByQuestions)
            : toReturn.Satisfactory.Concat(
                    MapClausesToSubState(clauses,
                        x => x.UnansweredByQuestions,
                        x => toReturn.Unanswered.All(y => y.Name != x.Name)))
                .ToArray();

        toReturn.Informational = toReturn.Informational is null or {Length: 0}
            ? MapClausesToSubState(clauses, x => x.InformationalByQuestions)
            : toReturn.Satisfactory.Concat(
                    MapClausesToSubState(clauses, x => x.InformationalByQuestions))
                .ToArray();

        return toReturn;

        static DataSubState[] MapClausesToSubState(
            AnalysedControlFrameworkCategoryClause[] clauses,
            Func<AnalysedControlFrameworkCategoryClause, IEnumerable<SatisfactoryQuestionModel>> propertySelector,
            Func<DataSubState, bool>? filter = null)
        {
            IEnumerable<DataSubState> subStates =
                clauses
                    .SelectMany(propertySelector)
                    .DistinctBy(x => x.QuestionName)
                    .Select(x => new DataSubState
                    {
                        Name = x.QuestionName,
                        Comment = x.QuestionComment,
                        QuestionAnswer = x.AnswerType == AnswerType.SingleValue
                            ? SwapStringValueIfBool(x.Answer)
                            : string.Join(", ", (JsonConvert.DeserializeObject<string[]>(x.Answer) ?? [])
                                .Select(y => y.ToString())),
                        AnswerType = x.AnswerType,
                        Text = x.QuestionText
                    });

            if (filter is not null)
            {
                subStates = subStates.Where(filter);
            }

            return subStates.ToArray();
        }
    }

    public async Task<TenantType> RetrieveApplicationTypeFromTenant(Guid tenantId) => (await dbContext.Tenants
        .SingleAsync(x => x.Id == tenantId).ConfigureAwait(false)).Type;

    public int CalculateApplicationFormPercentage(string answerSurveyJson, string applicationFormVersionJson)
    {
        ApplicationFormSurveyJsonModel? applicationForm =
            JsonConvert.DeserializeObject<ApplicationFormSurveyJsonModel>(
                applicationFormVersionJson);
        if (applicationForm is null)
        {
            throw new ApiProblemDetailsException(Resource.Application_form_version_data_malformed,
                HttpStatusCode.UnprocessableContent.ToInt());
        }

        ElementsModel[] allQuestions = applicationForm.pages
            .Where(x => x.elements is not null)
            .SelectMany(x => x.elements)
            .ToArray();

        Dictionary<string, object>? answerPairs =
            JsonConvert.DeserializeObject<Dictionary<string, object>>(answerSurveyJson);

        if (answerPairs is null)
        {
            throw new ApiProblemDetailsException(Resource.Data_is_not_processable,
                HttpStatusCode.UnprocessableContent.ToInt());
        }

        int numberOfQuestionsAnswered = allQuestions.Sum(x =>
            answerPairs.Count(y => y.Key == x.name && y.Value is not null));

        decimal percentage = 0;
        if (numberOfQuestionsAnswered > 0 && allQuestions.Any())
        {
            percentage = (decimal)numberOfQuestionsAnswered / allQuestions.Length * 100;
        }

        return (int)percentage;
    }

    public void SortAnswersForSaving(
        Dictionary<string, object> answers,
        Dictionary<string, ApplicationFormAnswers> applicationFormAnswers,
        ApplicationFormSurveyJsonModel surveyModel)
    {
        foreach (KeyValuePair<string, object> answer in answers)
        {
            AnswerType typeOfAnswer = WorkoutAnswerType(answer.Value);
            ChoicesModel[]? questionChoices = surveyModel?
                .pages
                .Where(x => x.elements is not null)
                .SelectMany(x => x.elements
                    .Where(y => y.name == answer.Key))
                .Select(x => x.choices)
                .SingleOrDefault();

            if (questionChoices is null)
            {
                logger.LogWarning(Resource.No_question_choices);
            }

            ChoicesModel[]? arrayOfAnswers = [];
            if (answer.Value is JArray answersAsArray)
            {
                arrayOfAnswers = answersAsArray.Select(x => new ChoicesModel
                {
                    score = questionChoices?.SingleOrDefault(y => y.value == x.ToString())?.score,
                    text = questionChoices?.SingleOrDefault(y => y.value == x.ToString())?.text,
                    value = questionChoices?.SingleOrDefault(y => y.value == x.ToString())?.value
                }).ToArray() is { Length: 0 } ? throw new ApiProblemDetailsException(Resource.No_answer_scores_found, HttpStatusCode.NotFound.ToInt()) : arrayOfAnswers;
            }
            else
            {
                arrayOfAnswers =
                [
                    new()
                    {
                        score = questionChoices?.SingleOrDefault(y => y.value == answer.Value as string)?.score,
                        text = questionChoices?.SingleOrDefault(y => y.value == answer.Value as string)?.text,
                        value = questionChoices?.SingleOrDefault(y => y.value == answer.Value as string)?.value
                    }
                ];
            }

            applicationFormAnswers.Add(answer.Key, new()
            {
                answer = answer.Value,
                AnswerType = typeOfAnswer,
                isCorrect = arrayOfAnswers.Any(x => x.score > 0),
                ArrayOfAnswers = arrayOfAnswers
            });
        }
    }

    private static string SwapStringValueIfBool(string answer)
    {
        if (string.IsNullOrEmpty(answer) 
            || answer == "null")
        {
            return Resource.No_Data;
        }
        
        bool success = bool.TryParse(answer, out bool boolAnswer);
        if (success && boolAnswer)
        {
            return Resource.Yes;
        } 

        if (success && !boolAnswer)
        {
            return Resource.No;
        }

        return answer.Trim('"');
    }
}