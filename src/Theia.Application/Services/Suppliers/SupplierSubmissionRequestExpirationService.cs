using Telerik.DataSource;
using Theia.App.Shared.Vendors;

namespace Theia.Application.Services;

public class SupplierSubmissionRequestExpirationService(TimeProvider timeProvider)
{
    public SubmissionExpirationStatus GetSubmissionStatusFromCompletedDate(DateTimeOffset? completedOnUtc)
    {
        SubmissionExpirationStatus status = SubmissionExpirationStatus.NotApplicable;
        
        if (completedOnUtc is null)
        {
            return status;
        }
        
        DateTime dateNowUtc = timeProvider.GetUtcNow().Date;
        DateTime dayOfCompletion = completedOnUtc.Value.Date;
        
        DateTime yearAfterCompletion = dayOfCompletion.AddYears(1);
        DateTime nineMonthsAfterCompletion = dayOfCompletion.AddMonths(9);

        if (dateNowUtc < nineMonthsAfterCompletion)
        {
            status = SubmissionExpirationStatus.UpToDate;
        }
        else if (nineMonthsAfterCompletion <= dateNowUtc && dateNowUtc < yearAfterCompletion)
        {
            status = SubmissionExpirationStatus.ExpiringSoon;
        } 
        else if (yearAfterCompletion <= dateNowUtc)
        {
            status = SubmissionExpirationStatus.Expired;
        }

        return status;
    }
    
    public IEnumerable<FilterDescriptor> MapFilters(object value, string newMemberName)
    {
        if (!Enum.TryParse(value.ToString(), out SubmissionExpirationStatus status))
            return [];

        DateTimeOffset now = timeProvider.GetUtcNow().Date;

        (FilterOperator Op, DateTimeOffset? Date)[] filters = status switch
        {
            SubmissionExpirationStatus.NotApplicable => [(FilterOperator.IsNull, null)],
            SubmissionExpirationStatus.Expired => [(FilterOperator.IsLessThanOrEqualTo, now.AddYears(-1))],
            SubmissionExpirationStatus.ExpiringSoon =>
                [(FilterOperator.IsGreaterThan, now.AddYears(-1)), (FilterOperator.IsLessThanOrEqualTo, now.AddMonths(-9))],
            SubmissionExpirationStatus.UpToDate => [(FilterOperator.IsGreaterThan, now.AddMonths(-9))],
            _ => []
        };

        return filters.Select(f => new FilterDescriptor(newMemberName, f.Op, f.Date));
    }
}