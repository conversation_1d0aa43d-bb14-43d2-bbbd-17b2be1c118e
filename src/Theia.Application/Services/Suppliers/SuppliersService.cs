using Theia.Domain.Entities.Organisations.Submissions;

namespace Theia.Application.Services.Suppliers;

public class SuppliersService(IApplicationDbContext dbContext)
{
    public IQueryable<SubmissionSupplier> GetFromUnsubmittedSubmissionsQuery(Guid supplierId, Guid organisationId)
    {
        return dbContext.SubmissionSupplier
            .Where(x =>
                x.Submission != null
                && x.Submission.RequestedForOrganisationId == organisationId
                && x.Submission.Status == SubmissionStatus.Unsubmitted
                && x.SupplierId == supplierId);
    }
}