using AutoWrapper.Wrappers;
using Theia.Domain.Entities.Suppliers.ApplicationForms;

namespace Theia.Application.Services;

public class SupplierFormSnapshotPermissionService(
        IApplicationDbContext dbContext,
        IUserService userService,
        ITenantResolverService tenantResolverService,
        IRoleService roleService)
{
    public async Task CheckAccessOrThrowAsync(Guid snapshotId, CancellationToken ct)
    {
        IQueryable<SupplierApplicationFormVersionSnapshot> snapshotsQuery = GetAvailableSnapshotsToDisplay();
        bool hasAccess = await snapshotsQuery.AnyAsync(snapshot => snapshot.Id == snapshotId, ct);
        if (!hasAccess) throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
    }
    
    public IQueryable<SupplierApplicationFormVersionSnapshot> GetAvailableSnapshotsToDisplay()
    {
        Guid? tenantId = tenantResolverService.GetTenantId();
        if (!tenantId.HasValue)
        {
            throw new ApiProblemDetailsException(Resource.The_tenant_was_not_found, StatusCodes.Status403Forbidden);
        }

        string? userId = userService.GetAuthId();
        if (string.IsNullOrWhiteSpace(userId))
        {
            throw new ApiProblemDetailsException(Resource.User_is_invalid, StatusCodes.Status400BadRequest);
        }
        
        TenantType tenantType = tenantResolverService.GetTenantType();
        
        return tenantType switch
        {
            TenantType.Supplier => GetSnapshotsForSupplier(userId),
            TenantType.Insurer => GetSnapshotsForUnderwriters(userId, tenantId.Value, roleService.IsUserInUnderwriterAdminRole),
            TenantType.Organization => GetSnapshotsForOrganisation(tenantId.Value),
            TenantType.BrokingHouse => GetSnapshotsForBrokers(tenantId.Value, userId),
            _ => throw new ApiProblemDetailsException(Resource.Invalid_tenant_Id, StatusCodes.Status400BadRequest)
        };
    }

    private IQueryable<SupplierApplicationFormVersionSnapshot> GetSnapshotsForOrganisation(Guid organisationTenantId)
    {
        return dbContext.Organisations
            .Where(org => org.TenantId == organisationTenantId)
            .SelectMany(org => org.Suppliers)
            .SelectMany(sup => sup.Supplier!.SubmissionRequests!)
            .Where(req => req.Status == SupplierSubmissionRequestStatus.Completed)
            .SelectMany(req => req.Snapshots!)
            .Select(snap => snap.SupplierApplicationFormVersionSnapshot!)
            .Distinct();
    }

    private IQueryable<SupplierApplicationFormVersionSnapshot> GetSnapshotsForBrokers(Guid tenantId, string userId)
    {
        return dbContext.SupplierApplicationFormVersionSnapshots
            .Where(x => 
                x.Submissions.Any(sub => 
                    sub.SubmissionSupplier.Submission.PrimaryBrokingHouseTenantId == tenantId)
                || x.Submissions.Any(sub => 
                    sub.SubmissionSupplier.Submission.Layers
                        .Any(layer => layer.IndicationRequests.Any(ir => ir.Brokers.Any(b => b.UserId == userId)))));
    }

    private IQueryable<SupplierApplicationFormVersionSnapshot> GetSnapshotsForUnderwriters(string userId, Guid tenantId, bool isCurrentUserUnderwriterAdmin)
    {
        return dbContext.SupplierApplicationFormVersionSnapshots
            .Where(x => 
                x.Submissions.Any(sub => 
                    sub.SubmissionSupplier.Submission.Layers
                        .Any(layer =>
                            layer.IndicationRequests.Any(ir => ir.Underwriters.Any(u => u.UserId == userId))
                            || layer.IndicationRequests.Any(ir => ir.Underwriters.Any(u => u.User.UserTenantControls.Any(z => z.TenantId == tenantId) && isCurrentUserUnderwriterAdmin)))));
    }

    private IQueryable<SupplierApplicationFormVersionSnapshot> GetSnapshotsForSupplier(string userId)
    {
        return dbContext.Users
            .Where(u => u.Id == userId && u.Supplier != null)
            .SelectMany(u => u.Supplier.ApplicationFormVersions)
            .SelectMany(u => u.Snapshots)
            .Distinct();
    }
}