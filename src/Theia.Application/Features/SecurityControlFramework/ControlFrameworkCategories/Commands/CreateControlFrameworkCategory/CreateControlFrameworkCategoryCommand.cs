using Theia.Domain.Entities.ControlFrameworks;

namespace Theia.Application.Features.SecurityControlFramework.ControlFrameworkCategories.Commands.
    CreateControlFrameworkCategory;

public class CreateControlFrameworkCategoryCommand
{
    public ControlFrameworkCategory MapToEntity()
    {
        return new ControlFrameworkCategory
        {
            Id = Guid.NewGuid(),
            Name = Name,
            Description = Description,
            ControlFrameworkId = ControlFrameworkId,
            Weighting = Weighting,
            IsActive = IsActive
        };
    }

    public string Name { get; set; }
    public Guid ControlFrameworkId { get; set; }
    public string Description { get; set; }
    public int Weighting { get; set; }
    public bool IsActive { get; set; }
}