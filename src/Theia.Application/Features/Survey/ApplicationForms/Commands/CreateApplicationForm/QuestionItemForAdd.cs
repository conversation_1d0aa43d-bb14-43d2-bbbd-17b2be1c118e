namespace Theia.Application.Features.Survey.ApplicationForms.Commands.CreateApplicationForm;

public class QuestionItemForAdd
{
    public string Name { get; set; }
    public string Type { get; set; }
    public string Html { get; set; }
    public string Title { get; set; }
    public string CorrectAnswer { get; set; }
    public int? Weighting { get; set; }
    public bool ShowOtherItem { get; set; }
    public string VisibleIf { get; set; }
    public string Description { get; set; }
    public bool IsActive { get; set; }
    public string Reference { get; set; }
    public Guid ControlFrameworkCategoryClauseId { get; set; }

    public List<ChoiceItemForAdd> ChoiceItems { get; set; }
}