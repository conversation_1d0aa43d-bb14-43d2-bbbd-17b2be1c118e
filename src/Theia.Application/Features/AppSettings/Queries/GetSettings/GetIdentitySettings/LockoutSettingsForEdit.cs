namespace Theia.Application.Features.AppSettings.Queries.GetSettings.GetIdentitySettings;

public class LockoutSettingsForEdit
{
    public static LockoutSettingsForEdit MapFromEntity(LockoutSettings lockoutSettings)
    {
        return new LockoutSettingsForEdit
        {
            Id = lockoutSettings.Id.ToString(), AllowedForNewUsers = lockoutSettings.AllowedForNewUsers, MaxFailedAccessAttempts = lockoutSettings.MaxFailedAccessAttempts, DefaultLockoutTimeSpan = lockoutSettings.DefaultLockoutTimeSpan
        };
    }

    public string Id { get; set; }
    public bool AllowedForNewUsers { get; set; }
    public int MaxFailedAccessAttempts { get; set; }
    public int DefaultLockoutTimeSpan { get; set; }
}