namespace Theia.Application.Features.Account.Manage.Commands.UpdateUserProfile;

public class UpdateUserProfileCommandValidator : AbstractValidator<UpdateUserProfileCommand>
{
    public UpdateUserProfileCommandValidator()
    {
        RuleFor(v => v.Forename).Cascade(CascadeMode.Stop)
            .NotEmpty()
            .WithMessage(Resource.First_name_is_required);

        RuleFor(v => v.Surname).Cascade(CascadeMode.Stop)
            .NotEmpty()
            .WithMessage(Resource.Surname_is_required);

        RuleFor(v => v.JobTitle).Cascade(CascadeMode.Stop)
            .NotEmpty()
            .WithMessage(Resource.Job_title_is_required);
    }
}