using Theia.Application.Common.Models;
using Theia.Application.Features.Identity.Permissions.Queries.GetPermissions;
using Theia.Application.Features.Identity.Users.Commands.CreateUser;
using Theia.Application.Features.Identity.Users.Commands.DeleteUser;
using Theia.Application.Features.Identity.Users.Commands.GrantOrRevokeUserPermissions;
using Theia.Application.Features.Identity.Users.Commands.UpdateUser;
using Theia.Application.Features.Identity.Users.Queries.GetUserForEdit;
using Theia.Application.Features.Identity.Users.Queries.GetUserPermissions;

namespace Theia.Application.Common.Interfaces.UseCases.Identity;

public interface IUserUseCase
{
    Task<UserForEditResponse> GetUser(GetUserForEditQuery request);

    Task<string> EditUser(UpdateUserCommand request, CancellationToken ct);

    Task<string> DeleteUser(DeleteUserCommand request, CancellationToken ct);

    Task<UserPermissionsResponse> GetUserPermissions(GetUserPermissionsQuery request);

    Task<string> GrantOrRevokeUserPermissions(GrantOrRevokeUserPermissionsCommand request);
}