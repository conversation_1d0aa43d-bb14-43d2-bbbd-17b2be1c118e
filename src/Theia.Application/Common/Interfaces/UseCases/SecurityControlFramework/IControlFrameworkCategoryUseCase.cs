namespace Theia.Application.Common.Interfaces.UseCases.SecurityControlFramework;

public interface IControlFrameworkCategoryUseCase
{
    Task<ControlFrameworkCategoriesResponse> GetControlFrameworkCategories(
        GetControlFrameworkCategoriesQuery request);

    Task<ControlFrameworkCategoryForEditResponse> GetControlFrameworkCategory(
        GetControlFrameworkCategoryForEditQuery request);

    Task<CreateControlFrameworkCategoryResponse> AddControlFrameworkCategory(
        CreateControlFrameworkCategoryCommand request);
}