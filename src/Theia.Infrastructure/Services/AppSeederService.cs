using Newtonsoft.Json;
using Theia.App.Shared.Tenants;
using Theia.Domain.Entities.ControlFrameworks;
using Theia.Domain.Entities.Organisations;
using Theia.Domain.Entities.Organisations.Profile.Shared;
using Theia.Domain.Entities.Suppliers;
using Theia.Domain.Entities.Suppliers.Services;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;
using Theia.Infrastructure.Models;

namespace Theia.Infrastructure.Services;

public class AppSeederService : IAppSeederService
{
    public AppSeederService(ApplicationUserManager userManager,
        IApplicationDbContext dbContext,
        IOptions<IdentityOptions> identityOptions,
        IOptions<AppClientsOptions> appClientsOptions,
        IConfigReaderService configReaderService)
    {
        _userManager = userManager;
        _dbContext = dbContext;
        _identityOptions = identityOptions;
        _appClientsOptions = appClientsOptions;
        _configReaderService = configReaderService;
        DisablePasswordComplexity();
    }

    private readonly ApplicationUserManager _userManager;
    private readonly IApplicationDbContext _dbContext;
    private readonly IOptions<IdentityOptions> _identityOptions;
    private readonly IOptions<AppClientsOptions> _appClientsOptions;
    private readonly IConfigReaderService _configReaderService;

    public async Task SeedSupplierServices()
    {
        if (!await _dbContext.SupplierServices.AnyAsync())
        {
            string[] supplierServices =
            {
                "Application Software Providers", "Bitcoin Mining", "Broadcasting / Streaming", "Consulting", "Custom Software and Systems Integration", "Cyber Security Services", "Data Processing", "Hardware Assembly & Manufacture",
                "Infrastructure as a Service (IaaS)", "Managed Security Service Provider (MSSP)", "Managed Service Provider (MSP)", "Network Transport", "Network/telecoms Hardware", "Networking services", "Other", "Outsourcing/Hosting",
                "Platform as a Service (PaaS)", "Pre-packaged Software (COTS)", "Product or Services Training", "Recruitment", "Security Operations Centre (SOC)", "Software as a Service (SaaS)", "Specialty Programming or Services",
                "Value Added Reseller with no integration work", "Valued Added Reselling and integration work"
            };

            SupplierService[] services = supplierServices.Select(ss => new SupplierService {Name = ss}).ToArray();

            _dbContext.SupplierServices.AddRange(services);

            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SeedVendorTypesAsync()
    {
        SupplierServiceType[] vendorTypesToAdd =
        [
            new() { Type = "IT" },
            new() { Type = "Non-IT" },
            new() { Type = "Business Process Outsourcing (BPO)" }
        ];
        
        if (!_dbContext.SupplierServiceTypes.Any())
        {
            foreach (SupplierServiceType vendorType in vendorTypesToAdd)
            {
                _dbContext.SupplierServiceTypes.Add(vendorType);
            }

            await _dbContext.SaveChangesAsync();
        }
    }

    private void DisablePasswordComplexity()
    {
        _identityOptions.Value.Password.RequireDigit = false;
        _identityOptions.Value.Password.RequireLowercase = false;
        _identityOptions.Value.Password.RequireNonAlphanumeric = false;
        _identityOptions.Value.Password.RequireUppercase = false;
    }


    public async Task SeedRegions()
    {
        if (!_dbContext.Regions.Any())
        {
            Region region1 = new() {Name = "USA", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region2 = new() {Name = "North America excl. USA", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region3 = new() {Name = "South America", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region4 = new() {Name = "Western Europe", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region5 = new() {Name = "Northern Europe", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region6 = new() {Name = "Eastern Europe", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region7 = new() {Name = "Southern Europe", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region8 = new() {Name = "Oceania", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region9 = new() {Name = "Middle East", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region10 = new() {Name = "South-East Asia", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region11 = new() {Name = "South Asia", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region12 = new() {Name = "Central Asia", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region13 = new() {Name = "East Asia", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region14 = new() {Name = "West Asia", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region15 = new() {Name = "East Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region16 = new() {Name = "West Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region17 = new() {Name = "North Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region18 = new() {Name = "Southern Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region19 = new() {Name = "Central Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Region region20 = new() {Name = "Others", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            _dbContext.Regions.Add(region1);
            _dbContext.Regions.Add(region2);
            _dbContext.Regions.Add(region3);
            _dbContext.Regions.Add(region4);
            _dbContext.Regions.Add(region5);
            _dbContext.Regions.Add(region6);
            _dbContext.Regions.Add(region7);
            _dbContext.Regions.Add(region8);
            _dbContext.Regions.Add(region9);
            _dbContext.Regions.Add(region10);
            _dbContext.Regions.Add(region11);
            _dbContext.Regions.Add(region12);
            _dbContext.Regions.Add(region13);
            _dbContext.Regions.Add(region14);
            _dbContext.Regions.Add(region15);
            _dbContext.Regions.Add(region16);
            _dbContext.Regions.Add(region17);
            _dbContext.Regions.Add(region18);
            _dbContext.Regions.Add(region19);
            _dbContext.Regions.Add(region20);
            await _dbContext.SaveChangesAsync();
        }
    }


    public async Task SeedCountries()
    {
        if (!_dbContext.Countries.Any())
        {
            Guid usaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "USA").Id;
            Country country_0 = new() {Name = "United States of America", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = usaGUID};
            
            Guid northAmericaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "North America excl. USA").Id;
            Country country_1 = new() { Name = "Canada", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_2 = new() {Name = "Mexico", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_3 = new() {Name = "Bermuda", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_4 = new() {Name = "Saint Pierre & Miquelon", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_5 = new() {Name = "Saint Barthelemy", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_6 = new() {Name = "Saint Martin", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_7 = new() {Name = "Saint Kitts & Nevis", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_8 = new() {Name = "Saint Lucia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_9 = new() {Name = "Saint Vincent & the Grenadines", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_10 = new() {Name = "Antigua & Barbuda", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_11 = new() {Name = "Bahamas", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_12 = new() {Name = "Barbados", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_13 = new() {Name = "Dominica", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_14 = new() {Name = "Grenada", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_15 = new() {Name = "Jamaica", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_16 = new() {Name = "Trinidad & Tobago", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_17 = new() {Name = "Turks & Caicos Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_18 = new() {Name = "Anguilla", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_19 = new() {Name = "British Virgin Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_20 = new() {Name = "Cayman Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_21 = new() {Name = "Dominican Republic", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_22 = new() {Name = "Haiti", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_23 = new() {Name = "Puerto Rico", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_24 = new() {Name = "US Virgin Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_25 = new() {Name = "Belize", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_26 = new() {Name = "Costa Rica", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_27 = new() {Name = "El Salvador", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_28 = new() {Name = "Guatemala", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_29 = new() {Name = "Honduras", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_30 = new() {Name = "Nicaragua", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_31 = new() {Name = "Panama", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_32 = new() {Name = "Aruba", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_33 = new() {Name = "Curacao", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_34 = new() {Name = "Sint Maarten", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_35 = new() {Name = "Bonaire", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_36 = new() {Name = "Cuba", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_37 = new() {Name = "Guadeloupe", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};
            Country country_38 = new() {Name = "Martinique", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAmericaGUID};

            Guid southAmericaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "South America").Id;
            Country country_39 = new() {Name = "Argentina", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_40 = new() {Name = "Bolivia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_41 = new() {Name = "Brazil", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_42 = new() {Name = "Chile", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_43 = new() {Name = "Colombia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_44 = new() {Name = "Ecuador", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_45 = new() {Name = "Falkland Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_46 = new() {Name = "French Guiana", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_47 = new() {Name = "Guyana", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_48 = new() {Name = "Paraguay", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_49 = new() {Name = "Peru", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_50 = new() {Name = "South Georgia & South Sandwich Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_51 = new() {Name = "Suriname", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_52 = new() {Name = "Uruguay", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};
            Country country_53 = new() {Name = "Venezuela", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAmericaGUID};

            Guid westernEuropeGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Western Europe").Id;
            Country country_54 = new() {Name = "Andorra", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_55 = new() {Name = "Austria", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_56 = new() {Name = "Belgium", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_57 = new() {Name = "France", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_58 = new() {Name = "Germany", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_59 = new() {Name = "Liechtenstein", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_60 = new() {Name = "Luxembourg", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_61 = new() {Name = "Monaco", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_62 = new() {Name = "Netherlands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};
            Country country_63 = new() {Name = "Switzerland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westernEuropeGUID};

            Guid easternEuropeGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Eastern Europe").Id;
            Country country_64 = new() {Name = "Albania", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_65 = new() {Name = "Belarus", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_66 = new() {Name = "Bosnia & Herzegovina", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_67 = new() {Name = "Bulgaria", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_68 = new() {Name = "Croatia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_69 = new() {Name = "Czech Republic", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_70 = new() {Name = "Estonia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_71 = new() {Name = "Hungary", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_72 = new() {Name = "Kosovo", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_73 = new() {Name = "Latvia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_74 = new() {Name = "Lithuania", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_75 = new() {Name = "Macedonia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_76 = new() {Name = "Moldova", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_77 = new() {Name = "Montenegro", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_78 = new() {Name = "Poland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_79 = new() {Name = "Romania", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_80 = new() {Name = "Russia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_81 = new() {Name = "Serbia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_82 = new() {Name = "Slovakia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_83 = new() {Name = "Slovenia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};
            Country country_84 = new() {Name = "Ukraine", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = easternEuropeGUID};

            Guid northernEuropeGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Northern Europe").Id;
            Country country_85 = new() {Name = "Denmark", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_86 = new() {Name = "Estonia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_87 = new() {Name = "Faroe Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_88 = new() {Name = "Finland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_89 = new() {Name = "Iceland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_90 = new() {Name = "Ireland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_91 = new() {Name = "Sweden", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_92 = new() {Name = "United Kingdom", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_93 = new() {Name = "Greenland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_94 = new() {Name = "Norway", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_95 = new() {Name = "Svalbard", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_96 = new() {Name = "Åland Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_97 = new() {Name = "Guernsey", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_98 = new() {Name = "Jersey", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};
            Country country_99 = new() {Name = "Isle of Man", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northernEuropeGUID};

            Guid southernEuropeGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Southern Europe").Id;
            Country country_100 = new() {Name = "Cyprus", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_101 = new() {Name = "Gibraltar", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_102 = new() {Name = "Greece", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_103 = new() {Name = "Italy", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_104 = new() {Name = "Malta", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_105 = new() {Name = "Portugal", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_106 = new() {Name = "San Marino", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_107 = new() {Name = "Slovenia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_108 = new() {Name = "Spain", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};
            Country country_109 = new() {Name = "Vatican City", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernEuropeGUID};

            Guid oceaniaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Oceania").Id;
            Country country_110 = new() {Name = "Australia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_111 = new() {Name = "Fiji", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_112 = new() {Name = "Kiribati", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_113 = new() {Name = "Marshall Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_114 = new() {Name = "Micronesia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_115 = new() {Name = "Nauru", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_116 = new() {Name = "New Zealand", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_117 = new() {Name = "Palau", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_118 = new() {Name = "Papua New Guinea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_119 = new() {Name = "Samoa", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_120 = new() {Name = "Solomon Islands", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_121 = new() {Name = "Tonga", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_122 = new() {Name = "Tuvalu", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};
            Country country_123 = new() {Name = "Vanuatu", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = oceaniaGUID};

            Guid middleEastGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Middle East").Id;
            Country country_124 = new() {Name = "Bahrain", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_125 = new() {Name = "Iran", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_126 = new() {Name = "Iraq", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_127 = new() {Name = "Israel", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_128 = new() {Name = "Jordan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_129 = new() {Name = "Kuwait", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_130 = new() {Name = "Lebanon", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_131 = new() {Name = "Oman", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_132 = new() {Name = "Qatar", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_133 = new() {Name = "Saudi Arabia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_134 = new() {Name = "Syria", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_135 = new() {Name = "United Arab Emirates", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_136 = new() {Name = "Yemen", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};
            Country country_137 = new() {Name = "Palestine", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = middleEastGUID};

            Guid southEastAsiaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "South-East Asia").Id;
            Country country_138 = new() {Name = "Brunei", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_139 = new() {Name = "Cambodia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_140 = new() {Name = "East Timor", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_141 = new() {Name = "Indonesia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_142 = new() {Name = "Laos", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_143 = new() {Name = "Malaysia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_144 = new() {Name = "Myanmar", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_145 = new() {Name = "Philippines", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_146 = new() {Name = "Singapore", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_147 = new() {Name = "Thailand", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};
            Country country_148 = new() {Name = "Vietnam", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southEastAsiaGUID};

            Guid southAsiaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "South Asia").Id;
            Country country_149 = new() {Name = "Afghanistan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_150 = new() {Name = "Bangladesh", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_151 = new() {Name = "Bhutan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_152 = new() {Name = "India", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_153 = new() {Name = "Maldives", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_154 = new() {Name = "Nepal", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_155 = new() {Name = "Pakistan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};
            Country country_156 = new() {Name = "Sri Lanka", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southAsiaGUID};

            Guid centralAsiaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Central Asia").Id;
            Country country_157 = new() {Name = "Kazakhstan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAsiaGUID};
            Country country_158 = new() {Name = "Kyrgyzstan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAsiaGUID};
            Country country_159 = new() {Name = "Tajikistan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAsiaGUID};
            Country country_160 = new() {Name = "Turkmenistan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAsiaGUID};
            Country country_161 = new() {Name = "Uzbekistan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAsiaGUID};

            Guid eastAsiaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "East Asia").Id;
            Country country_162 = new() {Name = "China", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_163 = new() {Name = "Hong Kong", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_164 = new() {Name = "Japan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_165 = new() {Name = "Mongolia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_166 = new() {Name = "North Korea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_167 = new() {Name = "South Korea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};
            Country country_168 = new() {Name = "Taiwan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAsiaGUID};

            Guid westAsiaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "West Asia").Id;
            Country country_169 = new() {Name = "Armenia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAsiaGUID};
            Country country_170 = new() {Name = "Azerbaijan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAsiaGUID};
            Country country_171 = new() {Name = "Georgia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAsiaGUID};

            Guid eastAfricaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "East Africa").Id;
            Country country_172 = new() {Name = "Burundi", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_173 = new() {Name = "Comoros", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_174 = new() {Name = "Djibouti", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_175 = new() {Name = "Eritrea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_176 = new() {Name = "Ethiopia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_177 = new() {Name = "Kenya", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_178 = new() {Name = "Madagascar", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_179 = new() {Name = "Mauritius", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_180 = new() {Name = "Rwanda", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_181 = new() {Name = "Seychelles", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_182 = new() {Name = "Somalia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_183 = new() {Name = "Tanzania", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};
            Country country_184 = new() {Name = "Uganda", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = eastAfricaGUID};

            Guid southernAfricaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Southern Africa").Id;
            Country country_185 = new() {Name = "Angola", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_186 = new() {Name = "Botswana", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_187 = new() {Name = "Lesotho", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_188 = new() {Name = "Malawi", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_189 = new() {Name = "Mozambique", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_190 = new() {Name = "Namibia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_191 = new() {Name = "South Africa", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_192 = new() {Name = "Swaziland", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_193 = new() {Name = "Zambia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};
            Country country_194 = new() {Name = "Zimbabwe", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = southernAfricaGUID};

            Guid centralAfricaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "Central Africa").Id;
            Country country_195 = new() {Name = "Cameroon", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_196 = new() {Name = "Central African Republic", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_197 = new() {Name = "Chad", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_198 = new() {Name = "Congo", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_199 = new() {Name = "Equatorial Guinea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_200 = new() {Name = "Gabon", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};
            Country country_201 = new() {Name = "Sao Tome & Principe", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = centralAfricaGUID};

            Guid westAfricaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "West Africa").Id;
            Country country_202 = new() {Name = "Benin", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_203 = new() {Name = "Burkina Faso", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_204 = new() {Name = "Cape Verde", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_205 = new() {Name = "Cote d'Ivoire", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_206 = new() {Name = "Gambia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_207 = new() {Name = "Ghana", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_208 = new() {Name = "Guinea", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_209 = new() {Name = "Guinea-Bissau", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_210 = new() {Name = "Liberia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_211 = new() {Name = "Mali", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_212 = new() {Name = "Mauritania", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_213 = new() {Name = "Niger", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_214 = new() {Name = "Nigeria", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_215 = new() {Name = "Senegal", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_216 = new() {Name = "Sierra Leone", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};
            Country country_217 = new() {Name = "Togo", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = westAfricaGUID};

            Guid northAfricaGUID = _dbContext.Regions.FirstOrDefault(r => r.Name == "North Africa").Id;
            Country country_218 = new() {Name = "Algeria", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};
            Country country_219 = new() {Name = "Egypt", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};
            Country country_220 = new() {Name = "Libya", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};
            Country country_221 = new() {Name = "Morocco", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};
            Country country_222 = new() {Name = "Sudan", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};
            Country country_223 = new() {Name = "Tunisia", ExposureLevel = ExposureLevel.Medium, IsActive = true, RegionId = northAfricaGUID};

            _dbContext.Countries.Add(country_1);
            _dbContext.Countries.Add(country_2);
            _dbContext.Countries.Add(country_3);
            _dbContext.Countries.Add(country_4);
            _dbContext.Countries.Add(country_5);
            _dbContext.Countries.Add(country_6);
            _dbContext.Countries.Add(country_7);
            _dbContext.Countries.Add(country_8);
            _dbContext.Countries.Add(country_9);
            _dbContext.Countries.Add(country_10);
            _dbContext.Countries.Add(country_11);
            _dbContext.Countries.Add(country_12);
            _dbContext.Countries.Add(country_13);
            _dbContext.Countries.Add(country_14);
            _dbContext.Countries.Add(country_15);
            _dbContext.Countries.Add(country_16);
            _dbContext.Countries.Add(country_17);
            _dbContext.Countries.Add(country_18);
            _dbContext.Countries.Add(country_19);
            _dbContext.Countries.Add(country_20);
            _dbContext.Countries.Add(country_21);
            _dbContext.Countries.Add(country_22);
            _dbContext.Countries.Add(country_23);
            _dbContext.Countries.Add(country_24);
            _dbContext.Countries.Add(country_25);
            _dbContext.Countries.Add(country_26);
            _dbContext.Countries.Add(country_27);
            _dbContext.Countries.Add(country_28);
            _dbContext.Countries.Add(country_29);
            _dbContext.Countries.Add(country_30);
            _dbContext.Countries.Add(country_31);
            _dbContext.Countries.Add(country_32);
            _dbContext.Countries.Add(country_33);
            _dbContext.Countries.Add(country_34);
            _dbContext.Countries.Add(country_35);
            _dbContext.Countries.Add(country_36);
            _dbContext.Countries.Add(country_37);
            _dbContext.Countries.Add(country_38);
            _dbContext.Countries.Add(country_39);
            _dbContext.Countries.Add(country_40);
            _dbContext.Countries.Add(country_41);
            _dbContext.Countries.Add(country_42);
            _dbContext.Countries.Add(country_43);
            _dbContext.Countries.Add(country_44);
            _dbContext.Countries.Add(country_45);
            _dbContext.Countries.Add(country_46);
            _dbContext.Countries.Add(country_47);
            _dbContext.Countries.Add(country_48);
            _dbContext.Countries.Add(country_49);
            _dbContext.Countries.Add(country_50);
            _dbContext.Countries.Add(country_51);
            _dbContext.Countries.Add(country_52);
            _dbContext.Countries.Add(country_53);
            _dbContext.Countries.Add(country_54);
            _dbContext.Countries.Add(country_55);
            _dbContext.Countries.Add(country_56);
            _dbContext.Countries.Add(country_57);
            _dbContext.Countries.Add(country_58);
            _dbContext.Countries.Add(country_59);
            _dbContext.Countries.Add(country_60);
            _dbContext.Countries.Add(country_61);
            _dbContext.Countries.Add(country_62);
            _dbContext.Countries.Add(country_63);
            _dbContext.Countries.Add(country_64);
            _dbContext.Countries.Add(country_65);
            _dbContext.Countries.Add(country_66);
            _dbContext.Countries.Add(country_67);
            _dbContext.Countries.Add(country_68);
            _dbContext.Countries.Add(country_69);
            _dbContext.Countries.Add(country_70);
            _dbContext.Countries.Add(country_71);
            _dbContext.Countries.Add(country_72);
            _dbContext.Countries.Add(country_73);
            _dbContext.Countries.Add(country_74);
            _dbContext.Countries.Add(country_75);
            _dbContext.Countries.Add(country_76);
            _dbContext.Countries.Add(country_77);
            _dbContext.Countries.Add(country_78);
            _dbContext.Countries.Add(country_79);
            _dbContext.Countries.Add(country_80);
            _dbContext.Countries.Add(country_81);
            _dbContext.Countries.Add(country_82);
            _dbContext.Countries.Add(country_83);
            _dbContext.Countries.Add(country_84);
            _dbContext.Countries.Add(country_85);
            _dbContext.Countries.Add(country_86);
            _dbContext.Countries.Add(country_87);
            _dbContext.Countries.Add(country_88);
            _dbContext.Countries.Add(country_89);
            _dbContext.Countries.Add(country_90);
            _dbContext.Countries.Add(country_91);
            _dbContext.Countries.Add(country_92);
            _dbContext.Countries.Add(country_93);
            _dbContext.Countries.Add(country_94);
            _dbContext.Countries.Add(country_95);
            _dbContext.Countries.Add(country_96);
            _dbContext.Countries.Add(country_97);
            _dbContext.Countries.Add(country_98);
            _dbContext.Countries.Add(country_99);
            _dbContext.Countries.Add(country_100);
            _dbContext.Countries.Add(country_101);
            _dbContext.Countries.Add(country_102);
            _dbContext.Countries.Add(country_103);
            _dbContext.Countries.Add(country_104);
            _dbContext.Countries.Add(country_105);
            _dbContext.Countries.Add(country_106);
            _dbContext.Countries.Add(country_107);
            _dbContext.Countries.Add(country_108);
            _dbContext.Countries.Add(country_109);
            _dbContext.Countries.Add(country_110);
            _dbContext.Countries.Add(country_111);
            _dbContext.Countries.Add(country_112);
            _dbContext.Countries.Add(country_113);
            _dbContext.Countries.Add(country_114);
            _dbContext.Countries.Add(country_115);
            _dbContext.Countries.Add(country_116);
            _dbContext.Countries.Add(country_117);
            _dbContext.Countries.Add(country_118);
            _dbContext.Countries.Add(country_119);
            _dbContext.Countries.Add(country_120);
            _dbContext.Countries.Add(country_121);
            _dbContext.Countries.Add(country_122);
            _dbContext.Countries.Add(country_123);
            _dbContext.Countries.Add(country_124);
            _dbContext.Countries.Add(country_125);
            _dbContext.Countries.Add(country_126);
            _dbContext.Countries.Add(country_127);
            _dbContext.Countries.Add(country_128);
            _dbContext.Countries.Add(country_129);
            _dbContext.Countries.Add(country_130);
            _dbContext.Countries.Add(country_131);
            _dbContext.Countries.Add(country_132);
            _dbContext.Countries.Add(country_133);
            _dbContext.Countries.Add(country_134);
            _dbContext.Countries.Add(country_135);
            _dbContext.Countries.Add(country_136);
            _dbContext.Countries.Add(country_137);
            _dbContext.Countries.Add(country_138);
            _dbContext.Countries.Add(country_139);
            _dbContext.Countries.Add(country_140);
            _dbContext.Countries.Add(country_141);
            _dbContext.Countries.Add(country_142);
            _dbContext.Countries.Add(country_143);
            _dbContext.Countries.Add(country_144);
            _dbContext.Countries.Add(country_145);
            _dbContext.Countries.Add(country_146);
            _dbContext.Countries.Add(country_147);
            _dbContext.Countries.Add(country_148);
            _dbContext.Countries.Add(country_149);
            _dbContext.Countries.Add(country_150);
            _dbContext.Countries.Add(country_151);
            _dbContext.Countries.Add(country_152);
            _dbContext.Countries.Add(country_153);
            _dbContext.Countries.Add(country_154);
            _dbContext.Countries.Add(country_155);
            _dbContext.Countries.Add(country_156);
            _dbContext.Countries.Add(country_157);
            _dbContext.Countries.Add(country_158);
            _dbContext.Countries.Add(country_159);
            _dbContext.Countries.Add(country_160);
            _dbContext.Countries.Add(country_161);
            _dbContext.Countries.Add(country_162);
            _dbContext.Countries.Add(country_163);
            _dbContext.Countries.Add(country_164);
            _dbContext.Countries.Add(country_165);
            _dbContext.Countries.Add(country_166);
            _dbContext.Countries.Add(country_167);
            _dbContext.Countries.Add(country_168);
            _dbContext.Countries.Add(country_169);
            _dbContext.Countries.Add(country_170);
            _dbContext.Countries.Add(country_171);
            _dbContext.Countries.Add(country_172);
            _dbContext.Countries.Add(country_173);
            _dbContext.Countries.Add(country_174);
            _dbContext.Countries.Add(country_175);
            _dbContext.Countries.Add(country_176);
            _dbContext.Countries.Add(country_177);
            _dbContext.Countries.Add(country_178);
            _dbContext.Countries.Add(country_179);
            _dbContext.Countries.Add(country_180);
            _dbContext.Countries.Add(country_181);
            _dbContext.Countries.Add(country_182);
            _dbContext.Countries.Add(country_183);
            _dbContext.Countries.Add(country_184);
            _dbContext.Countries.Add(country_185);
            _dbContext.Countries.Add(country_186);
            _dbContext.Countries.Add(country_187);
            _dbContext.Countries.Add(country_188);
            _dbContext.Countries.Add(country_189);
            _dbContext.Countries.Add(country_190);
            _dbContext.Countries.Add(country_191);
            _dbContext.Countries.Add(country_192);
            _dbContext.Countries.Add(country_193);
            _dbContext.Countries.Add(country_194);
            _dbContext.Countries.Add(country_195);
            _dbContext.Countries.Add(country_196);
            _dbContext.Countries.Add(country_197);
            _dbContext.Countries.Add(country_198);
            _dbContext.Countries.Add(country_199);
            _dbContext.Countries.Add(country_200);
            _dbContext.Countries.Add(country_201);
            _dbContext.Countries.Add(country_202);
            _dbContext.Countries.Add(country_203);
            _dbContext.Countries.Add(country_204);
            _dbContext.Countries.Add(country_205);
            _dbContext.Countries.Add(country_206);
            _dbContext.Countries.Add(country_207);
            _dbContext.Countries.Add(country_208);
            _dbContext.Countries.Add(country_209);
            _dbContext.Countries.Add(country_210);
            _dbContext.Countries.Add(country_211);
            _dbContext.Countries.Add(country_212);
            _dbContext.Countries.Add(country_213);
            _dbContext.Countries.Add(country_214);
            _dbContext.Countries.Add(country_215);
            _dbContext.Countries.Add(country_216);
            _dbContext.Countries.Add(country_217);
            _dbContext.Countries.Add(country_218);
            _dbContext.Countries.Add(country_219);
            _dbContext.Countries.Add(country_220);
            _dbContext.Countries.Add(country_221);
            _dbContext.Countries.Add(country_222);
            _dbContext.Countries.Add(country_223);

            await _dbContext.SaveChangesAsync();
        }
    }


    public async Task SeedIndustries()
    {
        if (!_dbContext.Industries.Any())
        {
            Industry industry_1 =
                new() {Name = "Aerospace", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_2 = new() {Name = "Agriculture", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_3 = new() {Name = "Chemical", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_4 = new() {Name = "Computer", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_5 = new() {Name = "Construction", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_6 = new() {Name = "Defense", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_7 =
                new() {Name = "Education", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_8 = new() {Name = "Energy", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_9 = new() {Name = "Entertainment", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_10 = new() {Name = "Financial Services", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_11 = new() {Name = "Health Care", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_12 = new() {Name = "Hospitality", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_13 = new() {Name = "Information", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_14 = new() {Name = "Manufacturing", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_15 =
                new() {Name = "Mass Media", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_16 = new() {Name = "Mining", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_17 = new() {Name = "Retail", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_18 = new() {Name = "Telecommunications", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_19 =
                new() {Name = "Transport", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_20 = new() {Name = "Water", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            Industry industry_21 = new() {Name = "Others", ExposureLevel = ExposureLevel.Medium, IsActive = true};
            _dbContext.Industries.Add(industry_1);
            _dbContext.Industries.Add(industry_2);
            _dbContext.Industries.Add(industry_3);
            _dbContext.Industries.Add(industry_4);
            _dbContext.Industries.Add(industry_5);
            _dbContext.Industries.Add(industry_6);
            _dbContext.Industries.Add(industry_7);
            _dbContext.Industries.Add(industry_8);
            _dbContext.Industries.Add(industry_9);
            _dbContext.Industries.Add(industry_10);
            _dbContext.Industries.Add(industry_11);
            _dbContext.Industries.Add(industry_12);
            _dbContext.Industries.Add(industry_13);
            _dbContext.Industries.Add(industry_14);
            _dbContext.Industries.Add(industry_15);
            _dbContext.Industries.Add(industry_16);
            _dbContext.Industries.Add(industry_17);
            _dbContext.Industries.Add(industry_18);
            _dbContext.Industries.Add(industry_19);
            _dbContext.Industries.Add(industry_20);
            _dbContext.Industries.Add(industry_21);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SeedLossTypes()
    {
        if (!_dbContext.LossTypes.Any())
        {
            LossType lossType_1 = new()
            {
                Name = "Incident Response Costs",
                Description =
                    "This is associated with the cost of an organised approach to address and manage the aftermath of a security breach or cyber attack.",
                IsActive = true
            };
            LossType lossType_2 = new()
            {
                Name = "Data and Software Loss",
                Description =
                    "This is the loss caused by any process or event that results in data being corrupted, deleted and/or made unreadable by a user and/or software or application.",
                IsActive = true
            };
            LossType lossType_3 = new()
            {
                Name = "Cyber Extortion",
                Description =
                    "This is an online form of crime where criminals use threats to pressure victims into handing over money or other goods",
                IsActive = true
            };
            LossType lossType_4 = new()
            {
                Name = "Network Service Failure Liabilities",
                Description =
                    "This is the loss associated with the inability to meet legal responsibilities or obligations that a network service provider has in the event of a failure or disruption of the service",
                IsActive = true
            };
            LossType lossType_5 = new() {Name = "Business Interruption", Description = "This covers the loss of income that a business suffers after a disaster", IsActive = true};
            LossType lossType_6 = new()
            {
                Name = "Contingent Business Interruption",
                Description =
                    "This covers the loss of a primary supplier, partner, or customer affects your ability to do business",
                IsActive = true
            };
            LossType lossType_7 = new()
            {
                Name = "Breach of Privacy Event",
                Description =
                    "This is the loss associated with a breach of security leading to the accidental or unlawful destruction, loss, alteration, unauthorised disclosure of, or access to, personal data",
                IsActive = true
            };
            LossType lossType_8 = new()
            {
                Name = "Regulatory and Defence Coverage",
                Description =
                    "This covers the costs of dealing with state and federal regulatory agencies which oversee data breach laws and regulations",
                IsActive = true
            };
            LossType lossType_9 = new()
            {
                Name = "Technology Errors & Omissions",
                Description =
                    "This covers the costs if your business is blamed by a customer for causing damages due to errors, omissions or negligent acts, related to the service or products provided by your tech business",
                IsActive = true
            };
            LossType lossType_10 = new()
            {
                Name = "Crime/Social Engineering",
                Description =
                    "This is the loss resulting from the use of deception to manipulate individuals into divulging confidential or personal information that may be used for fraudulent purposes",
                IsActive = true
            };
            LossType lossType_11 = new()
            {
                Name = "Hardware Replacement",
                Description =
                    "This means reasonable expenses to replace computer hardware with available property that most closely duplicates the function of the seized or destroyed computer hardware at the time of loss",
                IsActive = true
            };
            LossType lossType_12 = new()
            {
                Name = "Reputational Harm",
                Description =
                    "This is the loss to financial capital, social capital and/or market share resulting from damage to a firm's reputation",
                IsActive = true
            };
            _dbContext.LossTypes.Add(lossType_1);
            _dbContext.LossTypes.Add(lossType_2);
            _dbContext.LossTypes.Add(lossType_3);
            _dbContext.LossTypes.Add(lossType_4);
            _dbContext.LossTypes.Add(lossType_5);
            _dbContext.LossTypes.Add(lossType_6);
            _dbContext.LossTypes.Add(lossType_7);
            _dbContext.LossTypes.Add(lossType_8);
            _dbContext.LossTypes.Add(lossType_9);
            _dbContext.LossTypes.Add(lossType_10);
            _dbContext.LossTypes.Add(lossType_11);
            _dbContext.LossTypes.Add(lossType_12);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SeedControlFrameworks()
    {
        if (!_dbContext.ControlFrameworks.Any())
        {
            ControlFramework isoFramework = new()
            {
                Code = ControlFrameworkCodes.ISO,
                Name = "ISO 27001:2022",
                Description = "ISO/IEC 27001 is an international standard to manage information security.",
                IsActive = true,
                Type = ControlFrameworkType.Both,
                ControlFrameworkCategories =
                [
                    new ControlFrameworkCategory {Name = "Organisation Controls", Weighting = 5, IsActive = true},
                    new ControlFrameworkCategory {Name = "People Controls", Weighting = 5, IsActive = true},
                    new ControlFrameworkCategory {Name = "Physical Controls", Weighting = 5, IsActive = true},
                    new ControlFrameworkCategory {Name = "Technology Controls", Weighting = 5, IsActive = true}
                ]
            };

            ControlFramework cisFramework = new()
            {
                Code = ControlFrameworkCodes.CIS,
                Name = "CIS Controls v8",
                Description = "The CIS Critical Security Controls (CIS Controls) are a prescriptive, prioritized, and simplified set of best practices that you can use to strengthen your cybersecurity posture.",
                IsActive = true,
                Type = ControlFrameworkType.Both,
                ControlFrameworkCategories =
                [
                    new ControlFrameworkCategory {Name = "Inventory and Control of Enterprise Assets", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Inventory and Control of Software Assets", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Data Protection", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Secure Configuration of Enterprise Assets and Software", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Account Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Access Control Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Continuous Vulnerability Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Audit Log Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Email and Web Browser Protections", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Malware Defenses", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Data Recovery", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Network Infrastructure Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Network Monitoring and Defense", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Security Awareness and Skills Training", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Service Provider Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Application Software Security", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Incident Response Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Penetration Testing", Weighting = 5, IsActive = true }
                ]
            };

            ControlFramework nistFramework = new()
            {
                Code = ControlFrameworkCodes.NIST,
                Name = "NIST CSF 2.0",
                Description =
                    "The NIST cybersecurity framework puts forth a set of recommendations and standards that enable organizations to be better prepared in identifying and detecting cyber-attacks, and also provides guidelines on how to respond, prevent, and recover from cyber incidents.",
                IsActive = true,
                Type = ControlFrameworkType.Both,
                ControlFrameworkCategories =
                [
                    new ControlFrameworkCategory { Name = "Govern - Organisational Context", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Govern - Risk Management Strategy", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Govern - Roles, Responsibilities, and Authorities", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Govern - Policy", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Govern - Oversight", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Govern - Cybersecurity Supply Chain Risk Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Identify - Asset Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Identify - Risk Assessment", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Identify - Improvement", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Protect - Identity Management, Authentication, and Access Control", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Protect - Awareness and Training", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Protect - Data Security", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Protect - Platform Security", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Protect - Technology Infrastructure Resilience", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Detect - Continuous Monitoring", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Detect - Adverse Event Analysis", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Respond - Incident Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Respond - Incident Analysis", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Respond - Incident Response Reporting and Communication", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Respond - Incident Mitigation", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Recover - Incident Recovery Plan Execution", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Recover - Incident Recovery Communication", Weighting = 5, IsActive = true }
                ]
            };
            
            ControlFramework theiaFramework = new()
            {
                Code = ControlFrameworkCodes.THEIA,
                Name = "TheiaLens SF 1.0",
                IsActive = true,
                Type = ControlFrameworkType.Both,
                ControlFrameworkCategories = [
                    new ControlFrameworkCategory { Name = "MFA & Access Controls", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Email & Web Security", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Backups", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Privileged Access Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Endpoint Detection & Response", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Patch & Vulnerability Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Incident Response Plans", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Phishing & Awareness Training", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "RDP Mitigation & Hardening", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Logging & Monitoring", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "End-of-Life Management", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Digital Supply Chain Risk", Weighting = 5, IsActive = true }
                ]
            };

            ControlFramework alliantSupplierFramework = new()
            {
                Code = ControlFrameworkCodes.ALLIANT,
                Name = "Alliant SA 1.0",
                Description = "The Alliant Supplier Assurance Framework is a set of best practices that you can use to assess and strengthen the cybersecurity posture of your third party suppliers.",
                IsActive = true,
                Type = ControlFrameworkType.Supplier,
                ControlFrameworkCategories = [
                    new ControlFrameworkCategory { Name = "Miscellaneous", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Endpoint", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Server", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Monitoring", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "IAM", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Network", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Backup", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "IR,BCP & DR", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Email", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Vuln & Change Mgmt", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Penetration Tests", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "End-of-Life Mgmt", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Risk Mgmt", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Operational Technology", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Secure Software Dev", Weighting = 5, IsActive = true },
                    new ControlFrameworkCategory { Name = "Privacy", Weighting = 5, IsActive = true }
                ]
            };

            _dbContext.ControlFrameworks.Add(isoFramework);
            _dbContext.ControlFrameworks.Add(cisFramework);
            _dbContext.ControlFrameworks.Add(nistFramework);
            _dbContext.ControlFrameworks.Add(theiaFramework);
            _dbContext.ControlFrameworks.Add(alliantSupplierFramework);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SeedControlFrameworkCategoryClauses()
    {
        if (!_dbContext.ControlFrameworkCategoryClauses.Any())
        {
            var organisationControlsGuid = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Organisation Controls")!.Id;
            var controlFrameworkCategoryClause1 = new ControlFrameworkCategoryClause { Name = "Policies for information security", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.1", Description = "Information security policy and topic-specific policies shall be defined, approved by management, published, communicated to and acknowledged by relevant personnel and relevant interested parties, and reviewed at planned intervals and if significant changes occur" };
            var controlFrameworkCategoryClause2 = new ControlFrameworkCategoryClause { Name = "Information security roles and responsibilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.2", Description = "Information security roles and responsibilities shall be defined and allocated according to the organization needs." };
            var controlFrameworkCategoryClause3 = new ControlFrameworkCategoryClause { Name = "Segregation of duties", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.3", Description = "Conflicting duties and conflicting areas of responsibility shall be segregated." };
            var controlFrameworkCategoryClause4 = new ControlFrameworkCategoryClause { Name = "Management responsibilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.4", Description = "Management shall require all personnel to apply information security in accordance with the established information security policy, topic-specific policies and procedures of the organization." };
            var controlFrameworkCategoryClause5 = new ControlFrameworkCategoryClause { Name = "Contact with authorities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.5", Description = "The organization shall establish and maintain contact with relevant authorities." };
            var controlFrameworkCategoryClause6 = new ControlFrameworkCategoryClause { Name = "Contact with special interest groups", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.6", Description = "The organization shall establish and maintain contact with special interest groups or other specialist security forums and professional associations." };
            var controlFrameworkCategoryClause7 = new ControlFrameworkCategoryClause { Name = "Threat intelligence", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.7", Description = "Information relating to information security threats shall be collected and analysed to produce threat intelligence." };
            var controlFrameworkCategoryClause8 = new ControlFrameworkCategoryClause { Name = "Information security in project management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.8", Description = "Information security shall be integrated into project management." };
            var controlFrameworkCategoryClause9 = new ControlFrameworkCategoryClause { Name = "Inventory of information and other associated assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.9", Description = "An inventory of information and other associated assets, including owners, shall be developed and maintained." };
            var controlFrameworkCategoryClause10 = new ControlFrameworkCategoryClause { Name = "Acceptable use of information and other associated assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.10", Description = "Rules for the acceptable use and procedures for handling information and other associated assets shall be identified, documented and implemented." };
            var controlFrameworkCategoryClause11 = new ControlFrameworkCategoryClause { Name = "Return of assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.11", Description = "Personnel and other interested parties as appropriate shall return all the organization’s assets in their possession upon change or termination of their employment, contract or agreement." };
            var controlFrameworkCategoryClause12 = new ControlFrameworkCategoryClause { Name = "Classification of information", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.12", Description = "Information shall be classified according to the information security needs of the organization based on confidentiality, integrity, availability and relevant interested party requirements" };
            var controlFrameworkCategoryClause13 = new ControlFrameworkCategoryClause { Name = "Labelling of information", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.13", Description = "An appropriate set of procedures for information labelling shall be developed and implemented in accordance with the information classification scheme adopted by the organization." };
            var controlFrameworkCategoryClause14 = new ControlFrameworkCategoryClause { Name = "Information transfer", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.14", Description = "Information transfer rules, procedures, or agreements shall be in place for all types of transfer facilities within the organization and between the organization and other parties." };
            var controlFrameworkCategoryClause15 = new ControlFrameworkCategoryClause { Name = "Access control", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.15", Description = "Rules to control physical and logical access to information and other associated assets shall be established and implemented based on business and information security requirements." };
            var controlFrameworkCategoryClause16 = new ControlFrameworkCategoryClause { Name = "Identity management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.16", Description = "The full life cycle of identities shall be managed." };
            var controlFrameworkCategoryClause17 = new ControlFrameworkCategoryClause { Name = "Authentication information", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.17", Description = "Allocation and management of authentication information shall be controlled by a management process, including advising personnel on appropriate handling of authentication information." };
            var controlFrameworkCategoryClause18 = new ControlFrameworkCategoryClause { Name = "Access rights", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.18", Description = "Access rights to information and other associated assets shall be provisioned, reviewed, modified and removed in accordance with the organization’s topic-specific policy on and rules for access control." };
            var controlFrameworkCategoryClause19 = new ControlFrameworkCategoryClause { Name = "Information security in supplier relationships", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.19", Description = "Processes and procedures shall be defined and implemented to manage the information security risks associated with the use of supplier’s products or services." };
            var controlFrameworkCategoryClause20 = new ControlFrameworkCategoryClause { Name = "Addressing information security within supplier agreements", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.20", Description = "Relevant information security requirements shall be established and agreed with each supplier based on the type of supplier relationship." };
            var controlFrameworkCategoryClause21 = new ControlFrameworkCategoryClause { Name = "Managing information security in the ICT supply chain", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.21", Description = "Processes and procedures shall be defined and implemented to manage the information security risks associated with the ICT products and services supply chain." };
            var controlFrameworkCategoryClause22 = new ControlFrameworkCategoryClause { Name = "Monitoring, review and change management of supplier services", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.22", Description = "The organization shall regularly monitor, review, evaluate and manage change in supplier information security practices and service delivery." };
            var controlFrameworkCategoryClause23 = new ControlFrameworkCategoryClause { Name = "Information security for use of cloud services", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.23", Description = "Processes for acquisition, use, management and exit from cloud services shall be established in accordance with the organization’s information security requirements." };
            var controlFrameworkCategoryClause24 = new ControlFrameworkCategoryClause { Name = "Information security incident management planning and preparation", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.24", Description = "The organization shall plan and prepare for managing information security incidents by defining, establishing and communicating information security incident management processes, roles and responsibilities." };
            var controlFrameworkCategoryClause25 = new ControlFrameworkCategoryClause { Name = "Assessment and decision on information security events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.25", Description = "The organization shall assess information security events and decide if they are to be categorized as information security incidents." };
            var controlFrameworkCategoryClause26 = new ControlFrameworkCategoryClause { Name = "Response to information security incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.26", Description = "Information security incidents shall be responded to in accordance with the documented procedures." };
            var controlFrameworkCategoryClause27 = new ControlFrameworkCategoryClause { Name = "Learning from information security incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.27", Description = "Knowledge gained from information security incidents shall be used to strengthen and improve the information security controls." };
            var controlFrameworkCategoryClause28 = new ControlFrameworkCategoryClause { Name = "Collection of evidence", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.28", Description = "The organization shall establish and implement procedures for the identification, collection, acquisition and preservation of evidence related to information security events." };
            var controlFrameworkCategoryClause29 = new ControlFrameworkCategoryClause { Name = "Information security during disruption", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.29", Description = "The organization shall plan how to maintain information security at an appropriate level during disruption." };
            var controlFrameworkCategoryClause30 = new ControlFrameworkCategoryClause { Name = "ICT readiness for business continuity", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.30", Description = "ICT readiness shall be planned, implemented, maintained and tested based on business continuity objectives and ICT continuity requirements." };
            var controlFrameworkCategoryClause31 = new ControlFrameworkCategoryClause { Name = "Legal, statutory, regulatory and contractual requirements", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.31", Description = "Legal, statutory, regulatory and contractual requirements relevant to information security and the organization’s approach to meet these requirements shall be identified, documented and kept up to date." };
            var controlFrameworkCategoryClause32 = new ControlFrameworkCategoryClause { Name = "Intellectual property rights", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.32", Description = "The organization shall implement appropriate procedures to protect intellectual property rights." };
            var controlFrameworkCategoryClause33 = new ControlFrameworkCategoryClause { Name = "Protection of records", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.33", Description = "Records shall be protected from loss, destruction, falsification, unauthorized access and unauthorized release." };
            var controlFrameworkCategoryClause34 = new ControlFrameworkCategoryClause { Name = "Privacy and protection of PII", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.34", Description = "The organization shall identify and meet the requirements regarding the preservation of privacy and protection of PII according to applicable laws and regulations and contractual requirements" };
            var controlFrameworkCategoryClause35 = new ControlFrameworkCategoryClause { Name = "Independent review of information security", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.35", Description = "The organization’s approach to managing information security and its implementation including people, processes and technologies shall be reviewed independently at planned intervals, or when significant changes occur." };
            var controlFrameworkCategoryClause36 = new ControlFrameworkCategoryClause { Name = "Compliance with policies, rules and standards for information security", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.36", Description = "Compliance with the organization’s information security policy, topic-specific policies, rules and standards shall be regularly reviewed." };
            var controlFrameworkCategoryClause37 = new ControlFrameworkCategoryClause { Name = "Documented operating procedures", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = organisationControlsGuid, Reference = "5.37", Description = "Operating procedures for information processing facilities shall be documented and made available to personnel who need them." };

            var peopleControlsGuid = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "People Controls")!.Id;
            var controlFrameworkCategoryClause38 = new ControlFrameworkCategoryClause { Name = "Screening", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.1", Description = "Background verification checks on all candidates to become personnel shall be carried out prior to joining the organization and on an ongoing basis taking into consideration applicable laws, regulations and ethics and be proportional to the business requirements, the classification of the information to be accessed and the perceived risks." };
            var controlFrameworkCategoryClause39 = new ControlFrameworkCategoryClause { Name = "Terms and conditions of employment", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.2", Description = "The employment contractual agreements shall state the personnel’s and the organization’s responsibilities for information security." };
            var controlFrameworkCategoryClause40 = new ControlFrameworkCategoryClause { Name = "Information security awareness, education and training", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.3", Description = "Personnel of the organization and relevant interested parties shall receive appropriate information security awareness, education and training and regular updates of the organization's information security policy, topic-specific policies and procedures, as relevant for their job function." };
            var controlFrameworkCategoryClause41 = new ControlFrameworkCategoryClause { Name = "Disciplinary process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.4", Description = "A disciplinary process shall be formalized and communicated to take actions against personnel and other relevant interested parties who have committed an information security policy violation." };
            var controlFrameworkCategoryClause42 = new ControlFrameworkCategoryClause { Name = "Responsibilities after termination or change of employment", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.5", Description = "Information security responsibilities and duties that remain valid after termination or change of employment shall be defined, enforced and communicated to relevant personnel and other interested parties." };
            var controlFrameworkCategoryClause43 = new ControlFrameworkCategoryClause { Name = "Confidentiality or non-disclosure agreements", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.6", Description = "Confidentiality or non-disclosure agreements reflecting the organization’s needs for the protection of information shall be identified, documented, regularly reviewed and signed by personnel and other relevant interested parties." };
            var controlFrameworkCategoryClause44 = new ControlFrameworkCategoryClause { Name = "Remote working", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.7", Description = "Security measures shall be implemented when personnel are working remotely to protect information accessed, processed or stored outside the organization’s premises." };
            var controlFrameworkCategoryClause45 = new ControlFrameworkCategoryClause { Name = "Information security event reporting", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = peopleControlsGuid, Reference = "6.8", Description = "The organization shall provide a mechanism for personnel to report observed or suspected information security events through appropriate channels in a timely manner." };

            var physicalControlsGuid = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Physical Controls")!.Id;
            var controlFrameworkCategoryClause46 = new ControlFrameworkCategoryClause { Name = "Physical security perimeters", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.1", Description = "Security perimeters shall be defined and used to protect areas that contain information and other associated assets." };
            var controlFrameworkCategoryClause47 = new ControlFrameworkCategoryClause { Name = "Physical entry", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.2", Description = "Secure areas shall be protected by appropriate entry controls and access points." };
            var controlFrameworkCategoryClause48 = new ControlFrameworkCategoryClause { Name = "Securing offices, rooms and facilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.3", Description = "Physical security for offices, rooms and facilities shall be designed and implemented." };
            var controlFrameworkCategoryClause49 = new ControlFrameworkCategoryClause { Name = "Physical security monitoring", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.4", Description = "Premises shall be continuously monitored for unauthorized physical access." };
            var controlFrameworkCategoryClause50 = new ControlFrameworkCategoryClause { Name = "Protecting against physical and environmental threats", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.5", Description = "Protection against physical and environmental threats, such as natural disasters and other intentional or unintentional physical threats to infrastructure shall be designed and implemented." };
            var controlFrameworkCategoryClause51 = new ControlFrameworkCategoryClause { Name = "Working in secure areas", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.6", Description = "Security measures for working in secure areas shall be designed and implemented." };
            var controlFrameworkCategoryClause52 = new ControlFrameworkCategoryClause { Name = "Clear desk and clear screen", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.7", Description = "Clear desk rules for papers and removable storage media and clear screen rules for information processing facilities shall be defined and appropriately enforced." };
            var controlFrameworkCategoryClause53 = new ControlFrameworkCategoryClause { Name = "Equipment siting and protection", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.8", Description = "Equipment shall be sited securely and protected." };
            var controlFrameworkCategoryClause54 = new ControlFrameworkCategoryClause { Name = "Security of assets off-premises", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.9", Description = "Off-site assets shall be protected" };
            var controlFrameworkCategoryClause55 = new ControlFrameworkCategoryClause { Name = "Storage media", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.10", Description = "Storage media shall be managed through their life cycle of acquisition, use, transportation and disposal in accordance with the organization’s classification scheme and handling requirements." };
            var controlFrameworkCategoryClause56 = new ControlFrameworkCategoryClause { Name = "Supporting utilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.11", Description = "Information processing facilities shall be protected from power failures and other disruptions caused by failures in supporting utilities." };
            var controlFrameworkCategoryClause57 = new ControlFrameworkCategoryClause { Name = "Cabling security", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.12", Description = "Cables carrying power, data or supporting information services shall be protected from interception, interference or damage." };
            var controlFrameworkCategoryClause58 = new ControlFrameworkCategoryClause { Name = "Equipment maintenance", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.13", Description = "Equipment shall be maintained correctly to ensure availability, integrity and confidentiality of information." };
            var controlFrameworkCategoryClause59 = new ControlFrameworkCategoryClause { Name = "Secure disposal or re-use of equipment", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = physicalControlsGuid, Reference = "7.14", Description = "Items of equipment containing storage media shall be verified to ensure that any sensitive data and licensed software has been removed or securely overwritten prior to disposal or re-use." };

            var technologyControlsGuid = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Technology Controls")!.Id;
            var controlFrameworkCategoryClause60 = new ControlFrameworkCategoryClause { Name = "User endpoint devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.1", Description = "Information stored on, processed by or accessible via user end point devices shall be protected." };
            var controlFrameworkCategoryClause61 = new ControlFrameworkCategoryClause { Name = "Privileged access rights", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.2", Description = "The allocation and use of privileged access rights shall be restricted and managed." };
            var controlFrameworkCategoryClause62 = new ControlFrameworkCategoryClause { Name = "Information access restriction", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.3", Description = "Access to information and other associated assets shall be restricted in accordance with the established topic-specific policy on access control." };
            var controlFrameworkCategoryClause63 = new ControlFrameworkCategoryClause { Name = "Access to source code", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.4", Description = "Read and write access to source code, development tools and software libraries shall be appropriately managed." };
            var controlFrameworkCategoryClause64 = new ControlFrameworkCategoryClause { Name = "Secure authentication", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.5", Description = "Secure authentication technologies and procedures shall be implemented based on information access restrictions and the topic-specific policy on access control." };
            var controlFrameworkCategoryClause65 = new ControlFrameworkCategoryClause { Name = "Capacity management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.6", Description = "The use of resources shall be monitored and adjusted in line with current and expected capacity requirements." };
            var controlFrameworkCategoryClause66 = new ControlFrameworkCategoryClause { Name = "Protection against malware", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.7", Description = "Protection against malware shall be implemented and supported by appropriate user awareness." };
            var controlFrameworkCategoryClause67 = new ControlFrameworkCategoryClause { Name = "Management of technical vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.8", Description = "Information about technical vulnerabilities of information systems in use shall be obtained, the organization’s exposure to such vulnerabilities shall be evaluated and appropriate measures shall be taken." };
            var controlFrameworkCategoryClause68 = new ControlFrameworkCategoryClause { Name = "Configuration management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.9", Description = "Configurations, including security configurations, of hardware, software, services and networks shall be established, documented, implemented, monitored and reviewed." };
            var controlFrameworkCategoryClause69 = new ControlFrameworkCategoryClause { Name = "Information deletion", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.10", Description = "Information stored in information systems, devices or in any other storage media shall be deleted when no longer required." };
            var controlFrameworkCategoryClause70 = new ControlFrameworkCategoryClause { Name = "Data masking", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.11", Description = "Data masking shall be used in accordance with the organization’s topic-specific policy on access control and other related topic-specific policies, and business requirements, taking applicable legislation into consideration." };
            var controlFrameworkCategoryClause71 = new ControlFrameworkCategoryClause { Name = "Data leakage prevention", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.12", Description = "Data leakage prevention measures shall be applied to systems, networks and any other devices that process, store or transmit sensitive information." };
            var controlFrameworkCategoryClause72 = new ControlFrameworkCategoryClause { Name = "Information backup", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.13", Description = "Backup copies of information, software and systems shall be maintained and regularly tested in accordance with the agreed topic-specific policy on backup." };
            var controlFrameworkCategoryClause73 = new ControlFrameworkCategoryClause { Name = "Redundancy of information processing facilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.14", Description = "Information processing facilities shall be implemented with redundancy sufficient to meet availability requirements." };
            var controlFrameworkCategoryClause74 = new ControlFrameworkCategoryClause { Name = "Logging", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.15", Description = "Logs that record activities, exceptions, faults and other relevant events shall be produced, stored, protected and analysed." };
            var controlFrameworkCategoryClause75 = new ControlFrameworkCategoryClause { Name = "Monitoring activities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.16", Description = "Networks, systems and applications shall be monitored for anomalous behaviour and appropriate actions taken to evaluate potential information security incidents." };
            var controlFrameworkCategoryClause76 = new ControlFrameworkCategoryClause { Name = "Clock synchronization", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.17", Description = "The clocks of information processing systems used by the organization shall be synchronized to approved time sources." };
            var controlFrameworkCategoryClause77 = new ControlFrameworkCategoryClause { Name = "Use of privileged utility programs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.18", Description = "The use of utility programs that can be capable of overriding system and application controls shall be restricted and tightly controlled." };
            var controlFrameworkCategoryClause78 = new ControlFrameworkCategoryClause { Name = "Installation of software on operational systems", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.19", Description = "Procedures and measures shall be implemented to securely manage software installation on operational systems." };
            var controlFrameworkCategoryClause79 = new ControlFrameworkCategoryClause { Name = "Networks security", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.20", Description = "Networks and network devices shall be secured, managed and controlled to protect information in systems and applications." };
            var controlFrameworkCategoryClause80 = new ControlFrameworkCategoryClause { Name = "Security of network services", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.21", Description = "Security mechanisms, service levels and service requirements of network services shall be identified, implemented and monitored." };
            var controlFrameworkCategoryClause81 = new ControlFrameworkCategoryClause { Name = "Segregation of networks", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.22", Description = "Groups of information services, users and information systems shall be segregated in the organization’s networks." };
            var controlFrameworkCategoryClause82 = new ControlFrameworkCategoryClause { Name = "Web filtering", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.23", Description = "Access to external websites shall be managed to reduce exposure to malicious content." };
            var controlFrameworkCategoryClause83 = new ControlFrameworkCategoryClause { Name = "Use of cryptography", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.24", Description = "Rules for the effective use of cryptography, including cryptographic key management, shall be defined and implemented." };
            var controlFrameworkCategoryClause84 = new ControlFrameworkCategoryClause { Name = "Secure development life cycle", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.25", Description = "Rules for the secure development of software and systems shall be established and applied." };
            var controlFrameworkCategoryClause85 = new ControlFrameworkCategoryClause { Name = "Application security requirements", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.26", Description = "Information security requirements shall be identified, specified and approved when developing or acquiring applications." };
            var controlFrameworkCategoryClause86 = new ControlFrameworkCategoryClause { Name = "Secure system architecture and engineering principles", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.27", Description = "Principles for engineering secure systems shall be established, documented, maintained and applied to any information system development activities." };
            var controlFrameworkCategoryClause87 = new ControlFrameworkCategoryClause { Name = "Secure coding", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.28", Description = "Secure coding principles shall be applied to software development." };
            var controlFrameworkCategoryClause88 = new ControlFrameworkCategoryClause { Name = "Security testing in development and acceptance", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.29", Description = "Security testing processes shall be defined and implemented in the development life cycle." };
            var controlFrameworkCategoryClause89 = new ControlFrameworkCategoryClause { Name = "Outsourced development", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.30", Description = "The organization shall direct, monitor and review the activities related to outsourced system development." };
            var controlFrameworkCategoryClause90 = new ControlFrameworkCategoryClause { Name = "Separation of development, test and production environments", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.31", Description = "Development, testing and production environments shall be separated and secured." };
            var controlFrameworkCategoryClause91 = new ControlFrameworkCategoryClause { Name = "Change management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.32", Description = "Changes to information processing facilities and information systems shall be subject to change management procedures." };
            var controlFrameworkCategoryClause92 = new ControlFrameworkCategoryClause { Name = "Test information", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.33", Description = "Test information shall be appropriately selected, protected and managed." };
            var controlFrameworkCategoryClause93 = new ControlFrameworkCategoryClause { Name = "Protection of information systems during audit testing", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = technologyControlsGuid, Reference = "8.34", Description = "Audit tests and other assurance activities involving assessment of operational systems shall be planned and agreed between the tester and appropriate management." };

            var cisCatGuid1 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Inventory and Control of Enterprise Assets")!.Id;
            var controlFrameworkCategoryClause94 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain Detailed Enterprise Asset Inventory", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid1, Reference = "1.1", Description = "Establish and maintain an accurate, detailed, and up-to-date inventory of all enterprise assets with the potential to store or process data, to include: end-user devices (including portable and mobile), network devices, non-computing/IoT devices, and servers. Ensure the inventory records the network address (if static), hardware address, machine name, enterprise asset owner, department for each asset, and whether the asset has been approved to connect to the network. For mobile end-user devices, MDM type tools can support this process, where appropriate. This inventory includes assets connected to the infrastructure physically, virtually, remotely, and those within cloud environments. Additionally, it includes assets that are regularly connected to the enterprise’s network infrastructure, even if they are not under control of the enterprise. Review and update the inventory of all enterprise assets bi-annually, or more frequently." };
            var controlFrameworkCategoryClause95 = new ControlFrameworkCategoryClause { Name = "Address Unauthorized Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid1, Reference = "1.2", Description = "Ensure that a process exists to address unauthorized assets on a weekly basis. The enterprise may choose to remove the asset from the network, deny the asset from connecting remotely to the network, or quarantine the asset." };
            var controlFrameworkCategoryClause96 = new ControlFrameworkCategoryClause { Name = "Utilize an Active Discovery Tool", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid1, Reference = "1.3", Description = "Utilize an active discovery tool to identify assets connected to the enterprise’s network. Configure the active discovery tool to execute daily, or more frequently." };
            var controlFrameworkCategoryClause97 = new ControlFrameworkCategoryClause { Name = "Use Dynamic Host Configuration Protocol (DHCP) Logging to Update Enterprise Asset Inventory", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid1, Reference = "1.4", Description = "Use DHCP logging on all DHCP servers or Internet Protocol (IP) address management tools to update the enterprise’s asset inventory. Review and use logs to update the enterprise’s asset inventory weekly, or more frequently." };
            var controlFrameworkCategoryClause98 = new ControlFrameworkCategoryClause { Name = "Use a Passive Asset Discovery Tool", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid1, Reference = "1.5", Description = "Use a passive discovery tool to identify assets connected to the enterprise’s network. Review and use scans to update the enterprise’s asset inventory at least weekly, or more frequently." };

            var cisCatGuid2 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Inventory and Control of Software Assets")!.Id;
            var controlFrameworkCategoryClause99 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Software Inventory", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.1", Description = "Establish and maintain a detailed inventory of all licensed software installed on enterprise assets. The software inventory must document the title, publisher, initial install/use date, and business purpose for each entry; where appropriate, include the Uniform Resource Locator (URL), app store(s), version(s), deployment mechanism, and decommission date. Review and update the software inventory bi-annually, or more frequently." };
            var controlFrameworkCategoryClause100 = new ControlFrameworkCategoryClause { Name = "Ensure Authorized Software is Currently Supported", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.2", Description = "Ensure that only currently supported software is designated as authorized in the software inventory for enterprise assets. If software is unsupported, yet necessary for the fulfillment of the enterprise’s mission, document an exception detailing mitigating controls and residual risk acceptance. For any unsupported software without an exception documentation, designate as unauthorized. Review the software list to verify software support at least monthly, or more frequently." };
            var controlFrameworkCategoryClause101 = new ControlFrameworkCategoryClause { Name = "Address Unauthorized Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.3", Description = "Ensure that unauthorized software is either removed from use on enterprise assets or receives a documented exception. Review monthly, or more frequently." };
            var controlFrameworkCategoryClause102 = new ControlFrameworkCategoryClause { Name = "Utilize Automated Software Inventory Tools", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.4", Description = "Utilize software inventory tools, when possible, throughout the enterprise to automate the discovery and documentation of installed software." };
            var controlFrameworkCategoryClause103 = new ControlFrameworkCategoryClause { Name = "Allowlist Authorized Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.5", Description = "Use technical controls, such as application allowlisting, to ensure that only authorized software can execute or be accessed. Reassess bi-annually, or more frequently." };
            var controlFrameworkCategoryClause104 = new ControlFrameworkCategoryClause { Name = "Allowlist Authorized Libraries", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.6", Description = "Use technical controls to ensure that only authorized software libraries, such as specific .dll, .ocx, .so, etc., files, are allowed to load into a system process. Block unauthorized libraries from loading into a system process. Reassess bi-annually, or more frequently." };
            var controlFrameworkCategoryClause105 = new ControlFrameworkCategoryClause { Name = "Allowlist Authorized Scripts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid2, Reference = "2.7", Description = "Use technical controls, such as digital signatures and version control, to ensure that only authorized scripts, such as specific .ps1, .py, etc., files, are allowed to execute. Block unauthorized scripts from executing. Reassess bi-annually, or more frequently." };

            var cisCatGuid3 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Data Protection")!.Id;
            var	controlFrameworkCategoryClause106 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Data Management Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.1", Description = "Establish and maintain a data management process. In the process, address data sensitivity, data owner, handling of data, data retention limits, and disposal requirements, based on sensitivity and retention standards for the enterprise. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause107 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Data Inventory", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.2", Description = "Establish and maintain a data inventory, based on the enterprise’s data management process. Inventory sensitive data, at a minimum. Review and update inventory annually, at a minimum, with a priority on sensitive data."};
            var	controlFrameworkCategoryClause108 = new ControlFrameworkCategoryClause { Name = "Configure Data Access Control Lists", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.3", Description = "Configure data access control lists based on a user’s need to know. Apply data access control lists, also known as access permissions, to local and remote file systems, databases, and applications."};
            var	controlFrameworkCategoryClause109 = new ControlFrameworkCategoryClause { Name = "Enforce Data Retention", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.4", Description = "Retain data according to the enterprise’s data management process. Data retention must include both minimum and maximum timelines."};
            var	controlFrameworkCategoryClause110 = new ControlFrameworkCategoryClause { Name = "Securely Dispose of Data", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.5", Description = "Securely dispose of data as outlined in the enterprise’s data management process. Ensure the disposal process and method are commensurate with the data sensitivity."};
            var	controlFrameworkCategoryClause111 = new ControlFrameworkCategoryClause { Name = "Encrypt Data on End-User Devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.6", Description = "Encrypt data on end-user devices containing sensitive data. Example implementations can include: Windows BitLocker®, Apple FileVault®, Linux® dm-crypt."};
            var	controlFrameworkCategoryClause112 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Data Classification Scheme", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.7", Description = "Establish and maintain an overall data classification scheme for the enterprise. Enterprises may use labels, such as “Sensitive,” “Confidential,” and “Public,” and classify their data according to those labels. Review and update the classification scheme annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause113 = new ControlFrameworkCategoryClause { Name = "Document Data Flows", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.8", Description = "Document data flows. Data flow documentation includes service provider data flows and should be based on the enterprise’s data management process. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause114 = new ControlFrameworkCategoryClause { Name = "Encrypt Data on Removable Media", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.9", Description = "Encrypt data on removable media."};
            var	controlFrameworkCategoryClause115 = new ControlFrameworkCategoryClause { Name = "Encrypt Sensitive Data in Transit", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.10", Description = "Encrypt sensitive data in transit. Example implementations can include: Transport Layer Security (TLS) and Open Secure Shell (OpenSSH)."};
            var	controlFrameworkCategoryClause116 = new ControlFrameworkCategoryClause { Name = "Encrypt Sensitive Data At Rest", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.11", Description = "Encrypt sensitive data at rest on servers, applications, and databases containing sensitive data. Storage-layer encryption, also known as server-side encryption, meets the minimum requirement of this Safeguard. Additional encryption methods may include application-layer encryption, also known as client-side encryption, where access to the data storage device(s) does not permit access to the plain-text data."};
            var	controlFrameworkCategoryClause117 = new ControlFrameworkCategoryClause { Name = "Segment Data Processing and Storage Based on Sensitivity", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.12", Description = "Segment data processing and storage based on the sensitivity of the data. Do not process sensitive data on enterprise assets intended for lower sensitivity data."};
            var	controlFrameworkCategoryClause118 = new ControlFrameworkCategoryClause { Name = "Deploy a Data Loss Prevention Solution", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.13", Description = "Implement an automated tool, such as a host-based Data Loss Prevention (DLP) tool to identify all sensitive data stored, processed, or transmitted through enterprise assets, including those located onsite or at a remote service provider, and update the enterprise's sensitive data inventory."};
            var	controlFrameworkCategoryClause119 = new ControlFrameworkCategoryClause { Name = "Log Sensitive Data Access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid3, Reference = "3.14", Description = "Log sensitive data access, including modification and disposal."};

            var cisCatGuid4 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Secure Configuration of Enterprise Assets and Software")!.Id;
            var	controlFrameworkCategoryClause120 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Secure Configuration Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.1", Description = "Establish and maintain a secure configuration process for enterprise assets (end-user devices, including portable and mobile, non-computing/IoT devices, and servers) and software (operating systems and applications). Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause121 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Secure Configuration Process for Network Infrastructure", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.2", Description = "Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause122 = new ControlFrameworkCategoryClause { Name = "Configure Automatic Session Locking on Enterprise Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.3", Description = "Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes."};
            var	controlFrameworkCategoryClause123 = new ControlFrameworkCategoryClause { Name = "Implement and Manage a Firewall on Servers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.4", Description = "Implement and manage a firewall on servers, where supported. Example implementations include a virtual firewall, operating system firewall, or a third-party firewall agent."};
            var	controlFrameworkCategoryClause124 = new ControlFrameworkCategoryClause { Name = "Implement and Manage a Firewall on End-User Devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.5", Description = "Implement and manage a host-based firewall or port-filtering tool on end-user devices, with a default-deny rule that drops all traffic except those services and ports that are explicitly allowed."};
            var	controlFrameworkCategoryClause125 = new ControlFrameworkCategoryClause { Name = "Securely Manage Enterprise Assets and Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.6", Description = "Securely manage enterprise assets and software. Example implementations include managing configuration through version-controlled-infrastructure-as-code and accessing administrative interfaces over secure network protocols, such as Secure Shell (SSH) and Hypertext Transfer Protocol Secure (HTTPS). Do not use insecure management protocols, such as Telnet (Teletype Network) and HTTP, unless operationally essential."};
            var	controlFrameworkCategoryClause126 = new ControlFrameworkCategoryClause { Name = "Manage Default Accounts on Enterprise Assets and Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.7", Description = "Manage default accounts on enterprise assets and software, such as root, administrator, and other pre-configured vendor accounts. Example implementations can include: disabling default accounts or making them unusable."};
            var	controlFrameworkCategoryClause127 = new ControlFrameworkCategoryClause { Name = "Uninstall or Disable Unnecessary Services on Enterprise Assets and Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.8", Description = "Uninstall or disable unnecessary services on enterprise assets and software, such as an unused file sharing service, web application module, or service function."};
            var	controlFrameworkCategoryClause128 = new ControlFrameworkCategoryClause { Name = "Configure Trusted DNS Servers on Enterprise Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.9", Description = "Configure trusted DNS servers on enterprise assets. Example implementations include: configuring assets to use enterprise-controlled DNS servers and/or reputable externally accessible DNS servers."};
            var	controlFrameworkCategoryClause129 = new ControlFrameworkCategoryClause { Name = "Enforce Automatic Device Lockout on Portable End-User Devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.10", Description = "Enforce automatic device lockout following a predetermined threshold of local failed authentication attempts on portable end-user devices, where supported. For laptops, do not allow more than 20 failed authentication attempts; for tablets and smartphones, no more than 10 failed authentication attempts. Example implementations include Microsoft® InTune Device Lock and Apple® Configuration Profile maxFailedAttempts."};
            var	controlFrameworkCategoryClause130 = new ControlFrameworkCategoryClause { Name = "Enforce Remote Wipe Capability on Portable End-User Devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.11", Description = "Remotely wipe enterprise data from enterprise-owned portable end-user devices when deemed appropriate such as lost or stolen devices, or when an individual no longer supports the enterprise."};
            var	controlFrameworkCategoryClause131 = new ControlFrameworkCategoryClause { Name = "Separate Enterprise Workspaces on Mobile End-User Devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid4, Reference = "4.12", Description = "Ensure separate enterprise workspaces are used on mobile end-user devices, where supported. Example implementations include using an Apple® Configuration Profile or Android™ Work Profile to separate enterprise applications and data from personal applications and data."};

            var cisCatGuid5 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Account Management")!.Id;
            var	controlFrameworkCategoryClause132 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Inventory of Accounts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.1", Description = "Establish and maintain an inventory of all accounts managed in the enterprise. The inventory must include both user and administrator accounts. The inventory, at a minimum, should contain the person’s name, username, start/stop dates, and department. Validate that all active accounts are authorized, on a recurring schedule at a minimum quarterly, or more frequently."};
            var	controlFrameworkCategoryClause133 = new ControlFrameworkCategoryClause { Name = "Use Unique Passwords", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.2", Description = "Use unique passwords for all enterprise assets. Best practice implementation includes, at a minimum, an 8-character password for accounts using MFA and a 14-character password for accounts not using MFA."};
            var	controlFrameworkCategoryClause134 = new ControlFrameworkCategoryClause { Name = "Disable Dormant Accounts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.3", Description = "Delete or disable any dormant accounts after a period of 45 days of inactivity, where supported."};
            var	controlFrameworkCategoryClause135 = new ControlFrameworkCategoryClause { Name = "Restrict Administrator Privileges to Dedicated Administrator Accounts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.4", Description = "Restrict administrator privileges to dedicated administrator accounts on enterprise assets. Conduct general computing activities, such as internet browsing, email, and productivity suite use, from the user’s primary, non-privileged account."};
            var	controlFrameworkCategoryClause136 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Inventory of Service Accounts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.5", Description = "Establish and maintain an inventory of service accounts. The inventory, at a minimum, must contain department owner, review date, and purpose. Perform service account reviews to validate that all active accounts are authorized, on a recurring schedule at a minimum quarterly, or more frequently."};
            var	controlFrameworkCategoryClause137 = new ControlFrameworkCategoryClause { Name = "Centralize Account Management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid5, Reference = "5.6", Description = "Centralize account management through a directory or identity service."};

            var cisCatGuid6 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Access Control Management")!.Id;
            var	controlFrameworkCategoryClause138 = new ControlFrameworkCategoryClause { Name = "Establish an Access Granting Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.1", Description = "Establish and follow a process, preferably automated, for granting access to enterprise assets upon new hire, rights grant, or role change of a user."};
            var	controlFrameworkCategoryClause139 = new ControlFrameworkCategoryClause { Name = "Establish an Access Revoking Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.2", Description = "Establish and follow a process, preferably automated, for revoking access to enterprise assets, through disabling accounts immediately upon termination, rights revocation, or role change of a user. Disabling accounts, instead of deleting accounts, may be necessary to preserve audit trails."};
            var	controlFrameworkCategoryClause140 = new ControlFrameworkCategoryClause { Name = "Require MFA for Externally-Exposed Applications", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.3", Description = "Require all externally-exposed enterprise or third-party applications to enforce MFA, where supported. Enforcing MFA through a directory service or SSO provider is a satisfactory implementation of this Safeguard."};
            var	controlFrameworkCategoryClause141 = new ControlFrameworkCategoryClause { Name = "Require MFA for Remote Network Access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.4", Description = "Require MFA for remote network access."};
            var	controlFrameworkCategoryClause142 = new ControlFrameworkCategoryClause { Name = "Require MFA for Administrative Access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.5", Description = "Require MFA for all administrative access accounts, where supported, on all enterprise assets, whether managed on-site or through a third-party provider."};
            var	controlFrameworkCategoryClause143 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Inventory of Authentication and Authorization Systems", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.6", Description = "Establish and maintain an inventory of the enterprise’s authentication and authorization systems, including those hosted on-site or at a remote service provider. Review and update the inventory, at a minimum, annually, or more frequently."};
            var	controlFrameworkCategoryClause144 = new ControlFrameworkCategoryClause { Name = "Centralize Access Control", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.7", Description = "Centralize access control for all enterprise assets through a directory service or SSO provider, where supported."};
            var	controlFrameworkCategoryClause145 = new ControlFrameworkCategoryClause { Name = "Define and Maintain Role-Based Access Control", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid6, Reference = "6.8", Description = "Define and maintain role-based access control, through determining and documenting the access rights necessary for each role within the enterprise to successfully carry out its assigned duties. Perform access control reviews of enterprise assets to validate that all privileges are authorized, on a recurring schedule at a minimum annually, or more frequently."};

            var cisCatGuid7 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Continuous Vulnerability Management")!.Id;
            var	controlFrameworkCategoryClause146 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Vulnerability Management Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.1", Description = "Establish and maintain a documented vulnerability management process for enterprise assets. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause147 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Remediation Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.2", Description = "Establish and maintain a risk-based remediation strategy documented in a remediation process, with monthly, or more frequent, reviews."};
            var	controlFrameworkCategoryClause148 = new ControlFrameworkCategoryClause { Name = "Perform Automated Operating System Patch Management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.3", Description = "Perform operating system updates on enterprise assets through automated patch management on a monthly, or more frequent, basis."};
            var	controlFrameworkCategoryClause149 = new ControlFrameworkCategoryClause { Name = "Perform Automated Application Patch Management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.4", Description = "Perform application updates on enterprise assets through automated patch management on a monthly, or more frequent, basis."};
            var	controlFrameworkCategoryClause150 = new ControlFrameworkCategoryClause { Name = "Perform Automated Vulnerability Scans of Internal Enterprise Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.5", Description = "Perform automated vulnerability scans of internal enterprise assets on a quarterly, or more frequent, basis. Conduct both authenticated and unauthenticated scans, using a SCAP-compliant vulnerability scanning tool."};
            var	controlFrameworkCategoryClause151 = new ControlFrameworkCategoryClause { Name = "Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.6", Description = "Perform automated vulnerability scans of externally-exposed enterprise assets using a SCAP-compliant vulnerability scanning tool. Perform scans on a monthly, or more frequent, basis. "};
            var	controlFrameworkCategoryClause152 = new ControlFrameworkCategoryClause { Name = "Remediate Detected Vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid7, Reference = "7.7", Description = "Remediate detected vulnerabilities in software through processes and tooling on a monthly, or more frequent, basis, based on the remediation process."};

            var cisCatGuid8 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Audit Log Management")!.Id;
            var	controlFrameworkCategoryClause153 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Audit Log Management Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.1", Description = "Establish and maintain an audit log management process that defines the enterprise’s logging requirements. At a minimum, address the collection, review, and retention of audit logs for enterprise assets. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause154 = new ControlFrameworkCategoryClause { Name = "Collect Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.2", Description = "Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets."};
            var	controlFrameworkCategoryClause155 = new ControlFrameworkCategoryClause { Name = "Ensure Adequate Audit Log Storage", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.3", Description = "Ensure that logging destinations maintain adequate storage to comply with the enterprise’s audit log management process."};
            var	controlFrameworkCategoryClause156 = new ControlFrameworkCategoryClause { Name = "Standardize Time Synchronization", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.4", Description = "Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported."};
            var	controlFrameworkCategoryClause157 = new ControlFrameworkCategoryClause { Name = "Collect Detailed Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.5", Description = "Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation"};
            var	controlFrameworkCategoryClause158 = new ControlFrameworkCategoryClause { Name = "Collect DNS Query Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.6", Description = "Collect DNS query audit logs on enterprise assets, where appropriate and supported."};
            var	controlFrameworkCategoryClause159 = new ControlFrameworkCategoryClause { Name = "Collect URL Request Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.7", Description = "Collect URL request audit logs on enterprise assets, where appropriate and supported."};
            var	controlFrameworkCategoryClause160 = new ControlFrameworkCategoryClause { Name = "Collect Command-Line Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.8", Description = "Collect command-line audit logs. Example implementations include collecting audit logs from PowerShell®, BASH™, and remote administrative terminals."};
            var	controlFrameworkCategoryClause161 = new ControlFrameworkCategoryClause { Name = "Centralize Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.9", Description = "Centralize, to the extent possible, audit log collection and retention across enterprise assets."};
            var	controlFrameworkCategoryClause162 = new ControlFrameworkCategoryClause { Name = "Retain Audit Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.10", Description = "Retain audit logs across enterprise assets for a minimum of 90 days."};
            var	controlFrameworkCategoryClause163 = new ControlFrameworkCategoryClause { Name = "Conduct Audit Log Reviews", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.11", Description = "Conduct reviews of audit logs to detect anomalies or abnormal events that could indicate a potential threat. Conduct reviews on a weekly, or more frequent, basis."};
            var	controlFrameworkCategoryClause164 = new ControlFrameworkCategoryClause { Name = "Collect Service Provider Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid8, Reference = "8.12", Description = "Collect service provider logs, where supported. Example implementations include collecting authentication and authorization events, data creation and disposal events, and user management events."};

            var cisCatGuid9 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Email and Web Browser Protections")!.Id;
            var	controlFrameworkCategoryClause165 = new ControlFrameworkCategoryClause { Name = "Ensure Use of Only Fully Supported Browsers and Email Clients", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.1", Description = "Ensure only fully supported browsers and email clients are allowed to execute in the enterprise, only using the latest version of browsers and email clients provided through the vendor."};
            var	controlFrameworkCategoryClause166 = new ControlFrameworkCategoryClause { Name = "Use DNS Filtering Services", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.2", Description = "Use DNS filtering services on all enterprise assets to block access to known malicious domains."};
            var	controlFrameworkCategoryClause167 = new ControlFrameworkCategoryClause { Name = "Maintain and Enforce Network-Based URL Filters", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.3", Description = "Enforce and update network-based URL filters to limit an enterprise asset from connecting to potentially malicious or unapproved websites. Example implementations include category-based filtering, reputation-based filtering, or through the use of block lists. Enforce filters for all enterprise assets."};
            var	controlFrameworkCategoryClause168 = new ControlFrameworkCategoryClause { Name = "Restrict Unnecessary or Unauthorized Browser and Email Client Extensions", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.4", Description = "Restrict, either through uninstalling or disabling, any unauthorized or unnecessary browser or email client plugins, extensions, and add-on applications."};
            var	controlFrameworkCategoryClause169 = new ControlFrameworkCategoryClause { Name = "Implement DMARC", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.5", Description = "	To lower the chance of spoofed or modified emails from valid domains, implement DMARC policy and verification, starting with implementing the Sender Policy Framework (SPF) and the DomainKeys Identified Mail (DKIM) standards."};
            var	controlFrameworkCategoryClause170 = new ControlFrameworkCategoryClause { Name = "Block Unnecessary File Types", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.6", Description = "Block unnecessary file types attempting to enter the enterprise’s email gateway."};
            var	controlFrameworkCategoryClause171 = new ControlFrameworkCategoryClause { Name = "Deploy and Maintain Email Server Anti-Malware Protections", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid9, Reference = "9.7", Description = "Deploy and maintain email server anti-malware protections, such as attachment scanning and/or sandboxing."};

            var cisCatGuid10 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Malware Defenses")!.Id;
            var	controlFrameworkCategoryClause172 = new ControlFrameworkCategoryClause { Name = "Deploy and Maintain Anti-Malware Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.1", Description = "Deploy and maintain anti-malware software on all enterprise assets."};
            var	controlFrameworkCategoryClause173 = new ControlFrameworkCategoryClause { Name = "Configure Automatic Anti-Malware Signature Updates", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.2", Description = "Configure automatic updates for anti-malware signature files on all enterprise assets."};
            var	controlFrameworkCategoryClause174 = new ControlFrameworkCategoryClause { Name = "Disable Autorun and Autoplay for Removable Media", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.3", Description = "Disable autorun and autoplay auto-execute functionality for removable media."};
            var	controlFrameworkCategoryClause175 = new ControlFrameworkCategoryClause { Name = "Configure Automatic Anti-Malware Scanning of Removable Media", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.4", Description = "Configure anti-malware software to automatically scan removable media."};
            var	controlFrameworkCategoryClause176 = new ControlFrameworkCategoryClause { Name = "Enable Anti-Exploitation Features", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.5", Description = "Enable anti-exploitation features on enterprise assets and software, where possible, such as Microsoft® Data Execution Prevention (DEP), Windows® Defender Exploit Guard (WDEG), or Apple® System Integrity Protection (SIP) and Gatekeeper™."};
            var	controlFrameworkCategoryClause177 = new ControlFrameworkCategoryClause { Name = "Centrally Manage Anti-Malware Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.6", Description = "Centrally manage anti-malware software."};
            var	controlFrameworkCategoryClause178 = new ControlFrameworkCategoryClause { Name = "Use Behaviour-Based Anti-Malware Software", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid10, Reference = "10.7", Description = "Use behavior-based anti-malware software."};

            var cisCatGuid11 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Data Recovery")!.Id;
            var	controlFrameworkCategoryClause179 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Data Recovery Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid11, Reference = "11.1", Description = "Establish and maintain a data recovery process. In the process, address the scope of data recovery activities, recovery prioritization, and the security of backup data. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause180 = new ControlFrameworkCategoryClause { Name = "Perform Automated Backups", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid11, Reference = "11.2", Description = "Perform automated backups of in-scope enterprise assets. Run backups weekly, or more frequently, based on the sensitivity of the data."};
            var	controlFrameworkCategoryClause181 = new ControlFrameworkCategoryClause { Name = "Protect Recovery Data", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid11, Reference = "11.3", Description = "Protect recovery data with equivalent controls to the original data. Reference encryption or data separation, based on requirements."};
            var	controlFrameworkCategoryClause182 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Isolated Instance of Recovery Data", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid11, Reference = "11.4", Description = "Establish and maintain an isolated instance of recovery data. Example implementations include, version controlling backup destinations through offline, cloud, or off-site systems or services."};
            var	controlFrameworkCategoryClause183 = new ControlFrameworkCategoryClause { Name = "Test Data Recovery", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid11, Reference = "11.5", Description = "Test backup recovery quarterly, or more frequently, for a sampling of in-scope enterprise assets."};

            var cisCatGuid12 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Network Infrastructure Management")!.Id;
            var	controlFrameworkCategoryClause184 = new ControlFrameworkCategoryClause { Name = "Ensure Network Infrastructure is Up-to-Date", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.1", Description = "Ensure network infrastructure is kept up-to-date. Example implementations include running the latest stable release of software and/or using currently supported network-as-a-service (NaaS) offerings. Review software versions monthly, or more frequently, to verify software support."};
            var	controlFrameworkCategoryClause185 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Secure Network Architecture", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.2", Description = "Establish and maintain a secure network architecture. A secure network architecture must address segmentation, least privilege, and availability, at a minimum."};
            var	controlFrameworkCategoryClause186 = new ControlFrameworkCategoryClause { Name = "Securely Manage Network Infrastructure", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.3", Description = "Securely manage network infrastructure. Example implementations include version-controlled-infrastructure-as-code, and the use of secure network protocols, such as SSH and HTTPS."};
            var	controlFrameworkCategoryClause187 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain Architecture Diagram(s)", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.4", Description = "Establish and maintain architecture diagram(s) and/or other network system documentation. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause188 = new ControlFrameworkCategoryClause { Name = "Centralize Network Authentication, Authorization, and Auditing (AAA)", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.5", Description = "Centralize network AAA."};
            var	controlFrameworkCategoryClause189 = new ControlFrameworkCategoryClause { Name = "Use of Secure Network Management and Communication Protocols", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.6", Description = "Use secure network management and communication protocols (e.g., 802.1X, Wi-Fi Protected Access 2 (WPA2) Enterprise or greater)."};
            var	controlFrameworkCategoryClause190 = new ControlFrameworkCategoryClause { Name = "Ensure Remote Devices Utilize a VPN and are Connecting to an Enterprise’s AAA Infrastructure", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.7", Description = "Require users to authenticate to enterprise-managed VPN and authentication services prior to accessing enterprise resources on end-user devices."};
            var	controlFrameworkCategoryClause191 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain Dedicated Computing Resources for All Administrative Work", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid12, Reference = "12.8", Description = "Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access."};

            var cisCatGuid13 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Network Monitoring and Defense")!.Id;
            var	controlFrameworkCategoryClause192 = new ControlFrameworkCategoryClause { Name = "Centralize Security Event Alerting", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.1", Description = "Centralize security event alerting across enterprise assets for log correlation and analysis. Best practice implementation requires the use of a SIEM, which includes vendor-defined event correlation alerts. A log analytics platform configured with security-relevant correlation alerts also satisfies this Safeguard."};
            var	controlFrameworkCategoryClause193 = new ControlFrameworkCategoryClause { Name = "Deploy a Host-Based Intrusion Detection Solution", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.2", Description = "Deploy a host-based intrusion detection solution on enterprise assets, where appropriate and/or supported."};
            var	controlFrameworkCategoryClause194 = new ControlFrameworkCategoryClause { Name = "Deploy a Network Intrusion Detection Solution", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.3", Description = "Deploy a network intrusion detection solution on enterprise assets, where appropriate. Example implementations include the use of a Network Intrusion Detection System (NIDS) or equivalent cloud service provider (CSP) service."};
            var	controlFrameworkCategoryClause195 = new ControlFrameworkCategoryClause { Name = "Perform Traffic Filtering Between Network Segments", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.4", Description = "Perform traffic filtering between network segments, where appropriate."};
            var	controlFrameworkCategoryClause196 = new ControlFrameworkCategoryClause { Name = "Manage Access Control for Remote Assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.5", Description = "Manage access control for assets remotely connecting to enterprise resources. Determine amount of access to enterprise resources based on: up-to-date anti-malware software installed, configuration compliance with the enterprise’s secure configuration process, and ensuring the operating system and applications are up-to-date."};
            var	controlFrameworkCategoryClause197 = new ControlFrameworkCategoryClause { Name = "Collect Network Traffic Flow Logs", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.6", Description = "Collect network traffic flow logs and/or network traffic to review and alert upon from network devices."};
            var	controlFrameworkCategoryClause198 = new ControlFrameworkCategoryClause { Name = "Deploy a Host-Based Intrusion Prevention Solution", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.7", Description = "Deploy a host-based intrusion prevention solution on enterprise assets, where appropriate and/or supported. Example implementations include use of an Endpoint Detection and Response (EDR) client or host-based IPS agent."};
            var	controlFrameworkCategoryClause199 = new ControlFrameworkCategoryClause { Name = "Deploy a Network Intrusion Prevention Solution", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.8", Description = "Deploy a network intrusion prevention solution, where appropriate. Example implementations include the use of a Network Intrusion Prevention System (NIPS) or equivalent CSP service."};
            var	controlFrameworkCategoryClause200 = new ControlFrameworkCategoryClause { Name = "Deploy Port-Level Access Control", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.9", Description = "Deploy port-level access control. Port-level access control utilizes 802.1x, or similar network access control protocols, such as certificates, and may incorporate user and/or device authentication."};
            var	controlFrameworkCategoryClause201 = new ControlFrameworkCategoryClause { Name = "Perform Application Layer Filtering", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.10", Description = "Perform application layer filtering. Example implementations include a filtering proxy, application layer firewall, or gateway."};
            var	controlFrameworkCategoryClause202 = new ControlFrameworkCategoryClause { Name = "Tune Security Event Alerting Thresholds", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid13, Reference = "13.11", Description = "Tune security event alerting thresholds monthly, or more frequently."};

            var cisCatGuid14 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Security Awareness and Skills Training")!.Id;
            var	controlFrameworkCategoryClause203 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Security Awareness Program", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.1", Description = "Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause204 = new ControlFrameworkCategoryClause { Name = "Train Workforce Members to Recognize Social Engineering Attacks", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.2", Description = "Train workforce members to recognize social engineering attacks, such as phishing, pre-texting, and tailgating."};
            var	controlFrameworkCategoryClause205 = new ControlFrameworkCategoryClause { Name = "Train Workforce Members on Authentication Best Practices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.3", Description = "Train workforce members on authentication best practices. Example topics include MFA, password composition, and credential management."};
            var	controlFrameworkCategoryClause206 = new ControlFrameworkCategoryClause { Name = "Train Workforce on Data Handling Best Practices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.4", Description = "Train workforce members on how to identify and properly store, transfer, archive, and destroy sensitive data. This also includes training workforce members on clear screen and desk best practices, such as locking their screen when they step away from their enterprise asset, erasing physical and virtual whiteboards at the end of meetings, and storing data and assets securely."};
            var	controlFrameworkCategoryClause207 = new ControlFrameworkCategoryClause { Name = "Train Workforce Members on Causes of Unintentional Data Exposure", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.5", Description = "Train workforce members to be aware of causes for unintentional data exposure. Example topics include mis-delivery of sensitive data, losing a portable end-user device, or publishing data to unintended audiences."};
            var	controlFrameworkCategoryClause208 = new ControlFrameworkCategoryClause { Name = "Train Workforce Members on Recognizing and Reporting Security Incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.6", Description = "Train workforce members to be able to recognize a potential incident and be able to report such an incident."};
            var	controlFrameworkCategoryClause209 = new ControlFrameworkCategoryClause { Name = "Train Workforce on How to Identify and Report if Their Enterprise Assets are Missing Security Updates", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.7", Description = "Train workforce to understand how to verify and report out-of-date software patches or any failures in automated processes and tools. Part of this training should include notifying IT personnel of any failures in automated processes and tools."};
            var	controlFrameworkCategoryClause210 = new ControlFrameworkCategoryClause { Name = "Train Workforce on the Dangers of Connecting to and Transmitting Enterprise Data Over Insecure Networks", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.8", Description = "Train workforce members on the dangers of connecting to, and transmitting data over, insecure networks for enterprise activities. If the enterprise has remote workers, training must include guidance to ensure that all users securely configure their home network infrastructure."};
            var	controlFrameworkCategoryClause211 = new ControlFrameworkCategoryClause { Name = "Conduct Role-Specific Security Awareness and Skills Training", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid14, Reference = "14.9", Description = "Conduct role-specific security awareness and skills training. Example implementations include secure system administration courses for IT professionals, (OWASP® Top 10 vulnerability awareness and prevention training for web application developers, and advanced social engineering awareness training for high-profile roles."};

            var cisCatGuid15 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Service Provider Management")!.Id;
            var	controlFrameworkCategoryClause212 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Inventory of Service Providers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.1", Description = "Establish and maintain an inventory of service providers. The inventory is to list all known service providers, include classification(s), and designate an enterprise contact for each service provider. Review and update the inventory annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause213 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Service Provider Management Policy", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.2", Description = "Establish and maintain a service provider management policy. Ensure the policy addresses the classification, inventory, assessment, monitoring, and decommissioning of service providers. Review and update the policy annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause214 = new ControlFrameworkCategoryClause { Name = "Classify Service Providers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.3", Description = "Classify service providers. Classification consideration may include one or more characteristics, such as data sensitivity, data volume, availability requirements, applicable regulations, inherent risk, and mitigated risk. Update and review classifications annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause215 = new ControlFrameworkCategoryClause { Name = "Ensure Service Provider Contracts Include Security Requirements", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.4", Description = "Ensure service provider contracts include security requirements. Example requirements may include minimum security program requirements, security incident and/or data breach notification and response, data encryption requirements, and data disposal commitments. These security requirements must be consistent with the enterprise’s service provider management policy. Review service provider contracts annually to ensure contracts are not missing security requirements."};
            var	controlFrameworkCategoryClause216 = new ControlFrameworkCategoryClause { Name = "Assess Service Providers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.5", Description = "Assess service providers consistent with the enterprise’s service provider management policy. Assessment scope may vary based on classification(s), and may include review of standardized assessment reports, such as Service Organization Control 2 (SOC 2) and Payment Card Industry (PCI) Attestation of Compliance (AoC), customized questionnaires, or other appropriately rigorous processes. Reassess service providers annually, at a minimum, or with new and renewed contracts."};
            var	controlFrameworkCategoryClause217 = new ControlFrameworkCategoryClause { Name = "Monitor Service Providers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.6", Description = "Monitor service providers consistent with the enterprise’s service provider management policy. Monitoring may include periodic reassessment of service provider compliance, monitoring service provider release notes, and dark web monitoring."};
            var	controlFrameworkCategoryClause218 = new ControlFrameworkCategoryClause { Name = "Securely Decommission Service Providers", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid15, Reference = "15.7", Description = "Securely decommission service providers. Example considerations include user and service account deactivation, termination of data flows, and secure disposal of enterprise data within service provider systems."};

            var cisCatGuid16 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Application Software Security")!.Id;
            var	controlFrameworkCategoryClause219 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Secure Application Development Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.1", Description = "Establish and maintain a secure application development process. In the process, address such items as: secure application design standards, secure coding practices, developer training, vulnerability management, security of third-party code, and application security testing procedures. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause220 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Process to Accept and Address Software Vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.2", Description = "Establish and maintain a process to accept and address reports of software vulnerabilities, including providing a means for external entities to report. The process is to include such items as: a vulnerability handling policy that identifies reporting process, responsible party for handling vulnerability reports, and a process for intake, assignment, remediation, and remediation testing. As part of the process, use a vulnerability tracking system that includes severity ratings, and metrics for measuring timing for identification, analysis, and remediation of vulnerabilities. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard. Third-party application developers need to consider this an externally-facing policy that helps to set expectations for outside stakeholders."};
            var	controlFrameworkCategoryClause221 = new ControlFrameworkCategoryClause { Name = "Perform Root Cause Analysis on Security Vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.3", Description = "Perform root cause analysis on security vulnerabilities. When reviewing vulnerabilities, root cause analysis is the task of evaluating underlying issues that create vulnerabilities in code, and allows development teams to move beyond just fixing individual vulnerabilities as they arise."};
            var	controlFrameworkCategoryClause222 = new ControlFrameworkCategoryClause { Name = "Establish and Manage an Inventory of Third-Party Software Components", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.4", Description = "Establish and manage an updated inventory of third-party components used in development, often referred to as a “bill of materials,” as well as components slated for future use. This inventory is to include any risks that each third-party component could pose. Evaluate the list at least monthly to identify any changes or updates to these components, and validate that the component is still supported."};
            var	controlFrameworkCategoryClause223 = new ControlFrameworkCategoryClause { Name = "Use Up-to-Date and Trusted Third-Party Software Components", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.5", Description = "Use up-to-date and trusted third-party software components. When possible, choose established and proven frameworks and libraries that provide adequate security. Acquire these components from trusted sources or evaluate the software for vulnerabilities before use."};
            var	controlFrameworkCategoryClause224 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Severity Rating System and Process for Application Vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.6", Description = "Establish and maintain a severity rating system and process for application vulnerabilities that facilitates prioritizing the order in which discovered vulnerabilities are fixed. This process includes setting a minimum level of security acceptability for releasing code or applications. Severity ratings bring a systematic way of triaging vulnerabilities that improves risk management and helps ensure the most severe bugs are fixed first. Review and update the system and process annually."};
            var	controlFrameworkCategoryClause225 = new ControlFrameworkCategoryClause { Name = "Use Standard Hardening Configuration Templates for Application Infrastructure", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.7", Description = "Use standard, industry-recommended hardening configuration templates for application infrastructure components. This includes underlying servers, databases, and web servers, and applies to cloud containers, Platform as a Service (PaaS) components, and SaaS components. Do not allow in-house developed software to weaken configuration hardening."};
            var	controlFrameworkCategoryClause226 = new ControlFrameworkCategoryClause { Name = "Separate Production and Non-Production Systems", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.8", Description = "Maintain separate environments for production and non-production systems."};
            var	controlFrameworkCategoryClause227 = new ControlFrameworkCategoryClause { Name = "Train Developers in Application Security Concepts and Secure Coding", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.9", Description = "Ensure that all software development personnel receive training in writing secure code for their specific development environment and responsibilities. Training can include general security principles and application security standard practices. Conduct training at least annually and design in a way to promote security within the development team, and build a culture of security among the developers."};
            var	controlFrameworkCategoryClause228 = new ControlFrameworkCategoryClause { Name = "Apply Secure Design Principles in Application Architectures", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.10", Description = "Apply secure design principles in application architectures. Secure design principles include the concept of least privilege and enforcing mediation to validate every operation that the user makes, promoting the concept of never trust user input. Examples include ensuring that explicit error checking is performed and documented for all input, including for size, data type, and acceptable ranges or formats. Secure design also means minimizing the application infrastructure attack surface, such as turning off unprotected ports and services, removing unnecessary programs and files, and renaming or removing default accounts."};
            var	controlFrameworkCategoryClause229 = new ControlFrameworkCategoryClause { Name = "Leverage Vetted Modules or Services for Application Security Components", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.11", Description = "Leverage vetted modules or services for application security components, such as identity management, encryption, and auditing and logging. Using platform features in critical security functions will reduce developers’ workload and minimize the likelihood of design or implementation errors. Modern operating systems provide effective mechanisms for identification, authentication, and authorization and make those mechanisms available to applications. Use only standardized, currently accepted, and extensively reviewed encryption algorithms. Operating systems also provide mechanisms to create and maintain secure audit logs."};
            var	controlFrameworkCategoryClause230 = new ControlFrameworkCategoryClause { Name = "Implement Code-Level Security Checks", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.12", Description = "Apply static and dynamic analysis tools within the application life cycle to verify that secure coding practices are being followed."};
            var	controlFrameworkCategoryClause231 = new ControlFrameworkCategoryClause { Name = "Conduct Application Penetration Testing", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.13", Description = "Conduct application penetration testing. For critical applications, authenticated penetration testing is better suited to finding business logic vulnerabilities than code scanning and automated security testing. Penetration testing relies on the skill of the tester to manually manipulate an application as an authenticated and unauthenticated user."};
            var	controlFrameworkCategoryClause232 = new ControlFrameworkCategoryClause { Name = "Conduct Threat Modeling", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid16, Reference = "16.14", Description = "Conduct threat modeling. Threat modeling is the process of identifying and addressing application security design flaws within a design, before code is created. It is conducted through specially trained individuals who evaluate the application design and gauge security risks for each entry point and access level. The goal is to map out the application, architecture, and infrastructure in a structured way to understand its weaknesses."};

            var cisCatGuid17 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Incident Response Management")!.Id;
            var	controlFrameworkCategoryClause233 = new ControlFrameworkCategoryClause { Name = "Designate Personnel to Manage Incident Handling", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.1", Description = "Designate one key person, and at least one backup, who will manage the enterprise’s incident handling process. Management personnel are responsible for the coordination and documentation of incident response and recovery efforts and can consist of employees internal to the enterprise, third-party vendors, or a hybrid approach. If using a third-party vendor, designate at least one person internal to the enterprise to oversee any third-party work. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause234 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain Contact Information for Reporting Security Incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.2", Description = "Establish and maintain contact information for parties that need to be informed of security incidents. Contacts may include internal staff, third-party vendors, law enforcement, cyber insurance providers, relevant government agencies, Information Sharing and Analysis Center (ISAC) partners, or other stakeholders. Verify contacts annually to ensure that information is up-to-date."};
            var	controlFrameworkCategoryClause235 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Enterprise Process for Reporting Incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.3", Description = "Establish and maintain an enterprise process for the workforce to report security incidents. The process includes reporting timeframe, personnel to report to, mechanism for reporting, and the minimum information to be reported. Ensure the process is publicly available to all of the workforce. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause236 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain an Incident Response Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.4", Description = "Establish and maintain an incident response process that addresses roles and responsibilities, compliance requirements, and a communication plan. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause237 = new ControlFrameworkCategoryClause { Name = "Assign Key Roles and Responsibilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.5", Description = "Assign key roles and responsibilities for incident response, including staff from legal, IT, information security, facilities, public relations, human resources, incident responders, and analysts, as applicable. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause238 = new ControlFrameworkCategoryClause { Name = "Define Mechanisms for Communicating During Incident Response", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.6", Description = "Determine which primary and secondary mechanisms will be used to communicate and report during a security incident. Mechanisms can include phone calls, emails, or letters. Keep in mind that certain mechanisms, such as emails, can be affected during a security incident. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};
            var	controlFrameworkCategoryClause239 = new ControlFrameworkCategoryClause { Name = "Conduct Routine Incident Response Exercises", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.7", Description = "Plan and conduct routine incident response exercises and scenarios for key personnel involved in the incident response process to prepare for responding to real-world incidents. Exercises need to test communication channels, decision making, and workflows. Conduct testing on an annual basis, at a minimum."};
            var	controlFrameworkCategoryClause240 = new ControlFrameworkCategoryClause { Name = "Conduct Post-Incident Reviews", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.8", Description = "Conduct post-incident reviews. Post-incident reviews help prevent incident recurrence through identifying lessons learned and follow-up action."};
            var	controlFrameworkCategoryClause241 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain Security Incident Thresholds", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid17, Reference = "17.9", Description = "Establish and maintain security incident thresholds, including, at a minimum, differentiating between an incident and an event. Examples can include: abnormal activity, security vulnerability, security weakness, data breach, privacy incident, etc. Review annually, or when significant enterprise changes occur that could impact this Safeguard."};

            var cisCatGuid18 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Penetration Testing")!.Id;
            var	controlFrameworkCategoryClause242 = new ControlFrameworkCategoryClause { Name = "Establish and Maintain a Penetration Testing Program", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid18, Reference = "18.1", Description = "Establish and maintain a penetration testing program appropriate to the size, complexity, and maturity of the enterprise. Penetration testing program characteristics include scope, such as network, web application, Application Programming Interface (API), hosted services, and physical premise controls; frequency; limitations, such as acceptable hours, and excluded attack types; point of contact information; remediation, such as how findings will be routed internally; and retrospective requirements."};
            var	controlFrameworkCategoryClause243 = new ControlFrameworkCategoryClause { Name = "Perform Periodic External Penetration Tests", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid18, Reference = "18.2", Description = "Perform periodic external penetration tests based on program requirements, no less than annually. External penetration testing must include enterprise and environmental reconnaissance to detect exploitable information. Penetration testing requires specialized skills and experience and must be conducted through a qualified party. The testing may be clear box or opaque box."};
            var	controlFrameworkCategoryClause244 = new ControlFrameworkCategoryClause { Name = "Remediate Penetration Test Findings", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid18, Reference = "18.3", Description = "Remediate penetration test findings based on the enterprise’s policy for remediation scope and prioritization."};
            var	controlFrameworkCategoryClause245 = new ControlFrameworkCategoryClause { Name = "Validate Security Measures", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid18, Reference = "18.4", Description = "Validate security measures after each penetration test. If deemed necessary, modify rulesets and capabilities to detect the techniques used during testing."};
            var	controlFrameworkCategoryClause246 = new ControlFrameworkCategoryClause { Name = "Perform Periodic Internal Penetration Tests", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = cisCatGuid18, Reference = "18.5", Description = "Perform periodic internal penetration tests based on program requirements, no less than annually. The testing may be clear box or opaque box."};
            
            var nistCatGuid1 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Organisational Context")!.Id;
            var controlFrameworkCategoryClause247 = new ControlFrameworkCategoryClause { Name = "The organizational mission is understood and informs cybersecurity risk management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid1, Reference = "GV.OC-01", Description = ""};	
            var controlFrameworkCategoryClause248 = new ControlFrameworkCategoryClause { Name = "Internal and external stakeholders are understood, and their needs and expectations regarding cybersecurity risk management are understood and considered", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid1, Reference = "GV.OC-02", Description = ""};	
            var controlFrameworkCategoryClause249 = new ControlFrameworkCategoryClause { Name = "Legal, regulatory, and contractual requirements regarding cybersecurity - including privacy and civil liberties obligations - are understood and managed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid1, Reference = "GV.OC-03", Description = ""};	
            var controlFrameworkCategoryClause250 = new ControlFrameworkCategoryClause { Name = "Critical objectives, capabilities, and services that stakeholders depend on or expect from the organization are understood and communicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid1, Reference = "GV.OC-04", Description = ""};	
            var controlFrameworkCategoryClause251 = new ControlFrameworkCategoryClause { Name = "Outcomes, capabilities, and services that the organization depends on are understood and communicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid1, Reference = "GV.OC-05", Description = ""};	

            var nistCatGuid2 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Risk Management Strategy")!.Id;
            var controlFrameworkCategoryClause252 = new ControlFrameworkCategoryClause { Name = "Risk management objectives are established and agreed to by organizational stakeholders", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-01", Description = ""};	
            var controlFrameworkCategoryClause253 = new ControlFrameworkCategoryClause { Name = "Risk appetite and risk tolerance statements are established, communicated, and maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-02", Description = ""};	
            var controlFrameworkCategoryClause254 = new ControlFrameworkCategoryClause { Name = "Cybersecurity risk management activities and outcomes are included in enterprise risk management processes", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-03", Description = ""};	
            var controlFrameworkCategoryClause255 = new ControlFrameworkCategoryClause { Name = "Strategic direction that describes appropriate risk response options is established and communicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-04", Description = ""};	
            var controlFrameworkCategoryClause256 = new ControlFrameworkCategoryClause { Name = "Lines of communication across the organization are established for cybersecurity risks, including risks from suppliers and other third parties", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-05", Description = ""};	
            var controlFrameworkCategoryClause257 = new ControlFrameworkCategoryClause { Name = "A standardized method for calculating, documenting, categorizing, and prioritizing cybersecurity risks is established and communicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-06", Description = ""};	
            var controlFrameworkCategoryClause258 = new ControlFrameworkCategoryClause { Name = "Strategic opportunities (i.e., positive risks) are characterized and are included in organizational cybersecurity risk discussions", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid2, Reference = "GV.RM-07", Description = ""};	

            var nistCatGuid3 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Roles, Responsibilities, and Authorities")!.Id;
            var controlFrameworkCategoryClause259 = new ControlFrameworkCategoryClause { Name = "Organizational leadership is responsible and accountable for cybersecurity risk and fosters a culture that is risk-aware, ethical, and continually improving", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid3, Reference = "GV.RR-01", Description = ""};	
            var controlFrameworkCategoryClause260 = new ControlFrameworkCategoryClause { Name = "Roles, responsibilities, and authorities related to cybersecurity risk management are established, communicated, understood, and enforced", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid3, Reference = "GV.RR-02", Description = ""};	
            var controlFrameworkCategoryClause261 = new ControlFrameworkCategoryClause { Name = "Adequate resources are allocated commensurate with the cybersecurity risk strategy, roles, responsibilities, and policies", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid3, Reference = "GV.RR-03", Description = ""};	
            var controlFrameworkCategoryClause262 = new ControlFrameworkCategoryClause { Name = "Cybersecurity is included in human resources practices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid3, Reference = "GV.RR-04", Description = ""};	

            var nistCatGuid4 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Policy")!.Id;
            var controlFrameworkCategoryClause263 = new ControlFrameworkCategoryClause { Name = "Policy for managing cybersecurity risks is established based on organizational context, cybersecurity strategy, and priorities and is communicated and enforced", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid4, Reference = "GV.PO-01", Description = ""};	
            var controlFrameworkCategoryClause264 = new ControlFrameworkCategoryClause { Name = "Policy for managing cybersecurity risks is reviewed, updated, communicated, and enforced to reflect changes in requirements, threats, technology, and organizational mission", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid4, Reference = "GV.PO-02", Description = ""};	

            var nistCatGuid5 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Oversight")!.Id;
            var controlFrameworkCategoryClause265 = new ControlFrameworkCategoryClause { Name = "Cybersecurity risk management strategy outcomes are reviewed to inform and adjust strategy and direction", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid5, Reference = "GV.OV-01", Description = ""};	
            var controlFrameworkCategoryClause266 = new ControlFrameworkCategoryClause { Name = "The cybersecurity risk management strategy is reviewed and adjusted to ensure coverage of organizational requirements and risks", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid5, Reference = "GV.OV-02", Description = ""};	
            var controlFrameworkCategoryClause267 = new ControlFrameworkCategoryClause { Name = "Organizational cybersecurity risk management performance is evaluated and reviewed for adjustments needed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid5, Reference = "GV.OV-03", Description = ""};	

            var nistCatGuid6 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Govern - Cybersecurity Supply Chain Risk Management")!.Id;
            var controlFrameworkCategoryClause268 = new ControlFrameworkCategoryClause { Name = "A cybersecurity supply chain risk management program, strategy, objectives, policies, and processes are established and agreed to by organizational stakeholders", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-01", Description = ""};	
            var controlFrameworkCategoryClause269 = new ControlFrameworkCategoryClause { Name = "Cybersecurity roles and responsibilities for suppliers, customers, and partners are established, communicated, and coordinated internally and externally", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-02", Description = ""};	
            var controlFrameworkCategoryClause270 = new ControlFrameworkCategoryClause { Name = "Cybersecurity supply chain risk management is integrated into cybersecurity and enterprise risk management, risk assessment, and improvement processes", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-03", Description = ""};	
            var controlFrameworkCategoryClause271 = new ControlFrameworkCategoryClause { Name = "Suppliers are known and prioritized by criticality", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-04", Description = ""};	
            var controlFrameworkCategoryClause272 = new ControlFrameworkCategoryClause { Name = "Requirements to address cybersecurity risks in supply chains are established, prioritized, and integrated into contracts and other types of agreements with suppliers and other relevant third parties", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-05", Description = ""};	
            var controlFrameworkCategoryClause273 = new ControlFrameworkCategoryClause { Name = "Planning and due diligence are performed to reduce risks before entering into formal supplier or other third-party relationships", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-06", Description = ""};	
            var controlFrameworkCategoryClause274 = new ControlFrameworkCategoryClause { Name = "The risks posed by a supplier, their products and services, and other third parties are understood, recorded, prioritized, assessed, responded to, and monitored over the course of the relationship", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-07", Description = ""};	
            var controlFrameworkCategoryClause275 = new ControlFrameworkCategoryClause { Name = "Relevant suppliers and other third parties are included in incident planning, response, and recovery activities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-08", Description = ""};	
            var controlFrameworkCategoryClause276 = new ControlFrameworkCategoryClause { Name = "Supply chain security practices are integrated into cybersecurity and enterprise risk management programs, and their performance is monitored throughout the technology product and service life cycle", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-09", Description = ""};	
            var controlFrameworkCategoryClause277 = new ControlFrameworkCategoryClause { Name = "Cybersecurity supply chain risk management plans include provisions for activities that occur after the conclusion of a partnership or service agreement", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid6, Reference = "GV.SC-10", Description = ""};	

            var nistCatGuid7 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Identify - Asset Management")!.Id;
            var controlFrameworkCategoryClause278 = new ControlFrameworkCategoryClause { Name = "Inventories of hardware managed by the organization are maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-01", Description = ""};	
            var controlFrameworkCategoryClause279 = new ControlFrameworkCategoryClause { Name = "Inventories of software, services, and systems managed by the organization are maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-02", Description = ""};	
            var controlFrameworkCategoryClause280 = new ControlFrameworkCategoryClause { Name = "Representations of the organization's authorized network communication and internal and external network data flows are maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-03", Description = ""};	
            var controlFrameworkCategoryClause281 = new ControlFrameworkCategoryClause { Name = "Inventories of services provided by suppliers are maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-04", Description = ""};	
            var controlFrameworkCategoryClause282 = new ControlFrameworkCategoryClause { Name = "Assets are prioritized based on classification, criticality, resources, and impact on the mission", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-05", Description = ""};	
            var controlFrameworkCategoryClause284 = new ControlFrameworkCategoryClause { Name = "Inventories of data and corresponding metadata for designated data types are maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-07", Description = ""};	
            var controlFrameworkCategoryClause285 = new ControlFrameworkCategoryClause { Name = "Systems, hardware, software, services, and data are managed throughout their life cycles", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid7, Reference = "ID.AM-08", Description = ""};	

            var nistCatGuid8 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Identify - Risk Assessment")!.Id;
            var controlFrameworkCategoryClause286 = new ControlFrameworkCategoryClause { Name = "Vulnerabilities in assets are identified, validated, and recorded", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-01", Description = ""};	
            var controlFrameworkCategoryClause287 = new ControlFrameworkCategoryClause { Name = "Cyber threat intelligence is received from information sharing forums and sources", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-02", Description = ""};	
            var controlFrameworkCategoryClause288 = new ControlFrameworkCategoryClause { Name = "Internal and external threats to the organization are identified and recorded", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-03", Description = ""};	
            var controlFrameworkCategoryClause289 = new ControlFrameworkCategoryClause { Name = "Potential impacts and likelihoods of threats exploiting vulnerabilities are identified and recorded", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-04", Description = ""};	
            var controlFrameworkCategoryClause290 = new ControlFrameworkCategoryClause { Name = "Threats, vulnerabilities, likelihoods, and impacts are used to understand inherent risk and inform risk response prioritization", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-05", Description = ""};	
            var controlFrameworkCategoryClause291 = new ControlFrameworkCategoryClause { Name = "Risk responses are chosen, prioritized, planned, tracked, and communicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-06", Description = ""};	
            var controlFrameworkCategoryClause292 = new ControlFrameworkCategoryClause { Name = "Changes and exceptions are managed, assessed for risk impact, recorded, and tracked", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-07", Description = ""};	
            var controlFrameworkCategoryClause293 = new ControlFrameworkCategoryClause { Name = "Processes for receiving, analyzing, and responding to vulnerability disclosures are established", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-08", Description = ""};	
            var controlFrameworkCategoryClause294 = new ControlFrameworkCategoryClause { Name = "The authenticity and integrity of hardware and software are assessed prior to acquisition and use", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-09", Description = ""};	
            var controlFrameworkCategoryClause295 = new ControlFrameworkCategoryClause { Name = "Critical suppliers are assessed prior to acquisition", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid8, Reference = "ID.RA-10", Description = ""};	

            var nistCatGuid9 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Identify - Improvement")!.Id;
            var controlFrameworkCategoryClause296 = new ControlFrameworkCategoryClause { Name = "Improvements are identified from evaluations", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid9, Reference = "ID.IM-01", Description = ""};	
            var controlFrameworkCategoryClause297 = new ControlFrameworkCategoryClause { Name = "Improvements are identified from security tests and exercises, including those done in coordination with suppliers and relevant third parties", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid9, Reference = "ID.IM-02", Description = ""};	
            var controlFrameworkCategoryClause298 = new ControlFrameworkCategoryClause { Name = "Improvements are identified from execution of operational processes, procedures, and activities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid9, Reference = "ID.IM-03", Description = ""};	
            var controlFrameworkCategoryClause299 = new ControlFrameworkCategoryClause { Name = "Incident response plans and other cybersecurity plans that affect operations are established, communicated, maintained, and improved", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid9, Reference = "ID.IM-04", Description = ""};	

            var nistCatGuid10 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Protect - Identity Management, Authentication, and Access Control")!.Id;
            var controlFrameworkCategoryClause300 = new ControlFrameworkCategoryClause { Name = "Identities and credentials for authorized users, services, and hardware are managed by the organization", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-01", Description = ""};	
            var controlFrameworkCategoryClause301 = new ControlFrameworkCategoryClause { Name = "Identities are proofed and bound to credentials based on the context of interactions", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-02", Description = ""};	
            var controlFrameworkCategoryClause302 = new ControlFrameworkCategoryClause { Name = "Users, services, and hardware are authenticated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-03", Description = ""};	
            var controlFrameworkCategoryClause303 = new ControlFrameworkCategoryClause { Name = "Identity assertions are protected, conveyed, and verified", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-04", Description = ""};	
            var controlFrameworkCategoryClause304 = new ControlFrameworkCategoryClause { Name = "Access permissions, entitlements, and authorizations are defined in a policy, managed, enforced, and reviewed, and incorporate the principles of least privilege and separation of duties", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-05", Description = ""};	
            var controlFrameworkCategoryClause305 = new ControlFrameworkCategoryClause { Name = "Physical access to assets is managed, monitored, and enforced commensurate with risk", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid10, Reference = "PR.AA-06", Description = ""};	

            var nistCatGuid11 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Protect - Awareness and Training")!.Id;
            var controlFrameworkCategoryClause306 = new ControlFrameworkCategoryClause { Name = "Personnel are provided with awareness and training so that they possess the knowledge and skills to perform general tasks with cybersecurity risks in mind", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid11, Reference = "PR.AT-01", Description = ""};	
            var controlFrameworkCategoryClause307 = new ControlFrameworkCategoryClause { Name = "Individuals in specialized roles are provided with awareness and training so that they possess the knowledge and skills to perform relevant tasks with cybersecurity risks in mind", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid11, Reference = "PR.AT-02", Description = ""};	

            var nistCatGuid12 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Protect - Data Security")!.Id;
            var controlFrameworkCategoryClause308 = new ControlFrameworkCategoryClause { Name = "The confidentiality, integrity, and availability of data-at-rest are protected", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid12, Reference = "PR.DS-01", Description = ""};	
            var controlFrameworkCategoryClause309 = new ControlFrameworkCategoryClause { Name = "The confidentiality, integrity, and availability of data-in-transit are protected", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid12, Reference = "PR.DS-02", Description = ""};	
            var controlFrameworkCategoryClause310 = new ControlFrameworkCategoryClause { Name = "The confidentiality, integrity, and availability of data-in-use are protected", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid12, Reference = "PR.DS-10", Description = ""};	
            var controlFrameworkCategoryClause311 = new ControlFrameworkCategoryClause { Name = "Backups of data are created, protected, maintained, and tested", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid12, Reference = "PR.DS-11", Description = ""};	

            var nistCatGuid13 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Protect - Platform Security")!.Id;
            var controlFrameworkCategoryClause312 = new ControlFrameworkCategoryClause { Name = "Configuration management practices are established and applied", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-01", Description = ""};	
            var controlFrameworkCategoryClause313 = new ControlFrameworkCategoryClause { Name = "Software is maintained, replaced, and removed commensurate with risk", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-02", Description = ""};	
            var controlFrameworkCategoryClause314 = new ControlFrameworkCategoryClause { Name = "Hardware is maintained, replaced, and removed commensurate with risk", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-03", Description = ""};	
            var controlFrameworkCategoryClause315 = new ControlFrameworkCategoryClause { Name = "Log records are generated and made available for continuous monitoring", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-04", Description = ""};	
            var controlFrameworkCategoryClause316 = new ControlFrameworkCategoryClause { Name = "Installation and execution of unauthorized software are prevented", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-05", Description = ""};	
            var controlFrameworkCategoryClause317 = new ControlFrameworkCategoryClause { Name = "Secure software development practices are integrated, and their performance is monitored throughout the software development life cycle", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid13, Reference = "PR.PS-06", Description = ""};	

            var nistCatGuid14 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Protect - Technology Infrastructure Resilience")!.Id;
            var controlFrameworkCategoryClause318 = new ControlFrameworkCategoryClause { Name = "Networks and environments are protected from unauthorized logical access and usage", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid14, Reference = "PR.IR-01", Description = ""};	
            var controlFrameworkCategoryClause319 = new ControlFrameworkCategoryClause { Name = "The organization's technology assets are protected from environmental threats", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid14, Reference = "PR.IR-02", Description = ""};	
            var controlFrameworkCategoryClause320 = new ControlFrameworkCategoryClause { Name = "Mechanisms are implemented to achieve resilience requirements in normal and adverse situations", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid14, Reference = "PR.IR-03", Description = ""};	
            var controlFrameworkCategoryClause321 = new ControlFrameworkCategoryClause { Name = "Adequate resource capacity to ensure availability is maintained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid14, Reference = "PR.IR-04", Description = ""};	

            var nistCatGuid15 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Detect - Continuous Monitoring")!.Id;
            var controlFrameworkCategoryClause322 = new ControlFrameworkCategoryClause { Name = "Networks and network services are monitored to find potentially adverse events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid15, Reference = "DE.CM-01", Description = ""};	
            var controlFrameworkCategoryClause323 = new ControlFrameworkCategoryClause { Name = "The physical environment is monitored to find potentially adverse events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid15, Reference = "DE.CM-02", Description = ""};	
            var controlFrameworkCategoryClause324 = new ControlFrameworkCategoryClause { Name = "Personnel activity and technology usage are monitored to find potentially adverse events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid15, Reference = "DE.CM-03", Description = ""};	
            var controlFrameworkCategoryClause325 = new ControlFrameworkCategoryClause { Name = "External service provider activities and services are monitored to find potentially adverse events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid15, Reference = "DE.CM-06", Description = ""};	
            var controlFrameworkCategoryClause326 = new ControlFrameworkCategoryClause { Name = "Computing hardware and software, runtime environments, and their data are monitored to find potentially adverse events", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid15, Reference = "DE.CM-09", Description = ""};	

            var nistCatGuid16 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Detect - Adverse Event Analysis")!.Id;
            var controlFrameworkCategoryClause327 = new ControlFrameworkCategoryClause { Name = "Potentially adverse events are analyzed to better understand associated activities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-02", Description = ""};	
            var controlFrameworkCategoryClause328 = new ControlFrameworkCategoryClause { Name = "Information is correlated from multiple sources", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-03", Description = ""};	
            var controlFrameworkCategoryClause329 = new ControlFrameworkCategoryClause { Name = "The estimated impact and scope of adverse events are understood", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-04", Description = ""};	
            var controlFrameworkCategoryClause330 = new ControlFrameworkCategoryClause { Name = "Information on adverse events is provided to authorized staff and tools", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-06", Description = ""};	
            var controlFrameworkCategoryClause331 = new ControlFrameworkCategoryClause { Name = "Cyber threat intelligence and other contextual information are integrated into the analysis", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-07", Description = ""};	
            var controlFrameworkCategoryClause332 = new ControlFrameworkCategoryClause { Name = "Incidents are declared when adverse events meet the defined incident criteria", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid16, Reference = "DE.AE-08", Description = ""};	

            var nistCatGuid17 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Respond - Incident Management")!.Id;
            var controlFrameworkCategoryClause333 = new ControlFrameworkCategoryClause { Name = "The incident response plan is executed in coordination with relevant third parties once an incident is declared", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid17, Reference = "RS.MA-01", Description = ""};
            var controlFrameworkCategoryClause334 = new ControlFrameworkCategoryClause { Name = "Incident reports are triaged and validated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid17, Reference = "RS.MA-02", Description = ""};
            var controlFrameworkCategoryClause335 = new ControlFrameworkCategoryClause { Name = "Incidents are categorized and prioritized", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid17, Reference = "RS.MA-03", Description = ""};
            var controlFrameworkCategoryClause336 = new ControlFrameworkCategoryClause { Name = "Incidents are escalated or elevated as needed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid17, Reference = "RS.MA-04", Description = ""};
            var controlFrameworkCategoryClause337 = new ControlFrameworkCategoryClause { Name = "The criteria for initiating incident recovery are applied", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid17, Reference = "RS.MA-05", Description = ""};

            var nistCatGuid18 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Respond - Incident Analysis")!.Id;
            var controlFrameworkCategoryClause338 = new ControlFrameworkCategoryClause { Name = "Analysis is performed to establish what has taken place during an incident and the root cause of the incident", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid18, Reference = "RS.AN-03", Description = ""};
            var controlFrameworkCategoryClause339 = new ControlFrameworkCategoryClause { Name = "Actions performed during an investigation are recorded, and the records' integrity and provenance are preserved", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid18, Reference = "RS.AN-06", Description = ""};
            var controlFrameworkCategoryClause340 = new ControlFrameworkCategoryClause { Name = "Incident data and metadata are collected, and their integrity and provenance are preserved", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid18, Reference = "RS.AN-07", Description = ""};
            var controlFrameworkCategoryClause341 = new ControlFrameworkCategoryClause { Name = "An incident's magnitude is estimated and validated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid18, Reference = "RS.AN-08", Description = ""};

            var nistCatGuid19 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Respond - Incident Response Reporting and Communication")!.Id;
            var controlFrameworkCategoryClause342 = new ControlFrameworkCategoryClause { Name = "Internal and external stakeholders are notified of incidents", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid19, Reference = "RS.CO-02", Description = ""};	
            var controlFrameworkCategoryClause343 = new ControlFrameworkCategoryClause { Name = "Information is shared with designated internal and external stakeholders", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid19, Reference = "RS.CO-03", Description = ""};	

            var nistCatGuid20 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Respond - Incident Mitigation")!.Id;
            var controlFrameworkCategoryClause344 = new ControlFrameworkCategoryClause { Name = "Incidents are contained", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid20, Reference = "RS.MI-01", Description = ""};	
            var controlFrameworkCategoryClause345 = new ControlFrameworkCategoryClause { Name = "Incidents are eradicated", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid20, Reference = "RS.MI-02", Description = ""};	
            
            var nistCatGuid21 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Recover - Incident Recovery Plan Execution")!.Id;
            var controlFrameworkCategoryClause346 = new ControlFrameworkCategoryClause { Name = "The recovery portion of the incident response plan is executed once initiated from the incident response process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-01", Description = ""};	
            var controlFrameworkCategoryClause347 = new ControlFrameworkCategoryClause { Name = "Recovery actions are selected, scoped, prioritized, and performed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-02", Description = ""};	
            var controlFrameworkCategoryClause348 = new ControlFrameworkCategoryClause { Name = "The integrity of backups and other restoration assets is verified before using them for restoration", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-03", Description = ""};	
            var controlFrameworkCategoryClause349 = new ControlFrameworkCategoryClause { Name = "Critical mission functions and cybersecurity risk management are considered to establish post-incident operational norm", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-04", Description = ""};	
            var controlFrameworkCategoryClause350 = new ControlFrameworkCategoryClause { Name = "The integrity of restored assets is verified, systems and services are restored, and normal operating status is confirmed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-05", Description = ""};	
            var controlFrameworkCategoryClause351 = new ControlFrameworkCategoryClause { Name = "The end of incident recovery is declared based on criteria, and incident-related documentation is completed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid21, Reference = "RC.RP-06", Description = ""};	

            var nistCatGuid22 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Recover - Incident Recovery Communication")!.Id;
            var controlFrameworkCategoryClause352 = new ControlFrameworkCategoryClause { Name = "Recovery activities and progress in restoring operational capabilities are communicated to designated internal and external stakeholders", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid22, Reference = "RC.CO-03", Description = ""};	
            var controlFrameworkCategoryClause353 = new ControlFrameworkCategoryClause { Name = "Public updates on incident recovery are shared using approved methods and messaging", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = nistCatGuid22, Reference = "RC.CO-04", Description = ""};	

            var theiaCatGuid1 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "MFA & Access Controls")!.Id;
            var controlFrameworkCategoryClause354 = new ControlFrameworkCategoryClause { Name = "MFA for Remote Access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.1", Description = "Require MFA for all remote logins to the corporate network by using secure remote access, such as virtual private network (VPN) and remote desktop protocol (RDP)."};
            var controlFrameworkCategoryClause355 = new ControlFrameworkCategoryClause { Name = "MFA and Encryption for Administrators", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.2", Description = "Require multifactor authentication and encrypted channels for all administrative account access, irrespective of a user’s location"};
            var controlFrameworkCategoryClause356 = new ControlFrameworkCategoryClause { Name = "MFA for critical application access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.3", Description = "Require MFA for access to the most critical or sensitive data or systems, irrespective of a user’s location."};
            var controlFrameworkCategoryClause357 = new ControlFrameworkCategoryClause { Name = "Complex Password enforcement", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.4", Description = "Enforce complex long passwords that are longer than 14 characters and use upper and lowercase letters, numbers, and symbols."};
            var controlFrameworkCategoryClause358 = new ControlFrameworkCategoryClause { Name = "Identification of accounts and assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.5", Description = "Your organisation can identify: All systems and applications that are accessible remotely; critical and sensitive data, as well as all systems and applications that it is stored on; All high-privileged and administrative users"};
            var controlFrameworkCategoryClause359 = new ControlFrameworkCategoryClause { Name = "Risk Ranked authentication", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.6", Description = "Implement risk-ranked authentication, applying varying levels of stringency to authentication processes based on the likelihood that access to a given system could result in it being compromised"};
            var controlFrameworkCategoryClause360 = new ControlFrameworkCategoryClause { Name = "VPN and MFA combination", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.7", Description = "Combine the VPN and any remote solutions with MFA"};
            var controlFrameworkCategoryClause361 = new ControlFrameworkCategoryClause { Name = "Asset authentication hardening", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.8", Description = "Identify all corporate devices, especially those that accept biometrics, for potential use as an additional factor."};
            var controlFrameworkCategoryClause362 = new ControlFrameworkCategoryClause { Name = "Access control regulatory Compliance", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.9", Description = "Check and ensure compliance to local regulation regarding data protection, privacy, and biometrics data."};
            var controlFrameworkCategoryClause363 = new ControlFrameworkCategoryClause { Name = "Full deployment of access control on all devices", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.10", Description = "Deploy factors in all devices to avoid a compromise affecting them all."};
            var controlFrameworkCategoryClause364 = new ControlFrameworkCategoryClause { Name = "Access security awareness", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid1, Reference = "1.11", Description = "Train and inform employees on the value of security to reduce resistance and avoid any misunderstandings."};

            var theiaCatGuid2 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Email & Web Security")!.Id;
            var controlFrameworkCategoryClause365 = new ControlFrameworkCategoryClause { Name = "Scan and Filter Incoming e-mails", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid2, Reference = "2.1", Description = "Using technology to scan and filter incoming emails for malicious attachments and links."};
            var controlFrameworkCategoryClause366 = new ControlFrameworkCategoryClause { Name = "Disable Macros by Default", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid2, Reference = "2.2", Description = "Preventing macro-enabled files from running by default."};
            var controlFrameworkCategoryClause367 = new ControlFrameworkCategoryClause { Name = "Sandbox Evaluation", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid2, Reference = "2.3", Description = "Evaluating email attachments in a sandbox environment prior to user delivery, in order to determine whether files are malicious"};
            var controlFrameworkCategoryClause368 = new ControlFrameworkCategoryClause { Name = "Web Filtering", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid2, Reference = "2.4", Description = "Using technology to monitor web content and to block access to malicious websites or web content."};

            var theiaCatGuid3 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Backups")!.Id;
            var controlFrameworkCategoryClause369 = new ControlFrameworkCategoryClause { Name = "Tested Backup Procedures", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid3, Reference = "3.1", Description = "Organizations should review their critical systems and assets, and ensure that backup procedures are adequate and tested regularly"};
            var controlFrameworkCategoryClause370 = new ControlFrameworkCategoryClause { Name = "Offline Backup", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid3, Reference = "3.2", Description = "Organisations store one copy of the backup is stored offline and disconnected from the network"};
            var controlFrameworkCategoryClause371 = new ControlFrameworkCategoryClause { Name = "Documented response plans in place", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid3, Reference = "3.3", Description = "Disaster recovery, business continuity, and incident response plans are in place to accurately document the process that would be taken to recover systems from backups"};
            var controlFrameworkCategoryClause372 = new ControlFrameworkCategoryClause { Name = "Critical Asset backup", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid3, Reference = "3.4", Description = "Have a backup solution for the systems, data, and assets that are critical"};

            var theiaCatGuid4 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Privileged Access Management")!.Id;
            var controlFrameworkCategoryClause373 = new ControlFrameworkCategoryClause { Name = "Risk ranked critical assets", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid4, Reference = "4.1", Description = "Risk-based approach to identify critical assets that are at the highest risk of exposure, as a result of the compromise of privileged accounts, and then only implement the solution for those assets"};
            var controlFrameworkCategoryClause374 = new ControlFrameworkCategoryClause { Name = "Consistent operational effectiveness for PAM", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid4, Reference = "4.2", Description = "An organization should establish a governance and monitoring program for PAM so that performance does not degrade over time."};
            var controlFrameworkCategoryClause375 = new ControlFrameworkCategoryClause { Name = "Scalability of PAM", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid4, Reference = "4.3", Description = "Roadmaps for business growth must be factored in for further PAM implementation and scalability"};

            var theiaCatGuid5 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Endpoint Detection & Response")!.Id;
            var controlFrameworkCategoryClause376 = new ControlFrameworkCategoryClause { Name = "Visibility of Endpoints", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.1", Description = "Visibility across all your endpoints. Provide real-time visibility for you to view suspicious activities, even as they attempt to breach your environment, and stop them immediately"};
            var controlFrameworkCategoryClause377 = new ControlFrameworkCategoryClause { Name = "SIEM Implementation", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.2", Description = "A solution that collects a significant amount of telemetry from endpoints, which can be mined for signs of attack with a variety of analytic techniques"};
            var controlFrameworkCategoryClause378 = new ControlFrameworkCategoryClause { Name = "Behavioural indicators of attack", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.3", Description = "Effective endpoint detection and response requires behavioural approaches that search for indicators of attack (IOAs), so you are alerted of suspicious activities before a compromise can occur"};
            var controlFrameworkCategoryClause379 = new ControlFrameworkCategoryClause { Name = "Threat Intelligence incorporation", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.4", Description = "A solution that integrates threat intelligence, including details on the attributed adversary that is attacking you or other information about the attack"};
            var controlFrameworkCategoryClause380 = new ControlFrameworkCategoryClause { Name = "Real time threat response", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.5", Description = "A quick-response, solutions should operate in real-time, provide accurate alerting, and automate threat response. This requires detection engines that produce minimal false positives and the ability to set automated response policies."};
            var controlFrameworkCategoryClause381 = new ControlFrameworkCategoryClause { Name = "Cloud based EDR", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid5, Reference = "5.6", Description = "Having a cloud-based endpoint detection and response solution is the only way to ensure zero impact on endpoints. This solution should smoothly integrate with current systems and provide intuitive remote access to controls."};

            var theiaCatGuid6 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Patch & Vulnerability Management")!.Id;
            var controlFrameworkCategoryClause382 = new ControlFrameworkCategoryClause { Name = "Vulnerability Analysis", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.1", Description = "Conduct a vulnerability analysis, define the scope of assets, inform stakeholders and asset owners, and plan vulnerability scans."};
            var controlFrameworkCategoryClause383 = new ControlFrameworkCategoryClause { Name = "Identify and Detect Vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.2", Description = "Identification and detection of vulnerabilities. This can be achieved through a vulnerability scan."};
            var controlFrameworkCategoryClause384 = new ControlFrameworkCategoryClause { Name = "Vulnerability Remediation", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.3", Description = "Depending on the remediation, such as a patch or a change in configuration, software restrictions, and availability of solutions, different options can arise including mitigation by implementing remediating actions."};
            var controlFrameworkCategoryClause385 = new ControlFrameworkCategoryClause { Name = "Vulnerability Exception Process", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.4", Description = "Launching an exception process and investigating potential indicators of compromise (IOC)."};
            var controlFrameworkCategoryClause386 = new ControlFrameworkCategoryClause { Name = "Implementation of Defined Vulnerability Actions", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.5", Description = "Deployment of the tasks identified in the previous activities."};
            var controlFrameworkCategoryClause387 = new ControlFrameworkCategoryClause { Name = "Monitoring of vulnerabilities", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid6, Reference = "6.6", Description = "As new vulnerabilities arise every minute, committing to real continuous monitoring is essential to properly manage them."};

            var theiaCatGuid7 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Incident Response Plans")!.Id;
            var controlFrameworkCategoryClause388 = new ControlFrameworkCategoryClause { Name = "Cyber incident handling within IRP", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid7, Reference = "7.1", Description = "The incident response plan must contain defined processes and procedures for performing cyber incident handling, reporting, and recovery"};
            var controlFrameworkCategoryClause389 = new ControlFrameworkCategoryClause { Name = "Defined incident response team", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid7, Reference = "7.2", Description = "The incident response team members’ roles, tasks, and responsibilities during a security incident must be clearly defined. Additionally, strong definitions of escalation paths and decision making processes/responsibilities are obligatory."};
            var controlFrameworkCategoryClause390 = new ControlFrameworkCategoryClause { Name = "IRP external processes", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid7, Reference = "7.3", Description = "The parts of incident response that will be covered externally should be planned and documented, and the relevant contact information noted."};
           
            var theiaCatGuid8 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Phishing & Awareness Training")!.Id;
            var controlFrameworkCategoryClause391 = new ControlFrameworkCategoryClause { Name = "Training and testing roadmaps", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.1", Description = "Perform an annual analysis to identify gaps in their cybersecurity skillset and develop and implement training roadmaps and/or project plans to close identified gaps."};
            var controlFrameworkCategoryClause392 = new ControlFrameworkCategoryClause { Name = "Annual Training", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.2", Description = "Establish annual cybersecurity training and a cybersecurity awareness program that: is mandatory for all employees and third party partners; trains users to avoid common cyber thrests (e.g. social engineering and phishing); and provides at least annual updated content on latest attack techniques"};
            var controlFrameworkCategoryClause393 = new ControlFrameworkCategoryClause { Name = "Phishing Campaigns conducted", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.3", Description = "Conduct, at least annually, internal phishing campaigns"};
            var controlFrameworkCategoryClause394 = new ControlFrameworkCategoryClause { Name = "Report Suspicious Emails", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.4", Description = "Have a process to report suspicious emails to an internal security team to investigate"};
            var controlFrameworkCategoryClause395 = new ControlFrameworkCategoryClause { Name = "Resond to phishing campaings", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.5", Description = "Have a process to respond to phishing campaigns."};
            var controlFrameworkCategoryClause396 = new ControlFrameworkCategoryClause { Name = "Tag external emails", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid8, Reference = "8.6", Description = "Tag external emails to alert employees that the message originated from outside the organization."};

            var theiaCatGuid9 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "RDP Mitigation & Hardening")!.Id;
            var controlFrameworkCategoryClause397 = new ControlFrameworkCategoryClause { Name = "User and access management", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.1", Description = ""};
            var controlFrameworkCategoryClause398 = new ControlFrameworkCategoryClause { Name = "Password policies", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.2", Description = ""};
            var controlFrameworkCategoryClause399 = new ControlFrameworkCategoryClause { Name = "Secure services and protocols", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.3", Description = ""};
            var controlFrameworkCategoryClause400 = new ControlFrameworkCategoryClause { Name = "Firewall configurations", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.4", Description = ""};
            var controlFrameworkCategoryClause401 = new ControlFrameworkCategoryClause { Name = "Network configurations", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.5", Description = ""};
            var controlFrameworkCategoryClause402 = new ControlFrameworkCategoryClause { Name = "Remote access", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.6", Description = ""};
            var controlFrameworkCategoryClause403 = new ControlFrameworkCategoryClause { Name = "Log management and audit policies", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.7", Description = ""};
            var controlFrameworkCategoryClause404 = new ControlFrameworkCategoryClause { Name = "Antivirus/antimalware protections", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.8", Description = ""};
            var controlFrameworkCategoryClause405 = new ControlFrameworkCategoryClause { Name = "Application control", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.9", Description = ""};
            var controlFrameworkCategoryClause406 = new ControlFrameworkCategoryClause { Name = "Security updates", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.10", Description = ""};
            var controlFrameworkCategoryClause407 = new ControlFrameworkCategoryClause { Name = "Encryption", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid9, Reference = "9.11", Description = ""};

            var theiaCatGuid10 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Logging & Monitoring")!.Id;
            var controlFrameworkCategoryClause408 = new ControlFrameworkCategoryClause { Name = "Audit Logs for Monitoring", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid10, Reference = "10.1", Description = ""};
            var controlFrameworkCategoryClause409 = new ControlFrameworkCategoryClause { Name = "Intergrated SIEM", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid10, Reference = "10.2", Description = "Implement a security incident and event management system (SIEM) and integrate the main platforms into this system. Logs should be accessible for at least the last three months and backed up for a minimum of one year."};
            var controlFrameworkCategoryClause410 = new ControlFrameworkCategoryClause { Name = "SIEM logs analysed", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid10, Reference = "10.3", Description = "Analyze the logs in the network and define a set of use cases or common patterns that the organization would like to monitor and react to, in the instance that they are found. The information should also be used alongside threat intelligence information."};
            var controlFrameworkCategoryClause411 = new ControlFrameworkCategoryClause { Name = "Privileged user access review", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid10, Reference = "10.4", Description = "Define processes for reviewing, the administrators’ or highprivileged users’ activities on critical systems."};
            var controlFrameworkCategoryClause412 = new ControlFrameworkCategoryClause { Name = "Trained specialised SIEM Team", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid10, Reference = "10.5", Description = "Define and train a team of professionals specialized in the monitoring of security events and incident response."};

            var theiaCatGuid11 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "End-of-Life Management")!.Id;
            var controlFrameworkCategoryClause413 = new ControlFrameworkCategoryClause { Name = "EoL systems replaced or safeguarded", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid11, Reference = "11.1", Description = "Organizations should cease using, or safeguard obsolete products. Acess should be restricted and network air gaps or discrete network firewalls and monitoring of obsolete networks should be carried out."};
            var controlFrameworkCategoryClause414 = new ControlFrameworkCategoryClause { Name = "Segregation of EoL systems", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid11, Reference = "11.2", Description = "Steps taken to limit the potential impact of compromise, such as preventing those EOL systems from accessing or storing critical and sensitive data or systems, meaning that a compromise of the EOL device would not be as damaging."};
            var controlFrameworkCategoryClause415 = new ControlFrameworkCategoryClause { Name = "Overhaul or replacement of legacy estates", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid11, Reference = "11.3", Description = "For organizations with significant legacy estates and operational technology systems, an EOL product may mean that the whole system needs to be overhauled, upgraded, or replaced"};
            var controlFrameworkCategoryClause416 = new ControlFrameworkCategoryClause { Name = "Appropriate management and protection of EoL products", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid11, Reference = "11.4", Description = "Where organizations opt to continue to use the EOL product, implemententation of the necessay protection and risk mitigation steps in collaboration with the IT, OT security teams and external experts is necessary."};

            var theiaCatGuid12 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Digital Supply Chain Risk")!.Id;
            var controlFrameworkCategoryClause417 = new ControlFrameworkCategoryClause { Name = "Digital supply chain risk management framework", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid12, Reference = "12.1", Description = "Adopt a digital supply chain risk management framework, including risk rating of first tier vendors/suppliers, based on an advanced risk quantification"};
            var controlFrameworkCategoryClause418 = new ControlFrameworkCategoryClause { Name = "Implement a cybersecurity framework for Supply Chain", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid12, Reference = "12.2", Description = "Implement a cybersecurity framework including:  Account management based on “zero trust” expectations and the “need-to-know” principle. Enforce risk-based MFA, Engagement with internal SOC to develop use cases for third party accesses."};
            var controlFrameworkCategoryClause419 = new ControlFrameworkCategoryClause { Name = "Incident response playbook with supply chain scenario", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid12, Reference = "12.3", Description = "Develop and test an incident response playbook for vendor/digital supply chain scenarios and include third parties in this playbook."};
            var controlFrameworkCategoryClause420 = new ControlFrameworkCategoryClause { Name = "Vendor contract and protocol assessment", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid12, Reference = "12.4", Description = "Assess contracts, service agreements, and escalation protocols for each vendor or digital supplier."};
            var controlFrameworkCategoryClause421 = new ControlFrameworkCategoryClause { Name = "Appropriate cybersecurity hygiene controls for contracts", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = theiaCatGuid12, Reference = "12.5", Description = "Engage with the procurement department to include appropriate cybersecurity hygiene controls and responsibilities in new contracts and renewals, including security trainings and certifications."};

            
            
            var alliantSupplierCatGuid1 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Miscellaneous")!.Id;
            var controlFrameworkCategoryClause422 = new ControlFrameworkCategoryClause { Name = "Do you store, create, manage, or access Client Business Confidential Information?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.1", Description = ""};
            var controlFrameworkCategoryClause423 = new ControlFrameworkCategoryClause { Name = "Do you store, create, manage, or access Client's Employees' Personally Identifiable Information?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.2", Description = ""};
            var controlFrameworkCategoryClause424 = new ControlFrameworkCategoryClause { Name = "Does the company store, create, manage, or access Client's Investors' or Customers' Personally Identifiable Information?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.3", Description = ""};
            var controlFrameworkCategoryClause425 = new ControlFrameworkCategoryClause { Name = "How many individuals within the company are dedicated to Information Security?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.4", Description = ""};
            var controlFrameworkCategoryClause426 = new ControlFrameworkCategoryClause { Name = "Does the company have any technology or security certifications such as ISO 27001 or SOC 2? If so please attach.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.5", Description = ""};
            var controlFrameworkCategoryClause427 = new ControlFrameworkCategoryClause { Name = "Has the company ever experienced a cybersecurity event that led to one or more of the following: public disclosure, communication to clients, monetary loss, or a system outage that caused a significant business disruption?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.6", Description = ""};
            var controlFrameworkCategoryClause428 = new ControlFrameworkCategoryClause { Name = "Activity of third-party service providers with access to the company's network is monitored.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.7", Description = ""};
            var controlFrameworkCategoryClause429 = new ControlFrameworkCategoryClause { Name = "Resiliency requirements to support delivery of critical services have been established.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.8", Description = ""};
            var controlFrameworkCategoryClause430 = new ControlFrameworkCategoryClause { Name = "The physical environment is monitored to detect potential cybersecurity events.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.9", Description = ""};
            var controlFrameworkCategoryClause431 = new ControlFrameworkCategoryClause { Name = "Computers and mobile devices connected to the company's environment must meet security hygiene requirements before being granted access (antivirus, patch levels, etc.).", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.11", Description = ""};
            var controlFrameworkCategoryClause432 = new ControlFrameworkCategoryClause { Name = "Cybersecurity roles and responsibilities have been established.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.12", Description = ""};
            var controlFrameworkCategoryClause433 = new ControlFrameworkCategoryClause { Name = "External consultants have been hired to review information security controls.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.13", Description = ""};
            var controlFrameworkCategoryClause434 = new ControlFrameworkCategoryClause { Name = "A written information security policy is maintained.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.14", Description = ""};
            var controlFrameworkCategoryClause435 = new ControlFrameworkCategoryClause { Name = "Work-from-home confidentiality policies which are equivalent to those established for in-office work (e.g. clean desk policy, secure handling of sensitive/confidential information, etc.) have been established. Please describe.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.15", Description = ""};
            var controlFrameworkCategoryClause436 = new ControlFrameworkCategoryClause { Name = "Cybersecurity training is provided for all employees annually.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.16", Description = ""};
            var controlFrameworkCategoryClause437 = new ControlFrameworkCategoryClause { Name = "Are you utilizing pixel tracking (such as Meta Pixel or Google Tag Manager) on any websites or marketing operated by you or on your behalf? This includes any data collected that could be impacted by the Video Privacy Protection Act (VPPA).", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid1, Reference = "1.17", Description = ""};
                        
            var alliantSupplierCatGuid2 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Endpoint")!.Id;
            var controlFrameworkCategoryClause438 = new ControlFrameworkCategoryClause { Name = "Remote access is available to employees via public internet.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.1", Description = ""};
            var controlFrameworkCategoryClause439 = new ControlFrameworkCategoryClause { Name = "Remote access is available to employees via virtual desktop technology such as Citrix or Remote Desktop.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.2", Description = ""};
            var controlFrameworkCategoryClause440 = new ControlFrameworkCategoryClause { Name = "Remote access is available to employees via VPN.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.3", Description = ""};
            var controlFrameworkCategoryClause441 = new ControlFrameworkCategoryClause { Name = "All hardware and software is inventoried.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.4", Description = ""};
            var controlFrameworkCategoryClause442 = new ControlFrameworkCategoryClause { Name = "Hard disk encryption is mandated for laptops.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.5", Description = ""};
            var controlFrameworkCategoryClause443 = new ControlFrameworkCategoryClause { Name = "Remote wipe capabilities have been implemented on mobile devices used for business purposes.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.6", Description = ""};
            var controlFrameworkCategoryClause444 = new ControlFrameworkCategoryClause { Name = "Data-at-rest is encrypted.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.7", Description = ""};
            var controlFrameworkCategoryClause445 = new ControlFrameworkCategoryClause { Name = "Data-in-transit is encrypted.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.8", Description = ""};
            var controlFrameworkCategoryClause446 = new ControlFrameworkCategoryClause { Name = "Removable media usage is disabled.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.9", Description = ""};
            var controlFrameworkCategoryClause447 = new ControlFrameworkCategoryClause { Name = "Removable media usage is prohibited by policy.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.10", Description = ""};
            var controlFrameworkCategoryClause448 = new ControlFrameworkCategoryClause { Name = "What percentage of your endpoints have EDR tools deployed?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.11", Description = ""};
            var controlFrameworkCategoryClause449 = new ControlFrameworkCategoryClause { Name = "Passwords requirements are enforced on mobile devices used for business purposes.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.12", Description = ""};
            var controlFrameworkCategoryClause450 = new ControlFrameworkCategoryClause { Name = "Employees do not have local administrative rights to their computers.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.13", Description = ""};
            var controlFrameworkCategoryClause451 = new ControlFrameworkCategoryClause { Name = "Is MFA required for personal devices/assets to access email, applications, etc.?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.14", Description = ""};
            var controlFrameworkCategoryClause452 = new ControlFrameworkCategoryClause { Name = "Controls are in place to prevent exfiltration of sensitive data during remote sessions.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid2, Reference = "2.15", Description = ""};

            var alliantSupplierCatGuid3 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Server")!.Id;
            var controlFrameworkCategoryClause453 = new ControlFrameworkCategoryClause { Name = "Certificates and other hardening techniques are in use for all internet-facing servers.", Weighting = 3, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid3, Reference = "3.1", Description = ""};

            var alliantSupplierCatGuid4 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Monitoring")!.Id;
            var controlFrameworkCategoryClause454 = new ControlFrameworkCategoryClause { Name = "Alerts are generated and responded to when abnormal remote login activity is detected.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.1", Description = ""};
            var controlFrameworkCategoryClause455 = new ControlFrameworkCategoryClause { Name = "All network devices and systems have logging enabled.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.2", Description = ""};
            var controlFrameworkCategoryClause456 = new ControlFrameworkCategoryClause { Name = "Network activity logs are reviewed on a regular basis.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.3", Description = ""};
            var controlFrameworkCategoryClause457 = new ControlFrameworkCategoryClause { Name = "Is 24/7 monitoring available (i.e., through a first party SOC, co-managed SOC, or MDR)?", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.4", Description = ""};
            var controlFrameworkCategoryClause458 = new ControlFrameworkCategoryClause { Name = "Monitoring for unauthorized personnel, connections, devices, and software is performed.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.5", Description = ""};
            var controlFrameworkCategoryClause459 = new ControlFrameworkCategoryClause { Name = "Are system logs stored for at least 60 days?", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid4, Reference = "4.6", Description = ""};

            var alliantSupplierCatGuid5 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "IAM")!.Id;
            var controlFrameworkCategoryClause460 = new ControlFrameworkCategoryClause { Name = "Is a PAM tool deployed in your environment?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid5, Reference = "5.1", Description = ""};
            var controlFrameworkCategoryClause461 = new ControlFrameworkCategoryClause { Name = "Formal password policies are enforced on all user accounts.", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid5, Reference = "5.2", Description = ""};
            var controlFrameworkCategoryClause462 = new ControlFrameworkCategoryClause { Name = "Access permissions incorporate the principles of least privilege and separation of duties.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid5, Reference = "5.3", Description = ""};
            var controlFrameworkCategoryClause463 = new ControlFrameworkCategoryClause { Name = "Access rights are reviewed at least annually.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid5, Reference = "5.4", Description = ""};

            var alliantSupplierCatGuid6 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Network")!.Id;
            var controlFrameworkCategoryClause464 = new ControlFrameworkCategoryClause { Name = "Multifactor authentication is required for remote access to the network, applications, systems, and software in which  confidential information is available.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid6, Reference = "6.1", Description = ""};
            var controlFrameworkCategoryClause465 = new ControlFrameworkCategoryClause { Name = "Website filtering is in place to protect endpoints from accessing malicious websites.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid6, Reference = "6.2", Description = ""};
            var controlFrameworkCategoryClause466 = new ControlFrameworkCategoryClause { Name = "Intrusion detection and prevention solutions have been implemented.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid6, Reference = "6.3", Description = ""};
            var controlFrameworkCategoryClause467 = new ControlFrameworkCategoryClause { Name = "Personal computers utilize a secure remote connection (i.e., VPN/Citrix/etc.) with multifactor authentication enabled for network access.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid6, Reference = "6.4", Description = ""};
            var controlFrameworkCategoryClause468 = new ControlFrameworkCategoryClause { Name = "If applicable, is a full tunnel configuration in place for VPN access?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid6, Reference = "6.5", Description = ""};

            var alliantSupplierCatGuid7 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Backup")!.Id;
            var controlFrameworkCategoryClause469 = new ControlFrameworkCategoryClause { Name = "Client data is backed up and maintained in a secure location.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid7, Reference = "7.1", Description = ""};
            var controlFrameworkCategoryClause470 = new ControlFrameworkCategoryClause { Name = "The effectiveness of data backup processes is tested.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid7, Reference = "7.2", Description = ""};

            var alliantSupplierCatGuid8 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "IR,BCP & DR")!.Id;
            var controlFrameworkCategoryClause471 = new ControlFrameworkCategoryClause { Name = "After how many hours/days are clients notified of a breach?", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.1", Description = ""};
            var controlFrameworkCategoryClause472 = new ControlFrameworkCategoryClause { Name = "A Business Continuity Plan is maintained.", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.2", Description = ""};
            var controlFrameworkCategoryClause473 = new ControlFrameworkCategoryClause { Name = "A Disaster Recovery Plan is maintained.", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.3", Description = ""};
            var controlFrameworkCategoryClause474 = new ControlFrameworkCategoryClause { Name = "Disaster recovery and business continuity processes and procedures are tested annually.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.4", Description = ""};
            var controlFrameworkCategoryClause475 = new ControlFrameworkCategoryClause { Name = "Policies and procedures for securely destroying and disposing of paper documents are in place.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.5", Description = ""};
            var controlFrameworkCategoryClause476 = new ControlFrameworkCategoryClause { Name = "Policies and procedures for securely destroying and disposing of physical devices (computers, hard drives, etc.) are in place.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.6", Description = ""};
            var controlFrameworkCategoryClause477 = new ControlFrameworkCategoryClause { Name = "A Data Loss Prevention (DLP) solution or compensating controls to protect against data leaks are in place. If compensating controls are in place, please describe.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.7", Description = ""};
            var controlFrameworkCategoryClause478 = new ControlFrameworkCategoryClause { Name = "The business is able to maintain service continuity with a fully remote workforce for an extended period of time.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.8", Description = ""};
            var controlFrameworkCategoryClause479 = new ControlFrameworkCategoryClause { Name = "Incident response procedures are established and documented.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.9", Description = ""};
            var controlFrameworkCategoryClause480 = new ControlFrameworkCategoryClause { Name = "Incident response and recovery procedures are tested annually.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid8, Reference = "8.10", Description = ""};

            var alliantSupplierCatGuid9 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Email")!.Id;
            var controlFrameworkCategoryClause481 = new ControlFrameworkCategoryClause { Name = "Multifactor authentication is required for email access.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.1", Description = ""};
            var controlFrameworkCategoryClause482 = new ControlFrameworkCategoryClause { Name = "Are macros automatically disabled?", Weighting = 3, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.2", Description = ""};
            var controlFrameworkCategoryClause483 = new ControlFrameworkCategoryClause { Name = "Are emails coming from external sources flagged?", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.3", Description = ""};
            var controlFrameworkCategoryClause484 = new ControlFrameworkCategoryClause { Name = "What is the frequency of phishing tests?", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.4", Description = ""};
            var controlFrameworkCategoryClause485 = new ControlFrameworkCategoryClause { Name = "What is the failure rate for phishing tests?", Weighting = 4.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.5", Description = ""};
            var controlFrameworkCategoryClause486 = new ControlFrameworkCategoryClause { Name = "Are all legacy authentication methods and protocols disabled?", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid9, Reference = "9.6", Description = ""};

            var alliantSupplierCatGuid10 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Vuln & Change Mgmt")!.Id;
            var controlFrameworkCategoryClause487 = new ControlFrameworkCategoryClause { Name = "Configuration and permission change control processes are in place.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid10, Reference = "10.1", Description = ""};
            var controlFrameworkCategoryClause488 = new ControlFrameworkCategoryClause { Name = "What % of the environment is covered by vulnerability scans?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid10, Reference = "10.2", Description = ""};
            var controlFrameworkCategoryClause489 = new ControlFrameworkCategoryClause { Name = "Newly identified vulnerabilities are mitigated or documented as accepted risks.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid10, Reference = "10.3", Description = ""};
            var controlFrameworkCategoryClause490 = new ControlFrameworkCategoryClause { Name = "Vulnerability scans are performed on an ongoing basis.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid10, Reference = "10.4", Description = ""};

            var alliantSupplierCatGuid11 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Penetration Tests")!.Id;
            var controlFrameworkCategoryClause491 = new ControlFrameworkCategoryClause { Name = "Penetration tests are performed annually.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid11, Reference = "11.1", Description = ""};

            var alliantSupplierCatGuid12 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "End-of-Life Mgmt")!.Id;
            var controlFrameworkCategoryClause492 = new ControlFrameworkCategoryClause { Name = "Is the network free from any/all end-of-life (EoL) systems or software?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid12, Reference = "12.1", Description = ""};
            var controlFrameworkCategoryClause493 = new ControlFrameworkCategoryClause { Name = "Is network segmentation used to protect EoL systems and applications?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid12, Reference = "12.2", Description = ""};
            
            var alliantSupplierCatGuid13 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Risk Mgmt")!.Id;
            var controlFrameworkCategoryClause494 = new ControlFrameworkCategoryClause { Name = "The security posture of sub-contractors or third parties with access to customer data is reviewed annually.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid13, Reference = "13.1", Description = ""};

            var alliantSupplierCatGuid14 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Operational Technology")!.Id;
            var controlFrameworkCategoryClause495 = new ControlFrameworkCategoryClause { Name = "Does the organization have an OT or IOT environment?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid14, Reference = "14.1", Description = ""};
            var controlFrameworkCategoryClause496 = new ControlFrameworkCategoryClause { Name = "Is the OT/IOT environment segmented from the business network with traffic filtering between the environments?", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid14, Reference = "14.2", Description = ""};
            var controlFrameworkCategoryClause497 = new ControlFrameworkCategoryClause { Name = "Is the OT environment monitored for malicious activity? (i.e., SIEM, Attack Surface Management tools, logging, etc.) - include how this is done", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid14, Reference = "14.3", Description = ""};

            var alliantSupplierCatGuid15 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Secure Software Dev")!.Id;
            var controlFrameworkCategoryClause498 = new ControlFrameworkCategoryClause { Name = "If the insured develops software in-house, are any of the following utilized: SAST, DAST, SCA, and/or SBOM? (Include which)", Weighting = 3, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid15, Reference = "15.1", Description = ""};

            var alliantSupplierCatGuid16 = _dbContext.ControlFrameworkCategories.FirstOrDefault(r => r.Name == "Privacy")!.Id;
            var controlFrameworkCategoryClause499 = new ControlFrameworkCategoryClause { Name = "The Privacy Notice describes the method for which individuals can update their preferences on how their personal information will be used, including whether or not and to whom it is disclosed.", Weighting = 5, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.1", Description = ""};
            var controlFrameworkCategoryClause500 = new ControlFrameworkCategoryClause { Name = "Clients are provided with a Privacy Policy/Notice explaining privacy policies and practices.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.2", Description = ""};
            var controlFrameworkCategoryClause501 = new ControlFrameworkCategoryClause { Name = "Clients are promptly notified of updates to the Privacy Policy/Notice.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.3", Description = ""};
            var controlFrameworkCategoryClause502 = new ControlFrameworkCategoryClause { Name = "The  Privacy Notice details the types of personal information collected from individuals and the purposes for which the data is collected.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.4", Description = ""};
            var controlFrameworkCategoryClause503 = new ControlFrameworkCategoryClause { Name = "Data collected and shared with third parties is stated in the Privacy Notice along with information regarding the third parties' relationship with the Company", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.5", Description = ""};
            var controlFrameworkCategoryClause504 = new ControlFrameworkCategoryClause { Name = "Personal information collected by the Company is limited to the information necessary and relevant to the purposes for which that information was collected, and collected based on what is described in the Privacy Notice", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.6", Description = ""};
            var controlFrameworkCategoryClause505 = new ControlFrameworkCategoryClause { Name = "Steps are taken to ensure the accuracy and completeness of personal information received directly from an individual or a third-party (e.g., edit and validation controls, forced completion of mandatory fields)", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.7", Description = ""};
            var controlFrameworkCategoryClause506 = new ControlFrameworkCategoryClause { Name = "Employees receive training on the Company's privacy and data protection policies upon hire and on an ongoing basis", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.8", Description = ""};
            var controlFrameworkCategoryClause507 = new ControlFrameworkCategoryClause { Name = "Personal information is systematically destroyed, erased, or anonymized when it is no longer legally required to be retained or to fulfill the purpose(s) for which it was collected.", Weighting = 3.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.9", Description = ""};
            var controlFrameworkCategoryClause508 = new ControlFrameworkCategoryClause { Name = "Clients are promptly notified if their personal information is transferred to a new third-party and given information regarding the third-party.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.10", Description = ""};
            var controlFrameworkCategoryClause509 = new ControlFrameworkCategoryClause { Name = "Individuals are able to withdraw consent to privacy policies they previously consented to.", Weighting = 2.5m, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.11", Description = ""};
            var controlFrameworkCategoryClause510 = new ControlFrameworkCategoryClause { Name = "Policies are documented which detail the technical, administrative, and physical safeguards required for protection of personal information, as well as response procedures in the event of a data privacy related incident.", Weighting = 4, IsActive = true, ControlFrameworkCategoryId = alliantSupplierCatGuid16, Reference = "16.12", Description = ""};
            
            
            
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause1);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause2);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause3);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause4);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause5);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause6);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause7);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause8);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause9);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause10);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause11);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause12);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause13);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause14);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause15);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause16);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause17);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause18);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause19);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause20);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause21);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause22);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause23);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause24);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause25);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause26);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause27);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause28);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause29);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause30);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause31);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause32);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause33);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause34);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause35);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause36);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause37);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause38);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause39);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause40);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause41);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause42);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause43);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause44);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause45);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause46);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause47);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause48);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause49);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause50);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause51);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause52);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause53);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause54);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause55);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause56);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause57);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause58);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause59);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause60);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause61);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause62);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause63);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause64);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause65);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause66);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause67);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause68);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause69);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause70);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause71);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause72);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause73);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause74);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause75);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause76);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause77);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause78);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause79);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause80);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause81);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause82);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause83);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause84);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause85);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause86);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause87);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause88);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause89);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause90);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause91);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause92);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause93);

            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause94);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause95);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause96);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause97);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause98);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause99);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause100);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause101);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause102);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause103);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause104);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause105);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause106);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause107);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause108);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause109);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause110);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause111);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause112);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause113);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause114);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause115);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause116);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause117);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause118);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause119);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause120);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause121);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause122);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause123);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause124);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause125);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause126);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause127);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause128);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause129);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause130);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause131);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause132);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause133);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause134);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause135);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause136);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause137);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause138);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause139);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause140);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause141);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause142);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause143);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause144);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause145);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause146);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause147);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause148);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause149);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause150);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause151);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause152);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause153);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause154);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause155);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause156);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause157);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause158);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause159);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause160);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause161);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause162);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause163);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause164);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause165);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause166);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause167);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause168);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause169);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause170);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause171);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause172);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause173);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause174);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause175);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause176);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause177);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause178);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause179);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause180);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause181);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause182);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause183);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause184);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause185);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause186);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause187);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause188);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause189);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause190);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause191);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause192);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause193);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause194);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause195);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause196);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause197);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause198);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause199);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause200);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause201);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause202);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause203);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause204);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause205);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause206);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause207);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause208);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause209);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause210);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause211);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause212);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause213);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause214);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause215);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause216);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause217);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause218);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause219);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause220);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause221);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause222);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause223);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause224);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause225);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause226);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause227);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause228);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause229);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause230);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause231);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause232);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause233);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause234);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause235);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause236);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause237);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause238);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause239);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause240);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause241);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause242);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause243);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause244);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause245);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause246);

            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause247);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause248);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause249);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause250);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause251);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause252);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause253);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause254);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause255);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause256);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause257);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause258);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause259);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause260);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause261);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause262);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause263);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause264);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause265);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause266);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause267);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause268);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause269);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause270);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause271);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause272);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause273);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause274);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause275);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause276);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause277);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause278);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause279);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause280);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause281);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause282);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause284);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause285);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause286);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause287);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause288);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause289);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause290);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause291);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause292);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause293);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause294);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause295);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause296);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause297);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause298);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause299);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause300);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause301);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause302);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause303);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause304);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause305);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause306);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause307);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause308);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause309);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause310);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause311);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause312);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause313);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause314);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause315);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause316);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause317);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause318);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause319);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause320);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause321);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause322);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause323);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause324);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause325);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause326);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause327);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause328);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause329);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause330);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause331);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause332);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause333);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause334);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause335);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause336);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause337);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause338);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause339);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause340);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause341);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause342);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause343);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause344);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause345);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause346);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause347);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause348);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause349);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause350);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause351);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause352);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause353);

            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause354);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause355);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause356);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause357);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause358);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause359);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause360);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause361);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause362);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause363);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause364);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause365);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause366);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause367);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause368);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause369);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause370);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause371);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause372);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause373);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause374);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause375);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause376);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause377);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause378);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause379);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause380);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause381);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause382);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause383);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause384);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause385);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause386);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause387);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause388);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause389);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause390);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause391);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause392);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause393);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause394);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause395);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause396);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause397);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause398);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause399);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause400);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause401);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause402);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause403);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause404);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause405);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause406);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause407);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause408);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause409);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause410);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause411);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause412);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause413);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause414);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause415);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause416);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause417);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause418);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause419);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause420);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause421);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause422);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause423);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause424);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause425);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause426);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause427);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause428);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause429);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause430);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause431);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause432);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause433);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause434);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause435);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause436);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause437);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause438);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause439);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause440);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause441);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause442);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause443);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause444);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause445);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause446);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause447);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause448);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause449);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause450);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause451);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause452);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause453);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause454);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause455);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause456);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause457);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause458);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause459);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause460);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause461);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause462);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause463);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause464);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause465);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause466);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause467);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause468);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause469);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause470);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause471);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause472);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause473);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause474);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause475);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause476);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause477);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause478);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause479);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause480);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause481);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause482);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause483);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause484);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause485);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause486);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause487);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause488);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause489);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause490);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause491);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause492);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause493);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause494);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause495);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause496);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause497);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause498);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause499);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause500);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause501);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause502);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause503);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause504);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause505);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause506);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause507);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause508);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause509);
            _dbContext.ControlFrameworkCategoryClauses.Add(controlFrameworkCategoryClause510);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SeedApplicationForms()
    {
        await _dbContext.SaveChangesAsync();
    }

    public async Task SeedTenants()
    {
        TenantType[] existingTenantsTypes = await _dbContext.Tenants.Select(x => x.Type).ToArrayAsync();
        
        if (!existingTenantsTypes.Contains(TenantType.Supplier))
        {
            _dbContext.Tenants.Add(new Tenant
            {
                Name = TenantConstants.SuppliersTenantName,
                Type = TenantType.Supplier
            });
        }

        if (!existingTenantsTypes.Contains(TenantType.Admin))
        {
            _dbContext.Tenants.Add(new Tenant
            {
                Name = _configReaderService.GetAppClientsOptions().AdminAppSubdomain,
                Type = TenantType.Admin
            });
        }

        await _dbContext.SaveChangesAsync();
    }
}