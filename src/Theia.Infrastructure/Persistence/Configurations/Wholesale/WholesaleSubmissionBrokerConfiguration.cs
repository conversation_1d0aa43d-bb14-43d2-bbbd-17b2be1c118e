using Theia.Domain.Entities.Organisations.Submissions.Wholesale;

namespace Theia.Infrastructure.Persistence.Configurations.Wholesale;

public class WholesaleSubmissionBrokerConfiguration : IEntityTypeConfiguration<WholesaleSubmissionBroker>
{
    public void Configure(EntityTypeBuilder<WholesaleSubmissionBroker> builder)
    {
        builder.<PERSON>Key(x => new {x.WholesaleSubmissionId, x.BrokerId});

        builder.HasOne(x => x.Broker)
            .WithMany(b => b.WholesaleSubmissions)
            .HasForeignKey(x => x.BrokerId)
            .IsRequired();

        builder.HasOne(x => x.WholesaleSubmission)
            .WithMany(s => s.Brokers)
            .HasForeignKey(x => x.WholesaleSubmissionId)
            .IsRequired();
    }
}