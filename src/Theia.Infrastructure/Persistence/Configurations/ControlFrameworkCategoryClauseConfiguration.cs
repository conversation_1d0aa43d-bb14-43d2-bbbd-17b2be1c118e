using Theia.Domain.Entities.ControlFrameworks;

namespace Theia.Infrastructure.Persistence.Configurations;

public sealed class ControlFrameworkCategoryClauseConfiguration : IEntityTypeConfiguration<ControlFrameworkCategoryClause>
{
    public void Configure(EntityTypeBuilder<ControlFrameworkCategoryClause> builder)
    {
        builder.Property(c => c.Reference).HasMaxLength(12);
        builder.HasOne(c => c.ControlFrameworkCategory)
            .WithMany(cfc => cfc.ControlFrameworkCategoryClauses)
            .HasForeignKey(c => c.ControlFrameworkCategoryId)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);
    }
}