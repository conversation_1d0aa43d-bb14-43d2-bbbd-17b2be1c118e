using Theia.Domain.Entities.Organisations.Submissions;

namespace Theia.Infrastructure.Persistence.Configurations.Submissions;

public class SubmissionQuestionAnswerConfiguration : IEntityTypeConfiguration<SubmissionQuestionAnswer>
{
    public void Configure(EntityTypeBuilder<SubmissionQuestionAnswer> builder)
    {
        builder.<PERSON><PERSON>ey(sqa => sqa.Id);
        builder.Property(sqa => sqa.AnsweredAt).IsRequired();
        builder.Property(sqa => sqa.Content).IsRequired().HasMaxLength(500);

        builder.HasOne(sqa => sqa.AnsweredBy)
            .WithMany(u => u.SubmissionQuestionAnswers)
            .HasForeignKey(sqa => sqa.AnsweredById)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(sqa => sqa.Question)
            .WithMany(q => q.Answers)
            .HasForeignKey(sqa => sqa.QuestionId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade); 
    }
}