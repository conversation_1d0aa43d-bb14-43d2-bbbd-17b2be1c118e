using Theia.Domain.Entities.Organisations.Submissions;
using Theia.Domain.Entities.Organisations.Submissions.Organisation;
using Theia.Domain.Entities.Organisations.Submissions.Wholesale;

namespace Theia.Infrastructure.Persistence.Configurations.Submissions;

public class SubmissionConfiguration : IEntityTypeConfiguration<Submission>
{
    public void Configure(EntityTypeBuilder<Submission> builder)
    {
        builder.HasOne(s => s.RequestedForOrganisation)
            .WithMany(s => s.Submissions)
            .HasForeignKey(s => s.RequestedForOrganisationId)
            .IsRequired();

        builder.HasOne(s => s.RequestedByTenant)
            .WithMany(s => s.RequestedSubmissions)
            .HasForeignKey(s => s.TenantRequestedById)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(s => s.RequestedByApplicationUser)
            .WithMany(u => u.RequestedSubmissions)
            .HasForeignKey(s => s.RequestedBy)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(s => s.SubmissionVoidedByApplicationUser)
            .WithMany(u => u.VoidedSubmission)
            .HasForeignKey(s => s.SubmissionVoidedBy)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.NoAction);
        
        builder
            .HasDiscriminator(x => x.SubmissionType)
            .HasValue<OrganisationSubmission>(SubmissionType.OrganisationSubmission)
            .HasValue<WholesaleSubmission>(SubmissionType.WholesaleBrokerSubmission);
    }
}