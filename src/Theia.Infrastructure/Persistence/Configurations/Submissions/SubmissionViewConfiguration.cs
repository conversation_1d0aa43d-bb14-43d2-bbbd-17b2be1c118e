using Theia.Domain.Entities.Organisations.Submissions;

namespace Theia.Infrastructure.Persistence.Configurations.Submissions;

public class SubmissionViewConfiguration : IEntityTypeConfiguration<SubmissionView>
{
    public void Configure(EntityTypeBuilder<SubmissionView> builder)
    {
        builder.<PERSON><PERSON><PERSON>(bv => new {bv.SubmissionId, bv.ViewedById});
        
        builder.<PERSON>One(bv => bv.Submission)
            .WithMany(sub => sub.ViewedBy)
            .HasForeignKey(bv => bv.SubmissionId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(bv => bv.ViewedBy)
            .WithMany(user => user.ViewedSubmissions)
            .HasForeignKey(bv => bv.ViewedById)
            .OnDelete(DeleteBehavior.Cascade);
    }
}