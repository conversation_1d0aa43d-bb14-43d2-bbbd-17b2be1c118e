using Theia.Domain.Entities.Organisations.Submissions.Wholesale;

namespace Theia.Infrastructure.Persistence.Configurations.Submissions.Wholesale;

public class WholesaleSubmissionConfiguration : IEntityTypeConfiguration<WholesaleSubmission>
{
    public void Configure(EntityTypeBuilder<WholesaleSubmission> builder)
    {
        builder.HasOne(x => x.IndicationRequest)
            .WithMany(x => x.WholesaleSubmissions)
            .HasForeignKey(x => x.IndicationRequestId)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.BrokingHouse)
            .WithMany()
            .HasForeignKey(x => x.BrokingHouseId)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);

        builder.Property(x => x.IsApproved).IsRequired().HasDefaultValue(false);
    }
}