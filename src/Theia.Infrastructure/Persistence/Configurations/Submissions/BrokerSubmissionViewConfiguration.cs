using Theia.Domain.Entities.Organisations.Submissions.BrokerSubmissions;

namespace Theia.Infrastructure.Persistence.Configurations.Submissions;

public class BrokerSubmissionViewConfiguration : IEntityTypeConfiguration<BrokerSubmissionView>
{
    public void Configure(EntityTypeBuilder<BrokerSubmissionView> builder)
    {
        builder.HasKey(x => new { x.BrokerSubmissionId, x.UserId });
        builder.HasOne(x => x.BrokerSubmission)
            .WithMany(x => x.Views)
            .HasForeignKey(x => x.BrokerSubmissionId);
    }
}