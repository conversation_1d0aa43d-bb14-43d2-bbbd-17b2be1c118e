using Theia.Domain.Entities.Notifications;

namespace Theia.Infrastructure.Persistence.Configurations.Notifications;

public class NotificationConfigurations : IEntityTypeConfiguration<Notification>
{
    public void Configure(EntityTypeBuilder<Notification> builder)
    {
        builder.HasMany(x => x.NotificationRecipients)
            .WithOne(x => x.Notification)
            .HasForeignKey(x => x.NotificationId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);
    }
}