using Theia.Domain.Entities.ControlFrameworks;

namespace Theia.Infrastructure.Persistence.Configurations;

public sealed class ControlFrameworkCategoryConfiguration : IEntityTypeConfiguration<ControlFrameworkCategory>
{
    public void Configure(EntityTypeBuilder<ControlFrameworkCategory> builder)
    {
        builder.Property(c => c.Reference).HasMaxLength(5);
        builder.HasOne(c => c.ControlFramework)
            .WithMany(cf => cf.ControlFrameworkCategories)
            .HasForeignKey(c => c.ControlFrameworkId)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);
    }
}