using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;

namespace Theia.Infrastructure.Persistence.Configurations.Suppliers;

public class OrganisationSupplierAssociationSupplierServiceConfiguration : IEntityTypeConfiguration<OrganisationSupplierAssociationSupplierService>
{
    public void Configure(EntityTypeBuilder<OrganisationSupplierAssociationSupplierService> builder)
    {
        builder.HasKey(x => new { x.OrganisationSupplierAssociationId, x.SupplierServiceId });

        builder.HasOne(x => x.SupplierService)
            .WithMany(x => x.OrganisationSupplierAssociationSupplierServices)
            .HasForeignKey(x => x.SupplierServiceId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.OrganisationSupplierAssociation)
            .WithMany(x => x.OrganisationSupplierAssociationSupplierServices)
            .HasForeignKey(x => x.OrganisationSupplierAssociationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}