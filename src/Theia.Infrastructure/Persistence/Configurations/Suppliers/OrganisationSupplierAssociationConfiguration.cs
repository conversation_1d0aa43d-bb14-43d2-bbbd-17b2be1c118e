using Theia.Domain.Entities.Organisations.Suppliers;
using Theia.Domain.Entities.Suppliers;

namespace Theia.Infrastructure.Persistence.Configurations.Suppliers;

public class OrganisationSupplierAssociationConfiguration : IEntityTypeConfiguration<OrganisationSupplierAssociation>
{
    public void Configure(EntityTypeBuilder<OrganisationSupplierAssociation> builder)
    {
        builder.Has<PERSON>ey(osa => osa.Id);

        builder.HasOne(osa => osa.Organisation)
            .WithMany(org => org.Suppliers)
            .HasForeignKey(osa => osa.OrganisationId)
            .IsRequired();

        builder.HasOne(osa => osa.Supplier)
            .WithMany(s => s.Organisations)
            .HasForeignKey(osa => osa.SupplierId)
            .IsRequired();
    }
}