using Theia.Domain.Entities.Indications;

namespace Theia.Infrastructure.Persistence.Configurations.Indications;

public class OptionHeadsOfCoversConfiguration : IEntityTypeConfiguration<OptionHeadsOfCovers>
{
    public void Configure(EntityTypeBuilder<OptionHeadsOfCovers> builder)
    {
        builder.HasOne(x => x.HeadsOfCover)
            .WithMany(x => x.Options)
            .HasForeignKey(x => x.HeadsOfCoverId)
            .IsRequired()
            .OnDelete(DeleteBehavior.NoAction);
    }
}