using Theia.Domain.Entities.Indications;

namespace Theia.Infrastructure.Persistence.Configurations.Indications;

public class QuotaShareFollowerConfiguration : IEntityTypeConfiguration<QuotaShareFollower>
{
    public void Configure(EntityTypeBuilder<QuotaShareFollower> builder)
    {
        builder.HasOne(x => x.QuotaShare)
            .WithMany(x => x.Followers)
            .HasForeignKey(x => x.QuotaShareId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(x => x.Insurer)
            .WithMany(i => i.QuotaShares)
            .HasForeignKey(x => x.InsurerId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(x => x.Status)
            .IsRequired()
            .HasConversion<int>();
    }
}
