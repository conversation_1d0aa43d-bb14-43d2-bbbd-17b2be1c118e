<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup Label="Globals">
        <SccProjectName>SAK</SccProjectName>
        <SccProvider>SAK</SccProvider>
        <SccAuxPath>SAK</SccAuxPath>
        <SccLocalPath>SAK</SccLocalPath>
        <PackageId>Theia.Infrastructure</PackageId>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>12</LangVersion>
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="EntityFrameworkCore.Extensions" Version="5.0.0" />
        <PackageReference Include="Hangfire" Version="1.8.17" />
        <PackageReference Include="Hangfire.Core" Version="1.8.17" />
        <PackageReference Include="Hangfire.SqlServer" Version="1.8.17" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.12" />
        <PackageReference Include="Microsoft.Data.SqlClient.AlwaysEncrypted.AzureKeyVaultProvider" Version="5.1.0" />

        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
        <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.12" />

        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
        <PackageReference Include="Stubble.Core" Version="1.10.8" />
        <PackageReference Include="Theia.Http.Services" Version="1.0.4" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Theia.App.Jobs\Theia.App.Jobs.csproj" />
        <ProjectReference Include="..\Theia.App.Shared.Broking\Theia.App.Shared.Broking.csproj" />
        <ProjectReference Include="..\Theia.Application\Theia.Application.csproj" />
        <ProjectReference Include="..\Theia.Infrastructure.Common\Theia.Infrastructure.Common.csproj" />
    </ItemGroup>
</Project>
